<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Upgrade Required</h1>
                <p class="text-gray-600 mt-1">Unlock more features with a higher plan</p>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <?php
                $feature = request('feature', 'feature');
                $title = request('title', 'Upgrade Required');
                $description = request('description', 'This feature requires a higher plan.');
                $requiredPlan = request('plan', 'Pro');
                
                // Define feature-specific content
                $featureContent = [
                    'invoices_limit' => [
                        'title' => 'Monthly Invoice Limit Reached',
                        'description' => 'You\'ve reached your monthly limit of 3 invoices. Upgrade to Pro for unlimited invoices and professional features.',
                        'plan' => 'Pro',
                        'benefits' => [
                            'Unlimited invoices per month',
                            'Professional invoice templates',
                            'Custom branding options',
                            'TDS reports and management'
                        ]
                    ],
                    'tds_reports' => [
                        'title' => 'TDS Reports - Pro Feature',
                        'description' => 'TDS reports are available for Pro and Business plan users. Generate comprehensive TDS reports for tax compliance.',
                        'plan' => 'Pro',
                        'benefits' => [
                            'Generate TDS reports',
                            'Download in multiple formats',
                            'Financial year wise reports',
                            'Client-wise TDS tracking'
                        ]
                    ],
                    'contracts_limit' => [
                        'title' => 'Contract Limit Reached',
                        'description' => 'You\'ve reached your contract creation limit. Upgrade for unlimited contracts and advanced features.',
                        'plan' => 'Pro',
                        'benefits' => [
                            'Unlimited contracts',
                            'Advanced contract templates',
                            'Digital signatures',
                            'Contract analytics'
                        ]
                    ],
                    'ai_assistant' => [
                        'title' => 'AI Document Assistant - Business Feature',
                        'description' => 'Get AI-powered assistance for creating and managing your documents. Available in Business plan.',
                        'plan' => 'Business',
                        'benefits' => [
                            'AI-powered document creation',
                            'Smart content suggestions',
                            'Automated document review',
                            'Advanced document analytics'
                        ]
                    ],
                    'multi_client_dashboard' => [
                        'title' => 'Multi-Client Dashboard - Business Feature',
                        'description' => 'Manage multiple clients efficiently with advanced dashboard features. Available in Business plan.',
                        'plan' => 'Business',
                        'benefits' => [
                            'Advanced client management',
                            'Multi-client overview',
                            'Client performance analytics',
                            'Bulk operations'
                        ]
                    ],
                    'portfolio_generator' => [
                        'title' => 'Portfolio Generator - Business Feature',
                        'description' => 'Create professional portfolios automatically from your work history. Available in Business plan.',
                        'plan' => 'Business',
                        'benefits' => [
                            'Automated portfolio generation',
                            'Professional templates',
                            'Client testimonials integration',
                            'Portfolio analytics'
                        ]
                    ],
                    'custom_branding' => [
                        'title' => 'Custom Branding - Pro Feature',
                        'description' => 'Add your own branding to invoices and documents. Remove Freeligo branding completely.',
                        'plan' => 'Pro',
                        'benefits' => [
                            'Custom logo on documents',
                            'Remove Freeligo branding',
                            'Custom color schemes',
                            'Professional appearance'
                        ]
                    ]
                ];
                
                $content = $featureContent[$feature] ?? [
                    'title' => $title,
                    'description' => $description,
                    'plan' => $requiredPlan,
                    'benefits' => []
                ];
            ?>

            <?php if (isset($component)) { $__componentOriginal19d92fe0662932debb615994db3694d7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal19d92fe0662932debb615994db3694d7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.upgrade-prompt','data' => ['feature' => $feature,'title' => $content['title'],'description' => $content['description'],'requiredPlan' => $content['plan'],'benefits' => $content['benefits'],'showComparison' => true,'size' => 'full']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('upgrade-prompt'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['feature' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($feature),'title' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($content['title']),'description' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($content['description']),'required-plan' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($content['plan']),'benefits' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute($content['benefits']),'show-comparison' => true,'size' => 'full']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal19d92fe0662932debb615994db3694d7)): ?>
<?php $attributes = $__attributesOriginal19d92fe0662932debb615994db3694d7; ?>
<?php unset($__attributesOriginal19d92fe0662932debb615994db3694d7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal19d92fe0662932debb615994db3694d7)): ?>
<?php $component = $__componentOriginal19d92fe0662932debb615994db3694d7; ?>
<?php unset($__componentOriginal19d92fe0662932debb615994db3694d7); ?>
<?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\freeligo\resources\views/upgrade/required.blade.php ENDPATH**/ ?>