<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WorkflowTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'category',
        'trigger_type',
        'default_conditions',
        'default_actions',
        'required_variables',
        'optional_variables',
        'is_system_template',
        'is_active',
        'usage_count',
        'average_rating',
        'tags',
        'setup_instructions',
        'preview_data',
    ];

    protected function casts(): array
    {
        return [
            'default_conditions' => 'array',
            'default_actions' => 'array',
            'required_variables' => 'array',
            'optional_variables' => 'array',
            'is_system_template' => 'boolean',
            'is_active' => 'boolean',
            'usage_count' => 'integer',
            'average_rating' => 'decimal:2',
            'tags' => 'array',
            'preview_data' => 'array',
        ];
    }

    /**
     * Get workflows created from this template.
     */
    public function workflows(): HasMany
    {
        return $this->hasMany(Workflow::class, 'template_id');
    }

    /**
     * Increment usage count.
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    /**
     * Update average rating.
     */
    public function updateRating(float $newRating): void
    {
        $currentRating = $this->average_rating ?? 0;
        $totalRatings = $this->usage_count;
        
        if ($totalRatings > 0) {
            $newAverage = (($currentRating * $totalRatings) + $newRating) / ($totalRatings + 1);
            $this->update(['average_rating' => round($newAverage, 2)]);
        } else {
            $this->update(['average_rating' => $newRating]);
        }
    }

    /**
     * Create workflow from template.
     */
    public function createWorkflow(int $userId, array $customizations = []): Workflow
    {
        $workflowData = [
            'user_id' => $userId,
            'name' => $customizations['name'] ?? $this->name,
            'description' => $customizations['description'] ?? $this->description,
            'trigger_type' => $this->trigger_type,
            'trigger_conditions' => $customizations['conditions'] ?? $this->default_conditions,
            'actions' => $customizations['actions'] ?? $this->default_actions,
            'category' => 'template',
            'template_id' => $this->id,
            'custom_variables' => $customizations['variables'] ?? [],
            'ai_optimization_enabled' => $customizations['ai_optimization'] ?? false,
        ];

        $workflow = Workflow::create($workflowData);
        $this->incrementUsage();

        return $workflow;
    }

    /**
     * Get template variables for form generation.
     */
    public function getVariablesForForm(): array
    {
        $variables = [];
        
        if ($this->required_variables) {
            foreach ($this->required_variables as $variable) {
                $variables[] = array_merge($variable, ['required' => true]);
            }
        }
        
        if ($this->optional_variables) {
            foreach ($this->optional_variables as $variable) {
                $variables[] = array_merge($variable, ['required' => false]);
            }
        }
        
        return $variables;
    }

    /**
     * Validate template configuration.
     */
    public function validateConfiguration(array $config): array
    {
        $errors = [];
        
        // Check required variables
        if ($this->required_variables) {
            foreach ($this->required_variables as $variable) {
                $name = $variable['name'];
                if (!isset($config[$name]) || empty($config[$name])) {
                    $errors[] = "Required variable '{$name}' is missing";
                }
            }
        }
        
        // Validate variable types
        foreach ($config as $name => $value) {
            $variableConfig = $this->getVariableConfig($name);
            if ($variableConfig && !$this->validateVariableType($value, $variableConfig['type'])) {
                $errors[] = "Variable '{$name}' has invalid type";
            }
        }
        
        return $errors;
    }

    /**
     * Get variable configuration by name.
     */
    protected function getVariableConfig(string $name): ?array
    {
        $allVariables = array_merge(
            $this->required_variables ?? [],
            $this->optional_variables ?? []
        );
        
        foreach ($allVariables as $variable) {
            if ($variable['name'] === $name) {
                return $variable;
            }
        }
        
        return null;
    }

    /**
     * Validate variable type.
     */
    protected function validateVariableType($value, string $type): bool
    {
        return match ($type) {
            'string' => is_string($value),
            'number' => is_numeric($value),
            'boolean' => is_bool($value),
            'array' => is_array($value),
            'email' => filter_var($value, FILTER_VALIDATE_EMAIL) !== false,
            'url' => filter_var($value, FILTER_VALIDATE_URL) !== false,
            default => true,
        };
    }

    /**
     * Scope for system templates.
     */
    public function scopeSystem($query)
    {
        return $query->where('is_system_template', true);
    }

    /**
     * Scope for user templates.
     */
    public function scopeUser($query)
    {
        return $query->where('is_system_template', false);
    }

    /**
     * Scope for active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for templates by category.
     */
    public function scopeByCategory($query, string $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope for templates by trigger type.
     */
    public function scopeByTrigger($query, string $triggerType)
    {
        return $query->where('trigger_type', $triggerType);
    }

    /**
     * Scope for popular templates.
     */
    public function scopePopular($query, int $minUsage = 10)
    {
        return $query->where('usage_count', '>=', $minUsage)
                    ->orderBy('usage_count', 'desc');
    }

    /**
     * Scope for highly rated templates.
     */
    public function scopeHighlyRated($query, float $minRating = 4.0)
    {
        return $query->where('average_rating', '>=', $minRating)
                    ->orderBy('average_rating', 'desc');
    }

    /**
     * Search templates by tags.
     */
    public function scopeWithTags($query, array $tags)
    {
        foreach ($tags as $tag) {
            $query->whereJsonContains('tags', $tag);
        }
        
        return $query;
    }
}
