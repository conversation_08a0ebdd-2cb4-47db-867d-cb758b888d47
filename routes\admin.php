<?php

use App\Http\Controllers\Admin\AdminAuthController;
use App\Http\Controllers\Admin\AdminDashboardController;
use App\Http\Controllers\Admin\PlanController;
use App\Http\Controllers\Admin\UserManagementController;
use Illuminate\Support\Facades\Route;

// Admin Authentication Routes (Guest only)
Route::middleware('guest')->prefix('admin')->name('admin.')->group(function () {
    Route::get('login', [AdminAuthController::class, 'showLoginForm'])->name('login');
    Route::post('login', [AdminAuthController::class, 'login']);
});

// Admin Protected Routes
Route::middleware('admin')->prefix('admin')->name('admin.')->group(function () {
    // Admin Dashboard
    Route::get('/', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard', [AdminDashboardController::class, 'index']);
    Route::get('/dashboard/data', [AdminDashboardController::class, 'getData'])->name('dashboard.data');
    
    // User Management
    Route::resource('users', UserManagementController::class);

    // Plan Management
    Route::resource('plans', PlanController::class);
    Route::patch('plans/{plan}/toggle-status', [PlanController::class, 'toggleStatus'])->name('plans.toggle-status');

    // Automation Monitoring
    Route::prefix('automation')->name('automation.')->group(function () {
        Route::get('/dashboard', [App\Http\Controllers\Admin\AutomationMonitoringController::class, 'dashboard'])->name('dashboard');
        Route::get('/queue-data', [App\Http\Controllers\Admin\AutomationMonitoringController::class, 'getQueueData'])->name('queue-data');
        Route::post('/retry-failed-jobs', [App\Http\Controllers\Admin\AutomationMonitoringController::class, 'retryFailedJobs'])->name('retry-failed-jobs');
        Route::post('/clear-failed-jobs', [App\Http\Controllers\Admin\AutomationMonitoringController::class, 'clearFailedJobs'])->name('clear-failed-jobs');
        Route::get('/logs', [App\Http\Controllers\Admin\AutomationMonitoringController::class, 'getLogs'])->name('logs');
    });

    // Admin Logout
    Route::post('logout', [AdminAuthController::class, 'logout'])->name('logout');
});
