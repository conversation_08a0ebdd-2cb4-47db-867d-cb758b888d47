<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('TDS Record Details') }}
            </h2>
            <div class="flex items-center space-x-3">
                @if(!$tdsRecord->certificate_number)
                    <x-ui.button type="button" onclick="updateCertificate({{ $tdsRecord->id }})"
                               variant="success" icon="fas fa-certificate">
                        Add Certificate
                    </x-ui.button>
                @endif
                <x-ui.button href="{{ route('invoices.show', $tdsRecord->invoice) }}"
                           variant="primary" icon="fas fa-file-invoice">
                    View Invoice
                </x-ui.button>
                <x-ui.button href="{{ route('tds.index') }}"
                           variant="secondary" icon="fas fa-arrow-left">
                    Back to TDS Records
                </x-ui.button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- TDS Record Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- TDS Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">TDS Information</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">TDS Amount</label>
                                    <p class="mt-1 text-lg font-bold text-gray-900">₹{{ number_format($tdsRecord->tds_amount, 2) }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">TDS Percentage</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->tds_percentage }}%</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Financial Year</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->financial_year }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Deduction Date</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->deduction_date->format('d/m/Y') }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Certificate Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Certificate Information</h3>
                            <div class="space-y-3">
                                @if($tdsRecord->certificate_number)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Certificate Number</label>
                                        <p class="mt-1 text-sm font-medium text-gray-900">{{ $tdsRecord->certificate_number }}</p>
                                    </div>
                                    @if($tdsRecord->certificate_date)
                                        <div>
                                            <label class="block text-sm font-medium text-gray-500">Certificate Date</label>
                                            <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->certificate_date->format('d/m/Y') }}</p>
                                        </div>
                                    @endif
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Status</label>
                                        <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Certificate Available
                                        </span>
                                    </div>
                                @else
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Status</label>
                                        <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                            Certificate Pending
                                        </span>
                                    </div>
                                    <div class="mt-4">
                                        <button type="button" onclick="updateCertificate({{ $tdsRecord->id }})" 
                                                class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                            Add Certificate Details
                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Invoice -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Related Invoice</h3>
                    <div class="border border-gray-200 rounded-lg p-4">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Invoice Number</label>
                                <p class="mt-1 text-sm font-medium text-gray-900">{{ $tdsRecord->invoice->invoice_number }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Invoice Date</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->invoice->invoice_date->format('d/m/Y') }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Total Amount</label>
                                <p class="mt-1 text-sm text-gray-900">₹{{ number_format($tdsRecord->invoice->total_amount, 2) }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Net Amount (After TDS)</label>
                                <p class="mt-1 text-sm text-gray-900">₹{{ number_format($tdsRecord->invoice->net_amount, 2) }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Status</label>
                                <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                    @if($tdsRecord->invoice->status === 'paid') bg-green-100 text-green-800
                                    @elseif($tdsRecord->invoice->status === 'sent') bg-blue-100 text-blue-800
                                    @else bg-gray-100 text-gray-800 @endif">
                                    {{ ucfirst($tdsRecord->invoice->status) }}
                                </span>
                            </div>
                            <div class="flex items-end">
                                <a href="{{ route('invoices.show', $tdsRecord->invoice) }}" 
                                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                                    View Invoice
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Client Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Client Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Name</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->client->name }}</p>
                                </div>
                                @if($tdsRecord->client->company_name)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Company</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->client->company_name }}</p>
                                    </div>
                                @endif
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Email</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->client->email }}</p>
                                </div>
                                @if($tdsRecord->client->phone)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Phone</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->client->phone }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Type</label>
                                    <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        {{ $tdsRecord->client->type === 'company' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                                        {{ ucfirst($tdsRecord->client->type) }}
                                    </span>
                                </div>
                                @if($tdsRecord->client->pan_number)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">PAN Number</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->client->pan_number }}</p>
                                    </div>
                                @endif
                                @if($tdsRecord->client->gst_number)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">GST Number</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->client->gst_number }}</p>
                                    </div>
                                @endif
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Default TDS %</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $tdsRecord->client->default_tds_percentage }}%</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TDS Summary for this Client -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">TDS Summary for {{ $tdsRecord->client->name }}</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div class="bg-blue-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-blue-600">Total TDS (All Years)</div>
                            <div class="text-2xl font-bold text-blue-900">₹{{ number_format($clientTotalTds, 2) }}</div>
                        </div>
                        <div class="bg-green-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-green-600">Current Financial Year</div>
                            <div class="text-2xl font-bold text-green-900">₹{{ number_format($clientCurrentYearTds, 2) }}</div>
                        </div>
                        <div class="bg-yellow-50 p-4 rounded-lg">
                            <div class="text-sm font-medium text-yellow-600">Total Invoices</div>
                            <div class="text-2xl font-bold text-yellow-900">{{ $clientInvoiceCount }}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Certificate Update Modal -->
    <div id="certificateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Update TDS Certificate</h3>
                <form id="certificateForm" method="POST">
                    @csrf
                    @method('PATCH')
                    <div class="mb-4">
                        <label for="certificate_number" class="block text-sm font-medium text-gray-700">Certificate Number</label>
                        <input type="text" name="certificate_number" id="certificate_number" required
                               value="{{ $tdsRecord->certificate_number }}"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="mb-4">
                        <label for="certificate_date" class="block text-sm font-medium text-gray-700">Certificate Date</label>
                        <input type="date" name="certificate_date" id="certificate_date" required
                               value="{{ $tdsRecord->certificate_date ? $tdsRecord->certificate_date->format('Y-m-d') : '' }}"
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeCertificateModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Cancel
                        </button>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Update Certificate
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function updateCertificate(recordId) {
            document.getElementById('certificateForm').action = `/tds/${recordId}/certificate`;
            document.getElementById('certificateModal').classList.remove('hidden');
        }

        function closeCertificateModal() {
            document.getElementById('certificateModal').classList.add('hidden');
        }

        // Close modal when clicking outside
        document.getElementById('certificateModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCertificateModal();
            }
        });
    </script>
</x-app-layout>
