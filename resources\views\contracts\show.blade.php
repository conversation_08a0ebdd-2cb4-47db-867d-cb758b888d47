<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Contract: ') . $contract->title }}
            </h2>
            <div class="flex space-x-2">
                @if($contract->status !== 'signed')
                    <a href="{{ route('contracts.edit', $contract) }}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                        Edit Contract
                    </a>
                @endif
                <a href="{{ route('contracts.download', $contract) }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Download PDF
                </a>
                @if($contract->status === 'draft')
                    <form method="POST" action="{{ route('contracts.send', $contract) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" 
                                onclick="return confirm('Send this contract to client?')">
                            Send to Client
                        </button>
                    </form>
                @endif
                @if($contract->status === 'sent')
                    <form method="POST" action="{{ route('contracts.mark-signed', $contract) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded" 
                                onclick="return confirm('Mark this contract as signed?')">
                            Mark as Signed
                        </button>
                    </form>
                @endif
                <a href="{{ route('contracts.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Contracts
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Contract Header -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Contract Info -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Contract Details</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Title</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $contract->title }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Template</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $contract->contractTemplate->name }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Status</label>
                                    <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        @if($contract->status === 'signed') bg-green-100 text-green-800
                                        @elseif($contract->status === 'sent') bg-blue-100 text-blue-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($contract->status) }}
                                    </span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Created Date</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $contract->created_at->format('d/m/Y H:i') }}</p>
                                </div>
                                @if($contract->sent_date)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Sent Date</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $contract->sent_date->format('d/m/Y H:i') }}</p>
                                    </div>
                                @endif
                                @if($contract->signed_date)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Signed Date</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $contract->signed_date->format('d/m/Y H:i') }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Client Info -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Client Details</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Name</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $contract->client->name }}</p>
                                </div>
                                @if($contract->client->company_name)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Company</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $contract->client->company_name }}</p>
                                    </div>
                                @endif
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Email</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $contract->client->email }}</p>
                                </div>
                                @if($contract->client->phone)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Phone</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $contract->client->phone }}</p>
                                    </div>
                                @endif
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Type</label>
                                    <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        {{ $contract->client->type === 'company' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                                        {{ ucfirst($contract->client->type) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contract Variables -->
            @if($contract->variables && count($contract->variables) > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Contract Variables</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            @foreach($contract->variables as $key => $value)
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">{{ ucwords(str_replace('_', ' ', $key)) }}</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $value }}</p>
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif

            <!-- Contract Content -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Contract Content</h3>
                    <div class="bg-gray-50 p-6 rounded-lg border">
                        <div class="prose max-w-none">
                            <div class="text-sm whitespace-pre-line">{{ $contract->content }}</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Invoices -->
            @if($contract->client->invoices->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-medium text-gray-900">Related Invoices</h3>
                            <a href="{{ route('invoices.index', ['client_id' => $contract->client->id]) }}" class="text-sm text-blue-600 hover:text-blue-500">View all</a>
                        </div>
                        
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoice</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($contract->client->invoices->take(5) as $invoice)
                                        <tr>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{ $invoice->invoice_number }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                                {{ $invoice->invoice_date->format('d/m/Y') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                ₹{{ number_format($invoice->total_amount, 2) }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                    @if($invoice->status === 'paid') bg-green-100 text-green-800
                                                    @elseif($invoice->status === 'pending') bg-yellow-100 text-yellow-800
                                                    @else bg-gray-100 text-gray-800 @endif">
                                                    {{ ucfirst($invoice->status) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <a href="{{ route('invoices.show', $invoice) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
