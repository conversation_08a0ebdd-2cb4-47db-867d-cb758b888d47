<?php

namespace Database\Factories;

use App\Models\Invoice;
use App\Models\User;
use App\Models\Client;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

class InvoiceFactory extends Factory
{
    protected $model = Invoice::class;

    public function definition(): array
    {
        $subtotal = fake()->randomFloat(2, 1000, 50000);
        $taxPercentage = fake()->randomElement([0, 18, 28]);
        $taxAmount = ($subtotal * $taxPercentage) / 100;
        $totalAmount = $subtotal + $taxAmount;
        $tdsPercentage = fake()->randomElement([0, 2, 10]);
        $tdsAmount = ($subtotal * $tdsPercentage) / 100;
        $netAmount = $totalAmount - $tdsAmount;

        return [
            'user_id' => User::factory(),
            'client_id' => Client::factory(),
            'invoice_number' => 'INV-' . fake()->unique()->numberBetween(1000, 9999),
            'invoice_date' => fake()->dateTimeBetween('-6 months', 'now'),
            'due_date' => fake()->dateTimeBetween('now', '+30 days'),
            'subtotal' => $subtotal,
            'tax_percentage' => $taxPercentage,
            'tax_amount' => $taxAmount,
            'tds_percentage' => $tdsPercentage,
            'tds_amount' => $tdsAmount,
            'total_amount' => $totalAmount,
            'net_amount' => $netAmount,
            'status' => fake()->randomElement(['draft', 'sent', 'paid', 'overdue']),
            'paid_date' => null,
            'notes' => fake()->optional()->sentence(),
            'pdf_path' => null,
        ];
    }

    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
            'paid_date' => null,
        ]);
    }

    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'sent',
            'paid_date' => null,
        ]);
    }

    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'paid',
            'paid_date' => fake()->dateTimeBetween($attributes['invoice_date'], 'now'),
        ]);
    }

    public function overdue(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'overdue',
            'due_date' => fake()->dateTimeBetween('-30 days', '-1 day'),
            'paid_date' => null,
        ]);
    }

    public function withoutTax(): static
    {
        return $this->state(function (array $attributes) {
            $subtotal = $attributes['subtotal'];
            return [
                'tax_percentage' => 0,
                'tax_amount' => 0,
                'total_amount' => $subtotal,
                'net_amount' => $subtotal - $attributes['tds_amount'],
            ];
        });
    }

    public function withoutTds(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'tds_percentage' => 0,
                'tds_amount' => 0,
                'net_amount' => $attributes['total_amount'],
            ];
        });
    }

    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    public function forClient(Client $client): static
    {
        return $this->state(fn (array $attributes) => [
            'client_id' => $client->id,
            'user_id' => $client->user_id,
        ]);
    }
}
