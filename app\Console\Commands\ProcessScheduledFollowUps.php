<?php

namespace App\Console\Commands;

use App\Services\NotificationService;
use Illuminate\Console\Command;

class ProcessScheduledFollowUps extends Command
{
    protected $signature = 'followups:process-scheduled';
    protected $description = 'Process scheduled follow-ups and send notifications';

    protected $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    public function handle(): int
    {
        $this->info('Processing scheduled follow-ups...');

        $results = $this->notificationService->processScheduledFollowUps();

        $this->info("Processed {$results['processed']} follow-ups successfully.");
        
        if ($results['failed'] > 0) {
            $this->warn("Failed to process {$results['failed']} follow-ups.");
            foreach ($results['errors'] as $error) {
                $this->error("Follow-up ID {$error['followup_id']}: {$error['error']}");
            }
        }

        return Command::SUCCESS;
    }
}
