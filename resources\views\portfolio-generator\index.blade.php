<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Portfolio Generator</h1>
                <p class="text-gray-600 mt-1">Create professional portfolios from your project data</p>
            </div>
            <div class="flex items-center space-x-2">
                <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    Business Plan Feature
                </span>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Portfolio Stats -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="bg-blue-100 p-3 rounded-lg">
                            <i class="fas fa-project-diagram text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Completed Projects</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $portfolioData['total_projects'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="bg-green-100 p-3 rounded-lg">
                            <i class="fas fa-dollar-sign text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                            <p class="text-2xl font-bold text-gray-900">₹{{ number_format($portfolioData['total_revenue']) }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="bg-purple-100 p-3 rounded-lg">
                            <i class="fas fa-users text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Happy Clients</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $portfolioData['client_count'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="bg-yellow-100 p-3 rounded-lg">
                            <i class="fas fa-chart-line text-yellow-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Avg Project Value</p>
                            <p class="text-2xl font-bold text-gray-900">₹{{ number_format($portfolioData['avg_project_value']) }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Portfolio Generator -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Generate Portfolio</h3>
                    </div>
                    <div class="p-6">
                        <form id="portfolioForm">
                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">Choose Template</label>
                                <div class="grid grid-cols-2 gap-3">
                                    <label class="cursor-pointer">
                                        <input type="radio" name="template" value="modern" class="sr-only" checked>
                                        <div class="border-2 border-gray-200 rounded-lg p-4 hover:border-blue-500 transition-colors template-option">
                                            <div class="text-center">
                                                <i class="fas fa-laptop text-2xl text-gray-600 mb-2"></i>
                                                <p class="text-sm font-medium">Modern</p>
                                            </div>
                                        </div>
                                    </label>
                                    <label class="cursor-pointer">
                                        <input type="radio" name="template" value="classic" class="sr-only">
                                        <div class="border-2 border-gray-200 rounded-lg p-4 hover:border-blue-500 transition-colors template-option">
                                            <div class="text-center">
                                                <i class="fas fa-file-alt text-2xl text-gray-600 mb-2"></i>
                                                <p class="text-sm font-medium">Classic</p>
                                            </div>
                                        </div>
                                    </label>
                                    <label class="cursor-pointer">
                                        <input type="radio" name="template" value="creative" class="sr-only">
                                        <div class="border-2 border-gray-200 rounded-lg p-4 hover:border-blue-500 transition-colors template-option">
                                            <div class="text-center">
                                                <i class="fas fa-palette text-2xl text-gray-600 mb-2"></i>
                                                <p class="text-sm font-medium">Creative</p>
                                            </div>
                                        </div>
                                    </label>
                                    <label class="cursor-pointer">
                                        <input type="radio" name="template" value="minimal" class="sr-only">
                                        <div class="border-2 border-gray-200 rounded-lg p-4 hover:border-blue-500 transition-colors template-option">
                                            <div class="text-center">
                                                <i class="fas fa-circle text-2xl text-gray-600 mb-2"></i>
                                                <p class="text-sm font-medium">Minimal</p>
                                            </div>
                                        </div>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-3">Include Sections</label>
                                <div class="space-y-2">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="sections[]" value="about" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">About Me</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="sections[]" value="skills" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">Skills & Expertise</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="sections[]" value="projects" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">Recent Projects</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="sections[]" value="testimonials" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">Client Testimonials</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="sections[]" value="contact" checked class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <span class="ml-2 text-sm text-gray-700">Contact Information</span>
                                    </label>
                                </div>
                            </div>

                            <div class="mb-6">
                                <label class="block text-sm font-medium text-gray-700 mb-2">Style</label>
                                <select name="style" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                    <option value="professional">Professional</option>
                                    <option value="creative">Creative</option>
                                    <option value="technical">Technical</option>
                                    <option value="corporate">Corporate</option>
                                </select>
                            </div>

                            <div class="flex space-x-3">
                                <button type="submit" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-magic mr-2"></i>Generate Portfolio
                                </button>
                                <button type="button" onclick="previewPortfolio()" class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-eye mr-2"></i>Preview
                                </button>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Recent Projects -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Projects</h3>
                    </div>
                    <div class="p-6">
                        @if($portfolioData['recent_projects']->count() > 0)
                            <div class="space-y-4">
                                @foreach($portfolioData['recent_projects'] as $project)
                                    <div class="border border-gray-200 rounded-lg p-4">
                                        <h4 class="font-medium text-gray-900">{{ $project['title'] }}</h4>
                                        <p class="text-sm text-gray-600 mt-1">Client: {{ $project['client'] }}</p>
                                        <p class="text-xs text-gray-500 mt-2">{{ $project['description'] }}</p>
                                        @if($project['completed_date'])
                                            <p class="text-xs text-gray-500 mt-1">Completed: {{ \Carbon\Carbon::parse($project['completed_date'])->format('M Y') }}</p>
                                        @endif
                                    </div>
                                @endforeach
                            </div>
                        @else
                            <div class="text-center py-8">
                                <i class="fas fa-project-diagram text-gray-400 text-3xl mb-4"></i>
                                <p class="text-gray-600">No completed projects yet.</p>
                                <p class="text-sm text-gray-500 mt-1">Complete some contracts to showcase your work!</p>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Template selection
        document.querySelectorAll('input[name="template"]').forEach(radio => {
            radio.addEventListener('change', function() {
                document.querySelectorAll('.template-option').forEach(option => {
                    option.classList.remove('border-blue-500', 'bg-blue-50');
                    option.classList.add('border-gray-200');
                });
                
                if (this.checked) {
                    const option = this.nextElementSibling;
                    option.classList.remove('border-gray-200');
                    option.classList.add('border-blue-500', 'bg-blue-50');
                }
            });
        });

        // Initialize first template as selected
        document.querySelector('input[name="template"]:checked').dispatchEvent(new Event('change'));

        // Portfolio generation
        document.getElementById('portfolioForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            const sections = formData.getAll('sections[]');
            
            fetch('{{ route("portfolio-generator.generate") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    template: formData.get('template'),
                    sections: sections,
                    style: formData.get('style')
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.preview_url) {
                    window.open(data.preview_url, '_blank');
                } else {
                    alert('Error generating portfolio');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error generating portfolio');
            });
        });

        function previewPortfolio() {
            window.open('{{ route("portfolio-generator.preview") }}', '_blank');
        }
    </script>
</x-app-layout>
