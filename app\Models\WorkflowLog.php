<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkflowLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'workflow_execution_id',
        'workflow_step_id',
        'log_level',
        'event_type',
        'message',
        'context_data',
        'execution_time_ms',
        'logged_at',
    ];

    protected function casts(): array
    {
        return [
            'context_data' => 'array',
            'execution_time_ms' => 'decimal:3',
            'logged_at' => 'datetime',
        ];
    }

    /**
     * Get the workflow execution that owns the log.
     */
    public function workflowExecution(): BelongsTo
    {
        return $this->belongsTo(WorkflowExecution::class);
    }

    /**
     * Get the workflow step that owns the log.
     */
    public function workflowStep(): BelongsTo
    {
        return $this->belongsTo(WorkflowStep::class);
    }

    /**
     * Create an info log entry.
     */
    public static function info(int $executionId, string $eventType, string $message, array $context = [], ?int $stepId = null): self
    {
        return static::createLog($executionId, 'info', $eventType, $message, $context, $stepId);
    }

    /**
     * Create a warning log entry.
     */
    public static function warning(int $executionId, string $eventType, string $message, array $context = [], ?int $stepId = null): self
    {
        return static::createLog($executionId, 'warning', $eventType, $message, $context, $stepId);
    }

    /**
     * Create an error log entry.
     */
    public static function error(int $executionId, string $eventType, string $message, array $context = [], ?int $stepId = null): self
    {
        return static::createLog($executionId, 'error', $eventType, $message, $context, $stepId);
    }

    /**
     * Create a debug log entry.
     */
    public static function debug(int $executionId, string $eventType, string $message, array $context = [], ?int $stepId = null): self
    {
        return static::createLog($executionId, 'debug', $eventType, $message, $context, $stepId);
    }

    /**
     * Create a log entry with execution time.
     */
    public static function timed(int $executionId, string $eventType, string $message, float $executionTimeMs, array $context = [], ?int $stepId = null): self
    {
        return static::createLog($executionId, 'info', $eventType, $message, $context, $stepId, $executionTimeMs);
    }

    /**
     * Create a log entry.
     */
    protected static function createLog(int $executionId, string $logLevel, string $eventType, string $message, array $context = [], ?int $stepId = null, ?float $executionTimeMs = null): self
    {
        return static::create([
            'workflow_execution_id' => $executionId,
            'workflow_step_id' => $stepId,
            'log_level' => $logLevel,
            'event_type' => $eventType,
            'message' => $message,
            'context_data' => $context,
            'execution_time_ms' => $executionTimeMs,
            'logged_at' => now(),
        ]);
    }

    /**
     * Get execution timeline for a workflow execution.
     */
    public static function getExecutionTimeline(int $executionId): array
    {
        $logs = static::where('workflow_execution_id', $executionId)
            ->orderBy('logged_at')
            ->get();

        $timeline = [];
        $startTime = null;

        foreach ($logs as $log) {
            if (!$startTime) {
                $startTime = $log->logged_at;
            }

            $timeline[] = [
                'timestamp' => $log->logged_at->toISOString(),
                'relative_time' => $log->logged_at->diffInMilliseconds($startTime),
                'log_level' => $log->log_level,
                'event_type' => $log->event_type,
                'message' => $log->message,
                'step_id' => $log->workflow_step_id,
                'execution_time_ms' => $log->execution_time_ms,
                'context' => $log->context_data,
            ];
        }

        return $timeline;
    }

    /**
     * Get performance summary for a workflow execution.
     */
    public static function getPerformanceSummary(int $executionId): array
    {
        $logs = static::where('workflow_execution_id', $executionId)
            ->whereNotNull('execution_time_ms')
            ->get();

        if ($logs->isEmpty()) {
            return [
                'total_steps' => 0,
                'total_execution_time' => 0,
                'average_step_time' => 0,
                'slowest_step' => null,
                'fastest_step' => null,
            ];
        }

        $totalTime = $logs->sum('execution_time_ms');
        $averageTime = $logs->avg('execution_time_ms');
        $slowestStep = $logs->sortByDesc('execution_time_ms')->first();
        $fastestStep = $logs->sortBy('execution_time_ms')->first();

        return [
            'total_steps' => $logs->count(),
            'total_execution_time' => round($totalTime, 3),
            'average_step_time' => round($averageTime, 3),
            'slowest_step' => [
                'step_id' => $slowestStep->workflow_step_id,
                'event_type' => $slowestStep->event_type,
                'execution_time' => $slowestStep->execution_time_ms,
                'message' => $slowestStep->message,
            ],
            'fastest_step' => [
                'step_id' => $fastestStep->workflow_step_id,
                'event_type' => $fastestStep->event_type,
                'execution_time' => $fastestStep->execution_time_ms,
                'message' => $fastestStep->message,
            ],
        ];
    }

    /**
     * Get error analysis for a workflow execution.
     */
    public static function getErrorAnalysis(int $executionId): array
    {
        $errorLogs = static::where('workflow_execution_id', $executionId)
            ->where('log_level', 'error')
            ->get();

        $warningLogs = static::where('workflow_execution_id', $executionId)
            ->where('log_level', 'warning')
            ->get();

        $errorsByStep = $errorLogs->groupBy('workflow_step_id');
        $errorsByType = $errorLogs->groupBy('event_type');

        return [
            'total_errors' => $errorLogs->count(),
            'total_warnings' => $warningLogs->count(),
            'errors_by_step' => $errorsByStep->map(function ($logs, $stepId) {
                return [
                    'step_id' => $stepId,
                    'error_count' => $logs->count(),
                    'errors' => $logs->map(function ($log) {
                        return [
                            'message' => $log->message,
                            'event_type' => $log->event_type,
                            'timestamp' => $log->logged_at->toISOString(),
                            'context' => $log->context_data,
                        ];
                    })->toArray(),
                ];
            })->values()->toArray(),
            'errors_by_type' => $errorsByType->map(function ($logs, $eventType) {
                return [
                    'event_type' => $eventType,
                    'error_count' => $logs->count(),
                    'first_occurrence' => $logs->min('logged_at'),
                    'last_occurrence' => $logs->max('logged_at'),
                ];
            })->values()->toArray(),
            'error_timeline' => $errorLogs->map(function ($log) {
                return [
                    'timestamp' => $log->logged_at->toISOString(),
                    'message' => $log->message,
                    'event_type' => $log->event_type,
                    'step_id' => $log->workflow_step_id,
                ];
            })->toArray(),
        ];
    }

    /**
     * Clean up old logs.
     */
    public static function cleanup(int $daysToKeep = 30): int
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        return static::where('logged_at', '<', $cutoffDate)->delete();
    }

    /**
     * Scope for logs by level.
     */
    public function scopeByLevel($query, string $level)
    {
        return $query->where('log_level', $level);
    }

    /**
     * Scope for error logs.
     */
    public function scopeErrors($query)
    {
        return $query->where('log_level', 'error');
    }

    /**
     * Scope for warning logs.
     */
    public function scopeWarnings($query)
    {
        return $query->where('log_level', 'warning');
    }

    /**
     * Scope for logs by event type.
     */
    public function scopeByEventType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope for recent logs.
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('logged_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for logs with execution time.
     */
    public function scopeWithExecutionTime($query)
    {
        return $query->whereNotNull('execution_time_ms');
    }

    /**
     * Scope for slow operations.
     */
    public function scopeSlowOperations($query, float $thresholdMs = 1000.0)
    {
        return $query->where('execution_time_ms', '>', $thresholdMs);
    }
}
