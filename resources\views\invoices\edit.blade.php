<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Invoice: ') . $invoice->invoice_number }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('invoices.show', $invoice) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    View Invoice
                </a>
                <a href="{{ route('invoices.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Invoices
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('invoices.update', $invoice) }}" 
                          x-data="invoiceForm()" 
                          x-init="init()">
                        @csrf
                        @method('PUT')

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Left Column -->
                            <div class="space-y-6">
                                <!-- Client Selection -->
                                <div>
                                    <label for="client_id" class="block text-sm font-medium text-gray-700">Client</label>
                                    <select name="client_id" id="client_id" required x-model="clientId" @change="updateClientTds()"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Select a client</option>
                                        @foreach($clients as $client)
                                            <option value="{{ $client->id }}" 
                                                    data-tds="{{ $client->default_tds_percentage }}"
                                                    {{ old('client_id', $invoice->client_id) == $client->id ? 'selected' : '' }}>
                                                {{ $client->name }}
                                                @if($client->company_name) - {{ $client->company_name }} @endif
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('client_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Invoice Date -->
                                <div>
                                    <label for="invoice_date" class="block text-sm font-medium text-gray-700">Invoice Date</label>
                                    <input type="date" name="invoice_date" id="invoice_date" 
                                           value="{{ old('invoice_date', $invoice->invoice_date->format('Y-m-d')) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    @error('invoice_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Due Date -->
                                <div>
                                    <label for="due_date" class="block text-sm font-medium text-gray-700">Due Date</label>
                                    <input type="date" name="due_date" id="due_date" 
                                           value="{{ old('due_date', $invoice->due_date->format('Y-m-d')) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    @error('due_date')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- TDS Percentage -->
                                <div>
                                    <label for="tds_percentage" class="block text-sm font-medium text-gray-700">TDS Percentage</label>
                                    <select name="tds_percentage" id="tds_percentage" x-model="tdsPercentage" @change="calculateTotals()"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="0" {{ old('tds_percentage', $invoice->tds_percentage) == '0' ? 'selected' : '' }}>0%</option>
                                        <option value="1" {{ old('tds_percentage', $invoice->tds_percentage) == '1' ? 'selected' : '' }}>1%</option>
                                        <option value="2" {{ old('tds_percentage', $invoice->tds_percentage) == '2' ? 'selected' : '' }}>2%</option>
                                        <option value="5" {{ old('tds_percentage', $invoice->tds_percentage) == '5' ? 'selected' : '' }}>5%</option>
                                        <option value="10" {{ old('tds_percentage', $invoice->tds_percentage) == '10' ? 'selected' : '' }}>10%</option>
                                    </select>
                                    @error('tds_percentage')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Notes -->
                                <div>
                                    <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                                    <textarea name="notes" id="notes" rows="3"
                                              class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('notes', $invoice->notes) }}</textarea>
                                    @error('notes')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>

                            <!-- Right Column - Invoice Items -->
                            <div>
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-medium text-gray-900">Invoice Items</h3>
                                    <button type="button" @click="addItem()" 
                                            class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded text-sm">
                                        Add Item
                                    </button>
                                </div>

                                <div class="space-y-4">
                                    <template x-for="(item, index) in items" :key="index">
                                        <div class="border border-gray-200 rounded-lg p-4">
                                            <div class="flex justify-between items-start mb-3">
                                                <h4 class="text-sm font-medium text-gray-700" x-text="'Item ' + (index + 1)"></h4>
                                                <button type="button" @click="removeItem(index)" 
                                                        class="text-red-600 hover:text-red-900 text-sm">
                                                    Remove
                                                </button>
                                            </div>

                                            <div class="space-y-3">
                                                <div>
                                                    <input type="text" :name="'items[' + index + '][description]'" 
                                                           x-model="item.description" placeholder="Item description" required
                                                           class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                                </div>

                                                <div class="grid grid-cols-3 gap-3">
                                                    <div>
                                                        <input type="number" :name="'items[' + index + '][quantity]'" 
                                                               x-model="item.quantity" @input="calculateItemTotal(index)" 
                                                               placeholder="Qty" min="1" step="1" required
                                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                                    </div>
                                                    <div>
                                                        <input type="number" :name="'items[' + index + '][rate]'" 
                                                               x-model="item.rate" @input="calculateItemTotal(index)" 
                                                               placeholder="Rate" min="0" step="0.01" required
                                                               class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                                    </div>
                                                    <div>
                                                        <input type="number" :name="'items[' + index + '][amount]'" 
                                                               x-model="item.amount" readonly
                                                               class="block w-full rounded-md border-gray-300 bg-gray-50 shadow-sm">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </template>
                                </div>

                                <!-- Totals -->
                                <div class="mt-6 bg-gray-50 p-4 rounded-lg">
                                    <div class="space-y-2">
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Subtotal:</span>
                                            <span class="text-sm font-medium" x-text="'₹' + subtotal.toFixed(2)"></span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Tax (18%):</span>
                                            <span class="text-sm font-medium" x-text="'₹' + taxAmount.toFixed(2)"></span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span class="text-sm text-gray-600">Total:</span>
                                            <span class="text-sm font-medium" x-text="'₹' + totalAmount.toFixed(2)"></span>
                                        </div>
                                        <div class="flex justify-between" x-show="tdsAmount > 0">
                                            <span class="text-sm text-gray-600">TDS (<span x-text="tdsPercentage"></span>%):</span>
                                            <span class="text-sm font-medium text-red-600" x-text="'₹' + tdsAmount.toFixed(2)"></span>
                                        </div>
                                        <div class="flex justify-between border-t pt-2">
                                            <span class="text-base font-medium text-gray-900">Net Amount:</span>
                                            <span class="text-base font-bold text-gray-900" x-text="'₹' + netAmount.toFixed(2)"></span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="mt-8 flex justify-end space-x-3">
                            <a href="{{ route('invoices.show', $invoice) }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" name="status" value="draft"
                                    class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Save as Draft
                            </button>
                            <button type="submit" name="status" value="sent"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Update & Send
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function invoiceForm() {
            return {
                clientId: '{{ old('client_id', $invoice->client_id) }}',
                tdsPercentage: {{ old('tds_percentage', $invoice->tds_percentage) ?? 0 }},
                items: @json(old('items', $invoice->items->toArray())),
                subtotal: 0,
                taxAmount: 0,
                totalAmount: 0,
                tdsAmount: 0,
                netAmount: 0,

                init() {
                    this.calculateTotals();
                },

                updateClientTds() {
                    const select = document.getElementById('client_id');
                    const selectedOption = select.options[select.selectedIndex];
                    if (selectedOption && selectedOption.dataset.tds) {
                        this.tdsPercentage = parseFloat(selectedOption.dataset.tds);
                        this.calculateTotals();
                    }
                },

                addItem() {
                    this.items.push({ description: '', quantity: 1, rate: 0, amount: 0 });
                },

                removeItem(index) {
                    if (this.items.length > 1) {
                        this.items.splice(index, 1);
                        this.calculateTotals();
                    }
                },

                calculateItemTotal(index) {
                    const item = this.items[index];
                    item.amount = parseFloat(item.quantity || 0) * parseFloat(item.rate || 0);
                    this.calculateTotals();
                },

                calculateTotals() {
                    this.subtotal = this.items.reduce((sum, item) => sum + parseFloat(item.amount || 0), 0);
                    this.taxAmount = this.subtotal * 0.18;
                    this.totalAmount = this.subtotal + this.taxAmount;
                    this.tdsAmount = this.totalAmount * (parseFloat(this.tdsPercentage) / 100);
                    this.netAmount = this.totalAmount - this.tdsAmount;
                }
            };
        }
    </script>
</x-app-layout>
