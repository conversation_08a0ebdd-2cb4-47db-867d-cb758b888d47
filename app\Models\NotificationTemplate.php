<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'name',
        'type',
        'channel',
        'subject_template',
        'body_template',
        'variables',
        'is_active',
        'is_default',
    ];

    protected function casts(): array
    {
        return [
            'variables' => 'array',
            'is_active' => 'boolean',
            'is_default' => 'boolean',
        ];
    }

    /**
     * Get the user that owns the template.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for active templates.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for templates by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope for templates by channel.
     */
    public function scopeByChannel($query, string $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * Render template with variables.
     */
    public function render(array $variables = []): array
    {
        $subject = $this->renderTemplate($this->subject_template, $variables);
        $body = $this->renderTemplate($this->body_template, $variables);

        return [
            'subject' => $subject,
            'body' => $body,
            'channel' => $this->channel,
            'type' => $this->type
        ];
    }

    /**
     * Render template string with variables.
     */
    protected function renderTemplate(?string $template, array $variables): ?string
    {
        if (!$template) {
            return null;
        }

        return preg_replace_callback('/\{\{([^}]+)\}\}/', function ($matches) use ($variables) {
            $key = trim($matches[1]);
            return data_get($variables, $key, $matches[0]);
        }, $template);
    }

    /**
     * Get default templates for a user.
     */
    public static function getDefaultTemplates(): array
    {
        return [
            [
                'name' => 'Invoice Email',
                'type' => 'invoice_sent',
                'channel' => 'email',
                'subject_template' => 'Invoice #{{invoice.invoice_number}} from {{user.business_name}}',
                'body_template' => 'Dear {{client.name}},\n\nPlease find attached invoice #{{invoice.invoice_number}} for ₹{{invoice.total_amount}}.\n\nDue Date: {{invoice.due_date}}\n\nThank you for your business!\n\nBest regards,\n{{user.business_name}}',
                'variables' => ['invoice', 'client', 'user'],
                'is_active' => true,
                'is_default' => true,
            ],
            [
                'name' => 'Payment Reminder Email',
                'type' => 'payment_reminder',
                'channel' => 'email',
                'subject_template' => 'Payment Reminder - Invoice #{{invoice.invoice_number}}',
                'body_template' => 'Dear {{client.name}},\n\nThis is a friendly reminder that invoice #{{invoice.invoice_number}} for ₹{{invoice.total_amount}} was due on {{invoice.due_date}}.\n\nPlease arrange payment at your earliest convenience.\n\nThank you!\n\n{{user.business_name}}',
                'variables' => ['invoice', 'client', 'user'],
                'is_active' => true,
                'is_default' => true,
            ],
            [
                'name' => 'Payment Reminder WhatsApp',
                'type' => 'payment_reminder',
                'channel' => 'whatsapp',
                'subject_template' => null,
                'body_template' => 'Hi {{client.name}}, invoice #{{invoice.invoice_number}} for ₹{{invoice.total_amount}} was due on {{invoice.due_date}}. Please arrange payment. Thanks! - {{user.business_name}}',
                'variables' => ['invoice', 'client', 'user'],
                'is_active' => true,
                'is_default' => true,
            ],
            [
                'name' => 'Welcome Email',
                'type' => 'client_welcome',
                'channel' => 'email',
                'subject_template' => 'Welcome to {{user.business_name}}!',
                'body_template' => 'Dear {{client.name}},\n\nWelcome to {{user.business_name}}! We are excited to work with you.\n\nOur team will be in touch soon to discuss your requirements.\n\nBest regards,\n{{user.name}}',
                'variables' => ['client', 'user'],
                'is_active' => true,
                'is_default' => true,
            ],
            [
                'name' => 'Contract Expiry Notice',
                'type' => 'contract_expiring',
                'channel' => 'email',
                'subject_template' => 'Contract Renewal Notice - {{contract.title}}',
                'body_template' => 'Dear {{client.name}},\n\nYour contract "{{contract.title}}" is set to expire on {{contract.end_date}}.\n\nPlease contact us to discuss renewal terms.\n\nBest regards,\n{{user.business_name}}',
                'variables' => ['contract', 'client', 'user'],
                'is_active' => true,
                'is_default' => true,
            ]
        ];
    }
}
