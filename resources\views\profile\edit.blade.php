<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Profile') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Plan Status Card -->
            @php
                $planSummary = \App\Services\PlanChecker::getPlanSummary();
                $planName = $planSummary['plan_name'];
            @endphp
            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    <header class="mb-6">
                        <h2 class="text-lg font-medium text-gray-900">
                            {{ __('Subscription Plan') }}
                        </h2>
                        <p class="mt-1 text-sm text-gray-600">
                            {{ __('Manage your subscription and view plan features.') }}
                        </p>
                    </header>

                    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200 p-6">
                        <div class="flex items-center justify-between mb-4">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900">{{ $planName }} {{ $planName === 'Admin' ? 'Access' : 'Plan' }}</h3>
                                <p class="text-sm text-gray-600">
                                    @if($planName === 'Admin')
                                        Full system administration access
                                    @elseif($planName === 'Free')
                                        Get started with basic features
                                    @elseif($planName === 'Pro')
                                        Professional features for growing businesses
                                    @elseif($planName === 'Business')
                                        Advanced features for agencies and teams
                                    @endif
                                </p>
                            </div>
                            @php
                                $badgeColor = match($planName) {
                                    'Admin' => 'bg-red-100 text-red-800',
                                    'Free' => 'bg-gray-100 text-gray-800',
                                    'Pro' => 'bg-blue-100 text-blue-800',
                                    'Business' => 'bg-purple-100 text-purple-800',
                                    default => 'bg-gray-100 text-gray-800'
                                };
                            @endphp
                            <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium {{ $badgeColor }}">
                                @if($planName === 'Admin')
                                    <i class="fas fa-shield-alt w-4 h-4 mr-2"></i>
                                @elseif($planName === 'Free')
                                    <i class="fas fa-gift w-4 h-4 mr-2"></i>
                                @elseif($planName === 'Pro')
                                    <i class="fas fa-star w-4 h-4 mr-2"></i>
                                @elseif($planName === 'Business')
                                    <i class="fas fa-crown w-4 h-4 mr-2"></i>
                                @endif
                                {{ $planName }}
                            </span>
                        </div>

                        <div class="grid grid-cols-2 gap-4 mb-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-900">
                                    {{ $planLimitations['remaining_invoices'] === 'unlimited' ? '∞' : $planLimitations['remaining_invoices'] }}
                                </div>
                                <div class="text-sm text-gray-600">Invoices Remaining</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-gray-900">
                                    {{ $planLimitations['remaining_contracts'] === 'unlimited' ? '∞' : $planLimitations['remaining_contracts'] }}
                                </div>
                                <div class="text-sm text-gray-600">Contracts Remaining</div>
                            </div>
                        </div>

                        <div class="flex justify-between items-center">
                            <a href="{{ route('subscriptions.plans') }}"
                               class="inline-flex items-center px-4 py-2 bg-blue-600 border border-transparent rounded-md font-semibold text-xs text-white uppercase tracking-widest hover:bg-blue-700 focus:bg-blue-700 active:bg-blue-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 transition ease-in-out duration-150">
                                @if($planName === 'Free')
                                    <i class="fas fa-arrow-up mr-2"></i>
                                    Upgrade Plan
                                @else
                                    <i class="fas fa-cog mr-2"></i>
                                    Manage Subscription
                                @endif
                            </a>
                            <a href="{{ route('subscriptions.billing') }}"
                               class="text-sm text-blue-600 hover:text-blue-800">
                                View Billing History
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    @include('profile.partials.update-profile-information-form')
                </div>
            </div>

            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    @include('profile.partials.update-password-form')
                </div>
            </div>

            <div class="p-4 sm:p-8 bg-white shadow sm:rounded-lg">
                <div class="max-w-xl">
                    @include('profile.partials.delete-user-form')
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
