<x-admin-layout>
    <x-slot name="header">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">AI Settings</h1>
            <p class="text-gray-600 mt-2">Configure AI providers and test connections</p>
        </div>
    </x-slot>

<div class="container mx-auto px-4 py-8">
    <div class="max-w-4xl mx-auto">

        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                {{ session('success') }}
            </div>
        @endif

        <!-- Current Provider Status -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Current Configuration</h2>
            
            <div class="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                    <h3 class="font-medium text-gray-900">Active Provider</h3>
                    <p class="text-sm text-gray-600">{{ $providers[$currentProvider]['name'] }}</p>
                </div>
                <div class="flex items-center">
                    @if($connectionStatus[$currentProvider]['success'])
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>
                            Connected
                        </span>
                    @else
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            <i class="fas fa-times-circle mr-1"></i>
                            {{ $connectionStatus[$currentProvider]['error'] ?? 'Connection Failed' }}
                        </span>
                    @endif
                </div>
            </div>
        </div>

        <!-- Provider Selection -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">AI Provider Selection</h2>
            
            <form action="{{ route('admin.ai-settings.update-provider') }}" method="POST">
                @csrf
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    @foreach($providers as $key => $provider)
                        <div class="border rounded-lg p-4 {{ $currentProvider === $key ? 'border-blue-500 bg-blue-50' : 'border-gray-200' }}">
                            <div class="flex items-start justify-between mb-3">
                                <div class="flex items-center">
                                    <input type="radio" 
                                           name="provider" 
                                           value="{{ $key }}" 
                                           id="provider_{{ $key }}"
                                           {{ $currentProvider === $key ? 'checked' : '' }}
                                           class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300">
                                    <label for="provider_{{ $key }}" class="ml-3 font-medium text-gray-900">
                                        {{ $provider['name'] }}
                                    </label>
                                </div>
                                
                                <!-- Connection Status -->
                                <div class="flex items-center">
                                    @if($connectionStatus[$key]['success'])
                                        <span class="text-green-600 text-sm">
                                            <i class="fas fa-check-circle"></i>
                                        </span>
                                    @else
                                        <span class="text-red-600 text-sm">
                                            <i class="fas fa-times-circle"></i>
                                        </span>
                                    @endif
                                    <button type="button" 
                                            onclick="testProvider('{{ $key }}')"
                                            class="ml-2 text-blue-600 hover:text-blue-800 text-sm">
                                        Test
                                    </button>
                                </div>
                            </div>
                            
                            <p class="text-sm text-gray-600 mb-2">{{ $provider['description'] }}</p>
                            
                            <div class="flex justify-between text-xs text-gray-500">
                                <span>Speed: {{ $provider['speed'] }}</span>
                                <span>Cost: {{ $provider['cost'] }}</span>
                            </div>
                            
                            @if(!$connectionStatus[$key]['success'])
                                <div class="mt-2 text-xs text-red-600">
                                    {{ $connectionStatus[$key]['error'] }}
                                </div>
                            @endif
                        </div>
                    @endforeach
                </div>
                
                <div class="mt-6">
                    <button type="submit" 
                            class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg">
                        Update Provider
                    </button>
                </div>
            </form>
        </div>

        <!-- Configuration Instructions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Configuration Instructions</h2>
            
            <div class="space-y-4">
                <div>
                    <h3 class="font-medium text-gray-900 mb-2">OpenAI Configuration</h3>
                    <div class="bg-gray-50 rounded p-3 text-sm">
                        <p class="mb-2">Add these environment variables to your .env file:</p>
                        <code class="block text-xs">
                            OPENAI_API_KEY=your_openai_api_key<br>
                            OPENAI_ORGANIZATION=your_organization_id (optional)<br>
                            OPENAI_MODEL=gpt-4 (optional, default: gpt-4)
                        </code>
                    </div>
                </div>
                
                <div>
                    <h3 class="font-medium text-gray-900 mb-2">Groq Configuration</h3>
                    <div class="bg-gray-50 rounded p-3 text-sm">
                        <p class="mb-2">Add these environment variables to your .env file:</p>
                        <code class="block text-xs">
                            GROQ_API_KEY=your_groq_api_key<br>
                            GROQ_MODEL=meta-llama/llama-4-scout-17b-16e-instruct (optional)
                        </code>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</x-admin-layout>

<script>
async function testProvider(provider) {
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = 'Testing...';
    button.disabled = true;
    
    try {
        const response = await fetch('{{ route("admin.ai-settings.test-provider") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({ provider: provider })
        });
        
        const result = await response.json();
        
        if (result.success) {
            alert('Connection successful!');
        } else {
            alert('Connection failed: ' + result.error);
        }
    } catch (error) {
        alert('Test failed: ' + error.message);
    } finally {
        button.textContent = originalText;
        button.disabled = false;
    }
}
</script>
