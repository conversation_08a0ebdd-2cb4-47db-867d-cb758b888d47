<?php

namespace App\Services;

use App\Models\Workflow;
use App\Models\WorkflowExecution;
use App\Models\Invoice;
use App\Models\Contract;
use App\Models\Client;
use App\Services\NotificationService;
use App\Services\InvoiceService;
use App\Services\TdsService;
use Illuminate\Support\Facades\Log;

class WorkflowAutomationService
{
    protected $notificationService;
    protected $invoiceService;
    protected $tdsService;

    public function __construct(
        NotificationService $notificationService,
        InvoiceService $invoiceService,
        TdsService $tdsService
    ) {
        $this->notificationService = $notificationService;
        $this->invoiceService = $invoiceService;
        $this->tdsService = $tdsService;
    }

    /**
     * Trigger workflows based on event.
     */
    public function triggerWorkflows(string $triggerType, array $data): array
    {
        $results = [
            'triggered' => 0,
            'failed' => 0,
            'errors' => []
        ];

        $workflows = Workflow::active()
            ->byTrigger($triggerType)
            ->get();

        foreach ($workflows as $workflow) {
            try {
                if ($workflow->conditionsMet($data)) {
                    $this->executeWorkflow($workflow, $data);
                    $results['triggered']++;
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'workflow_id' => $workflow->id,
                    'error' => $e->getMessage()
                ];
                Log::error('Workflow execution failed', [
                    'workflow_id' => $workflow->id,
                    'trigger_type' => $triggerType,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Execute a workflow.
     */
    public function executeWorkflow(Workflow $workflow, array $triggerData): WorkflowExecution
    {
        $execution = WorkflowExecution::create([
            'workflow_id' => $workflow->id,
            'trigger_data' => $triggerData,
            'status' => 'running',
            'started_at' => now(),
            'actions_executed' => 0,
            'actions_failed' => 0,
        ]);

        try {
            $executionData = [];
            $actionsExecuted = 0;
            $actionsFailed = 0;

            foreach ($workflow->actions as $action) {
                try {
                    $result = $this->executeAction($action, $triggerData, $executionData);
                    $executionData[$action['type']] = $result;
                    $actionsExecuted++;
                } catch (\Exception $e) {
                    $actionsFailed++;
                    $executionData['errors'][] = [
                        'action' => $action['type'],
                        'error' => $e->getMessage()
                    ];
                    Log::error('Workflow action failed', [
                        'workflow_id' => $workflow->id,
                        'action' => $action['type'],
                        'error' => $e->getMessage()
                    ]);
                }
            }

            $execution->update([
                'actions_executed' => $actionsExecuted,
                'actions_failed' => $actionsFailed,
            ]);

            if ($actionsFailed === 0) {
                $execution->markCompleted($executionData);
            } else {
                $execution->markFailed("Some actions failed: {$actionsFailed} out of " . count($workflow->actions));
            }

            $workflow->incrementExecution();

        } catch (\Exception $e) {
            $execution->markFailed($e->getMessage());
            throw $e;
        }

        return $execution;
    }

    /**
     * Execute a single action.
     */
    protected function executeAction(array $action, array $triggerData, array $executionData): array
    {
        return match ($action['type']) {
            'send_email' => $this->executeSendEmailAction($action, $triggerData),
            'send_notification' => $this->executeSendNotificationAction($action, $triggerData),
            'create_follow_up' => $this->executeCreateFollowUpAction($action, $triggerData),
            'update_invoice_status' => $this->executeUpdateInvoiceStatusAction($action, $triggerData),
            'calculate_tds' => $this->executeCalculateTdsAction($action, $triggerData),
            'generate_contract' => $this->executeGenerateContractAction($action, $triggerData),
            'schedule_reminder' => $this->executeScheduleReminderAction($action, $triggerData),
            'create_recurring_invoice' => $this->executeCreateRecurringInvoiceAction($action, $triggerData),
            default => throw new \Exception("Unknown action type: {$action['type']}")
        };
    }

    /**
     * Execute send email action.
     */
    protected function executeSendEmailAction(array $action, array $triggerData): array
    {
        $recipient = $this->resolveValue($action['recipient'], $triggerData);
        $subject = $this->resolveValue($action['subject'], $triggerData);
        $message = $this->resolveValue($action['message'], $triggerData);

        $success = $this->notificationService->sendEmail($recipient, $message, ['subject' => $subject]);

        return [
            'action' => 'send_email',
            'success' => $success,
            'recipient' => $recipient,
            'subject' => $subject
        ];
    }

    /**
     * Execute send notification action.
     */
    protected function executeSendNotificationAction(array $action, array $triggerData): array
    {
        $channels = $action['channels'] ?? ['email'];
        $message = $this->resolveValue($action['message'], $triggerData);
        
        $recipients = [];
        foreach ($channels as $channel) {
            $recipients[$channel] = $this->resolveValue($action['recipient_' . $channel], $triggerData);
        }

        $results = $this->notificationService->sendMultiChannelNotification($recipients, $message);

        return [
            'action' => 'send_notification',
            'channels' => $channels,
            'results' => $results
        ];
    }

    /**
     * Execute create follow-up action.
     */
    protected function executeCreateFollowUpAction(array $action, array $triggerData): array
    {
        $invoiceId = $this->resolveValue($action['invoice_id'], $triggerData);
        $type = $action['type'] ?? 'reminder';
        $message = $this->resolveValue($action['message'], $triggerData);
        $scheduledDays = $action['scheduled_days'] ?? 3;

        $invoice = Invoice::findOrFail($invoiceId);
        
        $followUp = FollowUp::create([
            'user_id' => $invoice->user_id,
            'invoice_id' => $invoice->id,
            'type' => $type,
            'message' => $message,
            'method' => $action['method'] ?? 'email',
            'status' => 'scheduled',
            'scheduled_at' => now()->addDays($scheduledDays),
        ]);

        return [
            'action' => 'create_follow_up',
            'follow_up_id' => $followUp->id,
            'scheduled_at' => $followUp->scheduled_at
        ];
    }

    /**
     * Execute update invoice status action.
     */
    protected function executeUpdateInvoiceStatusAction(array $action, array $triggerData): array
    {
        $invoiceId = $this->resolveValue($action['invoice_id'], $triggerData);
        $status = $action['status'];

        $invoice = Invoice::findOrFail($invoiceId);
        $oldStatus = $invoice->status;
        $invoice->update(['status' => $status]);

        return [
            'action' => 'update_invoice_status',
            'invoice_id' => $invoice->id,
            'old_status' => $oldStatus,
            'new_status' => $status
        ];
    }

    /**
     * Execute calculate TDS action.
     */
    protected function executeCalculateTdsAction(array $action, array $triggerData): array
    {
        $invoiceId = $this->resolveValue($action['invoice_id'], $triggerData);
        $invoice = Invoice::findOrFail($invoiceId);

        $result = $this->tdsService->autoCalculateTdsForInvoice($invoice);

        return [
            'action' => 'calculate_tds',
            'invoice_id' => $invoice->id,
            'calculation_result' => $result
        ];
    }

    /**
     * Execute schedule reminder action.
     */
    protected function executeScheduleReminderAction(array $action, array $triggerData): array
    {
        $invoiceId = $this->resolveValue($action['invoice_id'], $triggerData);
        $days = $action['days'] ?? 7;
        $type = $action['reminder_type'] ?? 'payment_due';

        $invoice = Invoice::findOrFail($invoiceId);
        
        // Create escalation workflow
        $smartReminderService = app(SmartPaymentReminderService::class);
        $reminders = $smartReminderService->createEscalationWorkflow($invoice);

        return [
            'action' => 'schedule_reminder',
            'invoice_id' => $invoice->id,
            'reminders_created' => count($reminders)
        ];
    }

    /**
     * Resolve dynamic values in action parameters.
     */
    protected function resolveValue($value, array $data)
    {
        if (!is_string($value)) {
            return $value;
        }

        // Replace placeholders like {{invoice.client.email}}
        return preg_replace_callback('/\{\{([^}]+)\}\}/', function ($matches) use ($data) {
            return data_get($data, $matches[1], $matches[0]);
        }, $value);
    }

    /**
     * Create predefined workflows for common tasks.
     */
    public function createPredefinedWorkflows(int $userId): array
    {
        $workflows = [
            $this->createInvoiceApprovalWorkflow($userId),
            $this->createContractRenewalWorkflow($userId),
            $this->createClientOnboardingWorkflow($userId),
            $this->createPaymentOverdueWorkflow($userId),
        ];

        return $workflows;
    }

    /**
     * Create invoice approval workflow.
     */
    protected function createInvoiceApprovalWorkflow(int $userId): Workflow
    {
        return Workflow::create([
            'user_id' => $userId,
            'name' => 'Invoice Approval Workflow',
            'description' => 'Automatically process invoice approvals and send to clients',
            'trigger_type' => 'invoice_created',
            'trigger_conditions' => [
                ['field' => 'status', 'operator' => 'equals', 'value' => 'draft']
            ],
            'actions' => [
                [
                    'type' => 'calculate_tds',
                    'invoice_id' => '{{invoice.id}}'
                ],
                [
                    'type' => 'update_invoice_status',
                    'invoice_id' => '{{invoice.id}}',
                    'status' => 'sent'
                ],
                [
                    'type' => 'send_email',
                    'recipient' => '{{invoice.client.email}}',
                    'subject' => 'Invoice #{{invoice.invoice_number}} from {{invoice.user.business_name}}',
                    'message' => 'Please find attached your invoice for ₹{{invoice.total_amount}}.'
                ],
                [
                    'type' => 'schedule_reminder',
                    'invoice_id' => '{{invoice.id}}'
                ]
            ],
            'status' => 'active',
            'is_active' => true,
        ]);
    }

    /**
     * Create contract renewal workflow.
     */
    protected function createContractRenewalWorkflow(int $userId): Workflow
    {
        return Workflow::create([
            'user_id' => $userId,
            'name' => 'Contract Renewal Workflow',
            'description' => 'Automatically handle contract renewals and notifications',
            'trigger_type' => 'contract_expiring',
            'trigger_conditions' => [
                ['field' => 'days_until_expiry', 'operator' => 'less_than', 'value' => 30]
            ],
            'actions' => [
                [
                    'type' => 'send_email',
                    'recipient' => '{{contract.client.email}}',
                    'subject' => 'Contract Renewal Notice - {{contract.title}}',
                    'message' => 'Your contract {{contract.title}} expires on {{contract.expiry_date}}. Please contact us to renew.'
                ],
                [
                    'type' => 'send_notification',
                    'channels' => ['email'],
                    'recipient_email' => '{{contract.user.email}}',
                    'message' => 'Contract {{contract.title}} with {{contract.client.name}} expires in {{days_until_expiry}} days.'
                ]
            ],
            'status' => 'active',
            'is_active' => true,
        ]);
    }

    /**
     * Create client onboarding workflow.
     */
    protected function createClientOnboardingWorkflow(int $userId): Workflow
    {
        return Workflow::create([
            'user_id' => $userId,
            'name' => 'Client Onboarding Workflow',
            'description' => 'Automatically onboard new clients with welcome messages and setup',
            'trigger_type' => 'client_created',
            'trigger_conditions' => [],
            'actions' => [
                [
                    'type' => 'send_email',
                    'recipient' => '{{client.email}}',
                    'subject' => 'Welcome to {{user.business_name}}!',
                    'message' => 'Welcome {{client.name}}! We are excited to work with you. Our team will be in touch soon.'
                ],
                [
                    'type' => 'send_notification',
                    'channels' => ['email'],
                    'recipient_email' => '{{user.email}}',
                    'message' => 'New client {{client.name}} has been added to your account.'
                ]
            ],
            'status' => 'active',
            'is_active' => true,
        ]);
    }

    /**
     * Create payment overdue workflow.
     */
    protected function createPaymentOverdueWorkflow(int $userId): Workflow
    {
        return Workflow::create([
            'user_id' => $userId,
            'name' => 'Payment Overdue Workflow',
            'description' => 'Automatically handle overdue payments with escalating reminders',
            'trigger_type' => 'invoice_overdue',
            'trigger_conditions' => [
                ['field' => 'days_overdue', 'operator' => 'greater_than', 'value' => 0]
            ],
            'actions' => [
                [
                    'type' => 'create_follow_up',
                    'invoice_id' => '{{invoice.id}}',
                    'type' => 'overdue_notice',
                    'message' => 'Your payment for invoice #{{invoice.invoice_number}} is now {{days_overdue}} days overdue.',
                    'method' => 'email',
                    'scheduled_days' => 0
                ],
                [
                    'type' => 'send_notification',
                    'channels' => ['email'],
                    'recipient_email' => '{{invoice.user.email}}',
                    'message' => 'Invoice #{{invoice.invoice_number}} from {{invoice.client.name}} is {{days_overdue}} days overdue.'
                ]
            ],
            'status' => 'active',
            'is_active' => true,
        ]);
    }

    /**
     * Process all active workflows for a trigger type.
     */
    public function processWorkflowsByTrigger(string $triggerType): array
    {
        $results = [
            'processed' => 0,
            'failed' => 0,
            'errors' => []
        ];

        // Get data based on trigger type
        $triggerData = $this->getTriggerData($triggerType);

        foreach ($triggerData as $data) {
            try {
                $workflowResults = $this->triggerWorkflows($triggerType, $data);
                $results['processed'] += $workflowResults['triggered'];
                $results['failed'] += $workflowResults['failed'];
                $results['errors'] = array_merge($results['errors'], $workflowResults['errors']);
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'trigger_data' => $data,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Get trigger data based on trigger type.
     */
    protected function getTriggerData(string $triggerType): array
    {
        return match ($triggerType) {
            'invoice_overdue' => $this->getOverdueInvoicesData(),
            'contract_expiring' => $this->getExpiringContractsData(),
            'payment_received' => $this->getRecentPaymentsData(),
            default => []
        };
    }

    /**
     * Get overdue invoices data.
     */
    protected function getOverdueInvoicesData(): array
    {
        return Invoice::where('status', 'sent')
            ->where('due_date', '<', now())
            ->with(['client', 'user'])
            ->get()
            ->map(function ($invoice) {
                return [
                    'invoice' => $invoice->toArray(),
                    'days_overdue' => now()->diffInDays($invoice->due_date),
                ];
            })
            ->toArray();
    }

    /**
     * Get expiring contracts data.
     */
    protected function getExpiringContractsData(): array
    {
        return Contract::where('expiry_date', '>', now())
            ->where('expiry_date', '<=', now()->addDays(30))
            ->with(['client', 'user'])
            ->get()
            ->map(function ($contract) {
                return [
                    'contract' => $contract->toArray(),
                    'days_until_expiry' => now()->diffInDays($contract->expiry_date),
                ];
            })
            ->toArray();
    }

    /**
     * Get recent payments data.
     */
    protected function getRecentPaymentsData(): array
    {
        return Invoice::where('status', 'paid')
            ->where('paid_date', '>=', now()->subDay())
            ->with(['client', 'user'])
            ->get()
            ->map(function ($invoice) {
                return [
                    'invoice' => $invoice->toArray(),
                    'payment_date' => $invoice->paid_date,
                ];
            })
            ->toArray();
    }

    /**
     * Dispatch queue-based workflow trigger processing.
     */
    public function dispatchQueueProcessing(string $triggerType, array $triggerData = [], ?int $workflowId = null, array $options = []): void
    {
        \App\Jobs\ProcessWorkflowTriggerJob::dispatch($triggerType, $triggerData, $workflowId, $options)
            ->onQueue($options['queue'] ?? 'default');
    }

    /**
     * Dispatch queue-based processing for all scheduled workflows.
     */
    public function dispatchScheduledProcessing(array $options = []): void
    {
        \App\Jobs\ProcessWorkflowTriggerJob::dispatch('scheduled', [], null, $options)
            ->onQueue($options['queue'] ?? 'default');
    }
}
