<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PlanFeature extends Model
{
    use HasFactory;

    protected $fillable = [
        'plan_id',
        'feature_key',
        'feature_value',
        'feature_type',
    ];

    /**
     * Get the plan that owns the feature.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Check if feature value is unlimited.
     */
    public function isUnlimited(): bool
    {
        return $this->feature_value === 'unlimited';
    }

    /**
     * Get numeric value of the feature.
     */
    public function getNumericValue(): ?int
    {
        if ($this->isUnlimited()) {
            return null;
        }
        return is_numeric($this->feature_value) ? (int) $this->feature_value : 0;
    }

    /**
     * Check if feature is boolean type.
     */
    public function isBoolean(): bool
    {
        return $this->feature_type === 'boolean';
    }

    /**
     * Get boolean value of the feature.
     */
    public function getBooleanValue(): bool
    {
        return in_array(strtolower($this->feature_value), ['true', '1', 'yes', 'enabled']);
    }

    /**
     * Get human-readable feature name.
     */
    public function getFeatureNameAttribute(): string
    {
        $names = [
            'invoices_limit' => 'Invoices per month',
            'contracts_limit' => 'Contracts per month',
            'tds_reports' => 'TDS Reports',
            'custom_branding' => 'Custom Branding',
            'payment_reminders' => 'Payment Reminders',
            'analytics' => 'Analytics Dashboard',
            'api_access' => 'API Access',
            'priority_support' => 'Priority Support',
            'team_collaboration' => 'Team Collaboration',
            'advanced_analytics' => 'Advanced Analytics',
            'white_label' => 'White Label Solution',
            'ai_assistant' => 'AI Document Assistant',
        ];

        return $names[$this->feature_key] ?? ucwords(str_replace('_', ' ', $this->feature_key));
    }
}
