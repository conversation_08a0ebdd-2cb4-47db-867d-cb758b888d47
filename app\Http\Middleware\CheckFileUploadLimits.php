<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Services\FileService;

class CheckFileUploadLimits
{
    public function __construct(
        private FileService $fileService
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        
        // Skip for superadmin users
        if ($user->hasRole('superadmin')) {
            return $next($request);
        }

        // Check if user is uploading a file
        if ($request->hasFile('file') || $request->hasFile('files')) {
            $plan = $user->subscription?->plan ?? 'free';
            
            // Get current storage usage
            $currentUsage = $this->fileService->getStorageUsage($user->id);
            
            // Define storage limits (in bytes)
            $limits = [
                'free' => 100 * 1024 * 1024, // 100MB
                'pro' => 1024 * 1024 * 1024, // 1GB
                'business' => 5 * 1024 * 1024 * 1024, // 5GB
            ];
            
            $limit = $limits[$plan] ?? $limits['free'];
            
            // Calculate size of files being uploaded
            $uploadSize = 0;
            if ($request->hasFile('file')) {
                $uploadSize = $request->file('file')->getSize();
            } elseif ($request->hasFile('files')) {
                foreach ($request->file('files') as $file) {
                    $uploadSize += $file->getSize();
                }
            }
            
            // Check if upload would exceed limit
            if (($currentUsage['used_bytes'] + $uploadSize) > $limit) {
                if ($request->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Storage limit exceeded. Please upgrade your plan to upload more files.',
                        'current_usage' => $currentUsage,
                        'limit' => $limit,
                        'plan' => $plan
                    ], 413);
                }
                
                return redirect()->back()->with('error', 'Storage limit exceeded. Please upgrade your plan to upload more files.');
            }
        }

        return $next($request);
    }
}
