<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('project_members', function (Blueprint $table) {
            $table->id();
            $table->foreignId('project_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->enum('role', ['owner', 'manager', 'member', 'viewer'])->default('member');
            $table->decimal('hourly_rate', 8, 2)->nullable(); // Override project rate for specific member
            $table->boolean('can_track_time')->default(true);
            $table->boolean('can_manage_tasks')->default(false);
            $table->boolean('can_view_reports')->default(true);
            $table->datetime('joined_at')->useCurrent();
            $table->datetime('left_at')->nullable();
            $table->timestamps();

            $table->unique(['project_id', 'user_id']);
            $table->index(['user_id', 'role']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('project_members');
    }
};
