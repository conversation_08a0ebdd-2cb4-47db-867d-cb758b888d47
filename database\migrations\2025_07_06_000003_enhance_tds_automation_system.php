<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Enhance TDS records table - only add columns that don't exist
        Schema::table('tds_records', function (Blueprint $table) {
            // Check and add columns only if they don't exist
            if (!Schema::hasColumn('tds_records', 'is_auto_calculated')) {
                $table->boolean('is_auto_calculated')->default(false)->after('tds_certificate_number');
            }
            if (!Schema::hasColumn('tds_records', 'calculation_method')) {
                $table->string('calculation_method')->nullable()->after('is_auto_calculated');
            }
            if (!Schema::hasColumn('tds_records', 'automation_metadata')) {
                $table->json('automation_metadata')->nullable()->after('calculation_method');
            }
            if (!Schema::hasColumn('tds_records', 'suggested_tds_rate')) {
                $table->decimal('suggested_tds_rate', 5, 2)->nullable()->after('automation_metadata');
            }
            if (!Schema::hasColumn('tds_records', 'rate_justification')) {
                $table->text('rate_justification')->nullable()->after('suggested_tds_rate');
            }
            if (!Schema::hasColumn('tds_records', 'compliance_status')) {
                $table->string('compliance_status')->default('pending')->after('rate_justification');
            }
            if (!Schema::hasColumn('tds_records', 'compliance_checks')) {
                $table->json('compliance_checks')->nullable()->after('compliance_status');
            }
            if (!Schema::hasColumn('tds_records', 'compliance_verified_at')) {
                $table->timestamp('compliance_verified_at')->nullable()->after('compliance_checks');
            }
            if (!Schema::hasColumn('tds_records', 'verified_by')) {
                $table->unsignedBigInteger('verified_by')->nullable()->after('compliance_verified_at');
            }
            if (!Schema::hasColumn('tds_records', 'certificate_status')) {
                $table->string('certificate_status')->default('pending')->after('verified_by');
            }
            if (!Schema::hasColumn('tds_records', 'certificate_requested_at')) {
                $table->timestamp('certificate_requested_at')->nullable()->after('certificate_status');
            }
            if (!Schema::hasColumn('tds_records', 'certificate_received_at')) {
                $table->timestamp('certificate_received_at')->nullable()->after('certificate_requested_at');
            }
            if (!Schema::hasColumn('tds_records', 'certificate_file_path')) {
                $table->string('certificate_file_path')->nullable()->after('certificate_received_at');
            }
            if (!Schema::hasColumn('tds_records', 'ai_insights')) {
                $table->json('ai_insights')->nullable()->after('certificate_file_path');
            }
            if (!Schema::hasColumn('tds_records', 'confidence_score')) {
                $table->decimal('confidence_score', 5, 2)->nullable()->after('ai_insights');
            }
        });

        // Add indexes (will be ignored if they already exist)
        try {
            Schema::table('tds_records', function (Blueprint $table) {
                $table->index('is_auto_calculated');
                $table->index('compliance_status');
                $table->index('certificate_status');
                $table->index('calculation_method');
                $table->index('compliance_verified_at');
            });
        } catch (\Exception $e) {
            // Indexes may already exist, continue
        }

        // Create TDS rate configurations table
        if (!Schema::hasTable('tds_rate_configurations')) {
            Schema::create('tds_rate_configurations', function (Blueprint $table) {
                $table->id();
                $table->string('service_type'); // 'professional_services', 'technical_services', etc.
                $table->string('client_type'); // 'individual', 'company', 'government'
                $table->decimal('tds_rate', 5, 2);
                $table->decimal('threshold_amount', 12, 2)->nullable(); // Minimum amount for TDS applicability
                $table->string('section_code'); // '194J', '194C', etc.
                $table->text('description')->nullable();
                $table->boolean('is_active')->default(true);
                $table->string('financial_year');
                $table->json('conditions')->nullable(); // Additional conditions for rate applicability
                $table->timestamps();

                $table->index(['service_type', 'client_type', 'financial_year'], 'tds_rates_service_client_year_idx');
                $table->index('is_active');
                $table->index('financial_year');
            });
        }

        // Create TDS automation rules table
        if (!Schema::hasTable('tds_automation_rules')) {
            Schema::create('tds_automation_rules', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('rule_name');
                $table->json('conditions'); // Conditions for rule application
                $table->decimal('tds_rate', 5, 2);
                $table->string('section_code');
                $table->boolean('auto_apply')->default(true);
                $table->boolean('require_confirmation')->default(false);
                $table->integer('priority')->default(1); // Higher number = higher priority
                $table->boolean('is_active')->default(true);
                $table->timestamps();

                $table->index(['user_id', 'is_active']);
                $table->index('priority');
            });
        }

        // Create TDS compliance alerts table
        if (!Schema::hasTable('tds_compliance_alerts')) {
            Schema::create('tds_compliance_alerts', function (Blueprint $table) {
                $table->id();
                $table->foreignId('user_id')->constrained()->onDelete('cascade');
                $table->string('alert_type'); // 'quarterly_filing', 'certificate_missing', 'rate_mismatch'
                $table->string('severity'); // 'low', 'medium', 'high', 'critical'
                $table->string('title');
                $table->text('description');
                $table->json('metadata')->nullable(); // Additional alert data
                $table->string('status')->default('active'); // 'active', 'resolved', 'dismissed'
                $table->timestamp('due_date')->nullable();
                $table->timestamp('resolved_at')->nullable();
                $table->text('resolution_notes')->nullable();
                $table->timestamps();

                $table->index(['user_id', 'status']);
                $table->index('alert_type');
                $table->index('severity');
                $table->index('due_date');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tds_compliance_alerts');
        Schema::dropIfExists('tds_automation_rules');
        Schema::dropIfExists('tds_rate_configurations');
        
        Schema::table('tds_records', function (Blueprint $table) {
            $table->dropIndex(['is_auto_calculated']);
            $table->dropIndex(['compliance_status']);
            $table->dropIndex(['certificate_status']);
            $table->dropIndex(['calculation_method']);
            $table->dropIndex(['compliance_verified_at']);
            
            $table->dropColumn([
                'is_auto_calculated',
                'calculation_method',
                'automation_metadata',
                'suggested_tds_rate',
                'rate_justification',
                'compliance_status',
                'compliance_checks',
                'compliance_verified_at',
                'verified_by',
                'certificate_status',
                'certificate_requested_at',
                'certificate_received_at',
                'certificate_file_path',
                'ai_insights',
                'confidence_score'
            ]);
        });
    }
};
