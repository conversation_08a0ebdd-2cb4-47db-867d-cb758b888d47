<?php

namespace App\Http\Controllers;

use App\Services\PlanChecker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class MultiClientDashboardController extends Controller
{
    /**
     * Display multi-client dashboard.
     */
    public function index()
    {
        // Check if user can use multi-client dashboard
        if (!PlanChecker::canUseMultiClientDashboard()) {
            return PlanChecker::redirectToUpgrade('multi_client_dashboard');
        }

        $user = Auth::user();
        
        // Get client analytics with optimized eager loading
        $clients = $user->clients()
            ->with([
                'invoices' => function ($query) {
                    $query->select('id', 'client_id', 'status', 'total_amount', 'due_date', 'created_at');
                },
                'contracts' => function ($query) {
                    $query->select('id', 'client_id', 'status', 'created_at');
                }
            ])
            ->get();
        
        $clientAnalytics = $clients->map(function ($client) {
            return [
                'id' => $client->id,
                'name' => $client->name,
                'company_name' => $client->company_name,
                'total_invoices' => $client->invoices->count(),
                'total_revenue' => $client->invoices->where('status', 'paid')->sum('total_amount'),
                'pending_amount' => $client->invoices->where('status', 'pending')->sum('total_amount'),
                'overdue_amount' => $client->invoices
                    ->where('status', '!=', 'paid')
                    ->where('due_date', '<', now())
                    ->sum('total_amount'),
                'contracts_count' => $client->contracts->count(),
                'last_invoice_date' => $client->invoices->max('created_at'),
                'avg_payment_time' => $this->calculateAveragePaymentTime($client->invoices),
            ];
        });

        return view('multi-client-dashboard.index', compact('clientAnalytics'));
    }

    /**
     * Get client comparison data.
     */
    public function compare(Request $request)
    {
        // Check if user can use multi-client dashboard
        if (!PlanChecker::canUseMultiClientDashboard()) {
            return response()->json(['error' => 'Multi-Client Dashboard requires Business plan'], 403);
        }

        $validated = $request->validate([
            'client_ids' => 'required|array|min:2|max:5',
            'client_ids.*' => 'exists:clients,id',
            'metric' => 'required|in:revenue,invoices,payment_time,profitability'
        ]);

        $user = Auth::user();
        $clients = $user->clients()->whereIn('id', $validated['client_ids'])->get();

        $comparison = $clients->map(function ($client) use ($validated) {
            switch ($validated['metric']) {
                case 'revenue':
                    return [
                        'client' => $client->name,
                        'value' => $client->invoices->where('status', 'paid')->sum('total_amount')
                    ];
                case 'invoices':
                    return [
                        'client' => $client->name,
                        'value' => $client->invoices->count()
                    ];
                case 'payment_time':
                    return [
                        'client' => $client->name,
                        'value' => $this->calculateAveragePaymentTime($client->invoices)
                    ];
                default:
                    return [
                        'client' => $client->name,
                        'value' => 0
                    ];
            }
        });

        return response()->json($comparison);
    }

    /**
     * Calculate average payment time for client invoices.
     */
    private function calculateAveragePaymentTime($invoices)
    {
        $paidInvoices = $invoices->where('status', 'paid')->where('paid_date', '!=', null);
        
        if ($paidInvoices->isEmpty()) {
            return 0;
        }

        $totalDays = $paidInvoices->sum(function ($invoice) {
            return $invoice->created_at->diffInDays($invoice->paid_date);
        });

        return round($totalDays / $paidInvoices->count(), 1);
    }
}
