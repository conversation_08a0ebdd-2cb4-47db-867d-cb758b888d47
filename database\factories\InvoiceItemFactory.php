<?php

namespace Database\Factories;

use App\Models\InvoiceItem;
use App\Models\Invoice;
use Illuminate\Database\Eloquent\Factories\Factory;

class InvoiceItemFactory extends Factory
{
    protected $model = InvoiceItem::class;

    public function definition(): array
    {
        $quantity = fake()->numberBetween(1, 10);
        $rate = fake()->randomFloat(2, 100, 5000);
        $amount = $quantity * $rate;

        return [
            'invoice_id' => Invoice::factory(),
            'description' => fake()->sentence(),
            'quantity' => $quantity,
            'rate' => $rate,
            'amount' => $amount,
        ];
    }

    public function forInvoice(Invoice $invoice): static
    {
        return $this->state(fn (array $attributes) => [
            'invoice_id' => $invoice->id,
        ]);
    }

    public function service(): static
    {
        return $this->state(fn (array $attributes) => [
            'description' => fake()->randomElement([
                'Web Development Services',
                'Mobile App Development',
                'UI/UX Design',
                'Digital Marketing',
                'SEO Optimization',
                'Content Writing',
                'Graphic Design',
                'Database Management',
            ]),
            'quantity' => 1,
        ]);
    }

    public function product(): static
    {
        return $this->state(fn (array $attributes) => [
            'description' => fake()->randomElement([
                'Software License',
                'Hardware Component',
                'Training Material',
                'Documentation',
                'Support Package',
            ]),
            'quantity' => fake()->numberBetween(1, 5),
        ]);
    }
}
