<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('recurring_invoices', function (Blueprint $table) {
            // Enhanced automation fields
            $table->json('automation_settings')->nullable()->after('max_occurrences');
            $table->decimal('success_rate', 5, 2)->default(100.00)->after('automation_settings');
            $table->integer('consecutive_failures')->default(0)->after('success_rate');
            $table->timestamp('last_failure_at')->nullable()->after('consecutive_failures');
            $table->json('client_behavior_data')->nullable()->after('last_failure_at');
            $table->boolean('dynamic_frequency_enabled')->default(false)->after('client_behavior_data');
            $table->string('original_frequency')->nullable()->after('dynamic_frequency_enabled');
            $table->json('frequency_adjustment_history')->nullable()->after('original_frequency');
            $table->boolean('ai_description_enabled')->default(false)->after('frequency_adjustment_history');
            $table->json('retry_settings')->nullable()->after('ai_description_enabled');
            
            // Additional indexes for performance
            $table->index('success_rate');
            $table->index('consecutive_failures');
            $table->index('dynamic_frequency_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('recurring_invoices', function (Blueprint $table) {
            $table->dropIndex(['success_rate']);
            $table->dropIndex(['consecutive_failures']);
            $table->dropIndex(['dynamic_frequency_enabled']);
            
            $table->dropColumn([
                'automation_settings',
                'success_rate',
                'consecutive_failures',
                'last_failure_at',
                'client_behavior_data',
                'dynamic_frequency_enabled',
                'original_frequency',
                'frequency_adjustment_history',
                'ai_description_enabled',
                'retry_settings'
            ]);
        });
    }
};
