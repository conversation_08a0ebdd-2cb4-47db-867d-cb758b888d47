<!-- File Upload Modal -->
<div id="fileUploadModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-1/2 lg:w-1/3 shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Upload File</h3>
                <button onclick="closeFileUploadModal()" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Upload Form -->
            <form id="uploadForm" action="{{ route('files.store') }}" method="POST" enctype="multipart/form-data">
                @csrf
                
                <!-- File Drop Zone -->
                <div class="mb-4">
                    <div id="dropZone" class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors duration-200 cursor-pointer">
                        <div id="dropZoneContent">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 mb-2">Drop files here or click to browse</p>
                            <p class="text-sm text-gray-500">Maximum file size: 10MB</p>
                            <p class="text-sm text-gray-500">Allowed: JPG, PNG, GIF, PDF, DOC, DOCX</p>
                        </div>
                        <input type="file" id="fileInput" name="file" class="hidden" accept=".jpg,.jpeg,.png,.gif,.pdf,.doc,.docx">
                    </div>
                    
                    <!-- Selected File Preview -->
                    <div id="filePreview" class="hidden mt-4 p-4 bg-gray-50 rounded-lg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i id="fileIcon" class="fas fa-file text-2xl text-gray-400 mr-3"></i>
                                <div>
                                    <p id="fileName" class="font-medium text-gray-900"></p>
                                    <p id="fileSize" class="text-sm text-gray-500"></p>
                                </div>
                            </div>
                            <button type="button" onclick="clearFileSelection()" class="text-red-600 hover:text-red-800">
                                <i class="fas fa-times"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Description -->
                <div class="mb-4">
                    <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                        Description (Optional)
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="3" 
                              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="Add a description for this file..."></textarea>
                </div>

                <!-- Public File Option -->
                <div class="mb-6">
                    <label class="flex items-center">
                        <input type="checkbox" name="is_public" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2 text-sm text-gray-700">Make this file publicly accessible</span>
                    </label>
                    <p class="text-xs text-gray-500 mt-1">Public files can be viewed by anyone with the link</p>
                </div>

                <!-- Upload Progress -->
                <div id="uploadProgress" class="hidden mb-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-600">Uploading...</span>
                        <span id="progressPercent" class="text-sm text-gray-600">0%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="progressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>

                <!-- Modal Actions -->
                <div class="flex justify-end space-x-3">
                    <button type="button" 
                            onclick="closeFileUploadModal()" 
                            class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors duration-200">
                        Cancel
                    </button>
                    <button type="submit" 
                            id="uploadButton"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-upload mr-2"></i>
                        Upload File
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const dropZone = document.getElementById('dropZone');
    const fileInput = document.getElementById('fileInput');
    const filePreview = document.getElementById('filePreview');
    const uploadForm = document.getElementById('uploadForm');
    const uploadButton = document.getElementById('uploadButton');
    const uploadProgress = document.getElementById('uploadProgress');

    // Click to browse
    dropZone.addEventListener('click', () => fileInput.click());

    // Drag and drop handlers
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('border-blue-500', 'bg-blue-50');
    });

    dropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropZone.classList.remove('border-blue-500', 'bg-blue-50');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('border-blue-500', 'bg-blue-50');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelection(files[0]);
        }
    });

    // File input change
    fileInput.addEventListener('change', (e) => {
        if (e.target.files.length > 0) {
            handleFileSelection(e.target.files[0]);
        }
    });

    // Handle file selection
    function handleFileSelection(file) {
        const fileName = document.getElementById('fileName');
        const fileSize = document.getElementById('fileSize');
        const fileIcon = document.getElementById('fileIcon');

        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);

        // Set appropriate icon
        const extension = file.name.split('.').pop().toLowerCase();
        fileIcon.className = getFileIcon(extension);

        filePreview.classList.remove('hidden');
        uploadButton.disabled = false;
    }

    // Clear file selection
    window.clearFileSelection = function() {
        fileInput.value = '';
        filePreview.classList.add('hidden');
        uploadButton.disabled = true;
    };

    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Get file icon
    function getFileIcon(extension) {
        const iconMap = {
            'pdf': 'fas fa-file-pdf text-2xl text-red-500',
            'doc': 'fas fa-file-word text-2xl text-blue-500',
            'docx': 'fas fa-file-word text-2xl text-blue-500',
            'jpg': 'fas fa-image text-2xl text-green-500',
            'jpeg': 'fas fa-image text-2xl text-green-500',
            'png': 'fas fa-image text-2xl text-green-500',
            'gif': 'fas fa-image text-2xl text-green-500',
        };
        return iconMap[extension] || 'fas fa-file text-2xl text-gray-400';
    }

    // Form submission with progress
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!fileInput.files.length) {
            showToast('Please select a file to upload.', 'error');
            return;
        }

        const formData = new FormData(uploadForm);
        
        uploadButton.disabled = true;
        uploadProgress.classList.remove('hidden');

        fetch(uploadForm.action, {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Accept': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                closeFileUploadModal();
                location.reload();
            } else {
                showToast(data.message, 'error');
            }
        })
        .catch(error => {
            showToast('An error occurred while uploading the file.', 'error');
        })
        .finally(() => {
            uploadButton.disabled = false;
            uploadProgress.classList.add('hidden');
        });
    });

    // Initialize
    uploadButton.disabled = true;
});
</script>
