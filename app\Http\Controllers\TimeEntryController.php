<?php

namespace App\Http\Controllers;

use App\Models\TimeEntry;
use App\Models\Project;
use App\Models\Task;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Carbon\Carbon;

class TimeEntryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $timeEntries = TimeEntry::where('user_id', Auth::id())
            ->with(['project', 'task'])
            ->latest()
            ->paginate(15);

        $activeTimer = TimeEntry::getActiveTimer(Auth::id());
        $projects = Auth::user()->getAllInvolvedProjects()->get();

        return view('time-tracking.index', compact('timeEntries', 'activeTimer', 'projects'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $projects = Auth::user()->getAllInvolvedProjects()->get();
        $tasks = collect(); // Empty initially, will be populated via AJAX based on project selection

        return view('time-tracking.create', compact('projects', 'tasks'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'task_id' => 'nullable|exists:tasks,id',
            'description' => 'required|string|max:500',
            'start_time' => 'required|date',
            'end_time' => 'nullable|date|after:start_time',
            'duration_minutes' => 'nullable|integer|min:1',
            'hourly_rate' => 'nullable|numeric|min:0',
            'is_billable' => 'boolean',
        ]);

        // Check if user can access the project
        $project = Project::findOrFail($validated['project_id']);
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to log time for this project.');
        }

        $timeEntry = new TimeEntry($validated);
        $timeEntry->user_id = Auth::id();

        // Calculate duration if not provided
        if (!$timeEntry->duration_minutes && $timeEntry->start_time && $timeEntry->end_time) {
            $timeEntry->calculateDuration();
        }

        $timeEntry->save();

        return redirect()->route('time-tracking.index')
            ->with('success', 'Time entry created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(TimeEntry $timeEntry)
    {
        // Check if user can view this time entry
        if ($timeEntry->user_id !== Auth::id()) {
            abort(403, 'You do not have permission to view this time entry.');
        }

        $timeEntry->load(['project', 'task', 'user']);

        return view('time-tracking.show', compact('timeEntry'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(TimeEntry $timeEntry)
    {
        // Check if user can edit this time entry
        if ($timeEntry->user_id !== Auth::id()) {
            abort(403, 'You do not have permission to edit this time entry.');
        }

        $projects = Auth::user()->getAllInvolvedProjects()->get();
        $tasks = $timeEntry->project ? $timeEntry->project->tasks : collect();

        return view('time-tracking.edit', compact('timeEntry', 'projects', 'tasks'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, TimeEntry $timeEntry)
    {
        // Check if user can edit this time entry
        if ($timeEntry->user_id !== Auth::id()) {
            abort(403, 'You do not have permission to edit this time entry.');
        }

        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'task_id' => 'nullable|exists:tasks,id',
            'description' => 'required|string|max:500',
            'start_time' => 'required|date',
            'end_time' => 'nullable|date|after:start_time',
            'duration_minutes' => 'nullable|integer|min:1',
            'hourly_rate' => 'nullable|numeric|min:0',
            'is_billable' => 'boolean',
        ]);

        // Check if user can access the project
        $project = Project::findOrFail($validated['project_id']);
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to log time for this project.');
        }

        $timeEntry->update($validated);

        // Recalculate duration if needed
        if ($timeEntry->start_time && $timeEntry->end_time) {
            $timeEntry->calculateDuration();
            $timeEntry->save();
        }

        return redirect()->route('time-tracking.show', $timeEntry)
            ->with('success', 'Time entry updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(TimeEntry $timeEntry)
    {
        // Check if user can delete this time entry
        if ($timeEntry->user_id !== Auth::id()) {
            abort(403, 'You do not have permission to delete this time entry.');
        }

        $timeEntry->delete();

        return redirect()->route('time-tracking.index')
            ->with('success', 'Time entry deleted successfully.');
    }

    /**
     * Start a new timer.
     */
    public function startTimer(Request $request)
    {
        $validated = $request->validate([
            'project_id' => 'required|exists:projects,id',
            'task_id' => 'nullable|exists:tasks,id',
            'description' => 'nullable|string|max:500',
            'hourly_rate' => 'nullable|numeric|min:0',
        ]);

        // Check if user can access the project
        $project = Project::findOrFail($validated['project_id']);
        if (!Auth::user()->canAccessProject($project)) {
            return response()->json(['error' => 'You do not have permission to log time for this project.'], 403);
        }

        // Stop any existing active timer
        TimeEntry::stopActiveTimer(Auth::id());

        // Use provided hourly rate or fall back to project's hourly rate
        $hourlyRate = $validated['hourly_rate'] ?? $project->hourly_rate;

        // Create new timer entry
        $timeEntry = TimeEntry::create([
            'user_id' => Auth::id(),
            'project_id' => $validated['project_id'],
            'task_id' => $validated['task_id'],
            'description' => $validated['description'] ?: 'Time tracking',
            'start_time' => now(),
            'hourly_rate' => $hourlyRate,
            'is_billable' => true,
        ]);

        return response()->json([
            'success' => true,
            'timer' => $timeEntry->load(['project', 'task'])
        ]);
    }

    /**
     * Stop the active timer.
     */
    public function stopTimer(Request $request)
    {
        $timeEntry = TimeEntry::stopActiveTimer(Auth::id());

        if (!$timeEntry) {
            return response()->json(['error' => 'No active timer found.'], 404);
        }

        return response()->json([
            'success' => true,
            'timer' => $timeEntry->load(['project', 'task'])
        ]);
    }

    /**
     * Pause the active timer.
     */
    public function pauseTimer(Request $request)
    {
        $timeEntry = TimeEntry::stopActiveTimer(Auth::id());

        if (!$timeEntry) {
            return response()->json(['error' => 'No active timer found.'], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Timer paused successfully.',
            'timer' => $timeEntry->load(['project', 'task'])
        ]);
    }

    /**
     * Get the active timer.
     */
    public function getActiveTimer()
    {
        $activeTimer = TimeEntry::getActiveTimer(Auth::id());

        return response()->json([
            'timer' => $activeTimer ? $activeTimer->load(['project', 'task']) : null
        ]);
    }

    /**
     * Show time tracking summary report.
     */
    public function summary(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());

        $timeEntries = TimeEntry::where('user_id', Auth::id())
            ->whereBetween('start_time', [$startDate, $endDate])
            ->with(['project', 'task'])
            ->get();

        $summary = [
            'total_hours' => $timeEntries->sum('duration') / 60,
            'billable_hours' => $timeEntries->where('is_billable', true)->sum('duration') / 60,
            'total_entries' => $timeEntries->count(),
            'projects' => $timeEntries->groupBy('project_id')->map(function ($entries) {
                return [
                    'project' => $entries->first()->project,
                    'total_hours' => $entries->sum('duration') / 60,
                    'entries_count' => $entries->count(),
                ];
            }),
        ];

        return view('time-tracking.reports.summary', compact('summary', 'timeEntries', 'startDate', 'endDate'));
    }

    /**
     * Show timesheet report.
     */
    public function timesheet(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfWeek());
        $endDate = $request->get('end_date', now()->endOfWeek());

        $timeEntries = TimeEntry::where('user_id', Auth::id())
            ->whereBetween('start_time', [$startDate, $endDate])
            ->with(['project', 'task'])
            ->orderBy('start_time')
            ->get();

        return view('time-tracking.reports.timesheet', compact('timeEntries', 'startDate', 'endDate'));
    }

    /**
     * Export time tracking data.
     */
    public function export(Request $request)
    {
        $startDate = $request->get('start_date', now()->startOfMonth());
        $endDate = $request->get('end_date', now()->endOfMonth());
        $format = $request->get('format', 'csv');

        $timeEntries = TimeEntry::where('user_id', Auth::id())
            ->whereBetween('start_time', [$startDate, $endDate])
            ->with(['project', 'task'])
            ->orderBy('start_time')
            ->get();

        if ($format === 'csv') {
            $filename = 'timesheet_' . $startDate . '_to_' . $endDate . '.csv';

            $headers = [
                'Content-Type' => 'text/csv',
                'Content-Disposition' => 'attachment; filename="' . $filename . '"',
            ];

            $callback = function() use ($timeEntries) {
                $file = fopen('php://output', 'w');
                fputcsv($file, ['Date', 'Project', 'Task', 'Description', 'Start Time', 'End Time', 'Duration (hours)', 'Billable']);

                foreach ($timeEntries as $entry) {
                    fputcsv($file, [
                        $entry->start_time->format('Y-m-d'),
                        $entry->project->name,
                        $entry->task ? $entry->task->title : '',
                        $entry->description,
                        $entry->start_time->format('H:i'),
                        $entry->end_time ? $entry->end_time->format('H:i') : '',
                        round($entry->duration / 60, 2),
                        $entry->is_billable ? 'Yes' : 'No',
                    ]);
                }

                fclose($file);
            };

            return response()->stream($callback, 200, $headers);
        }

        // Default to showing export options
        return view('time-tracking.reports.export', compact('timeEntries', 'startDate', 'endDate'));
    }
}
