<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Contract;
use App\Models\FollowUp;
use App\Models\Invoice;
use App\Models\TdsRecord;
use App\Services\DashboardService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    protected DashboardService $dashboardService;

    public function __construct(DashboardService $dashboardService)
    {
        $this->dashboardService = $dashboardService;
    }

    /**
     * Display the dashboard.
     */
    public function index()
    {
        $dashboardData = $this->dashboardService->getDashboardData(Auth::id());

        // Extract data for view compatibility
        extract($dashboardData['overview_stats']);
        extract($dashboardData['financial_metrics']);
        extract($dashboardData['recent_activities']);
        extract($dashboardData['analytics_data']);
        extract($dashboardData['plan_limitations']);
        extract($dashboardData['alerts_notifications']);

        return view('dashboard', compact(
            'total_clients',
            'total_invoices',
            'total_contracts',
            'total_invoice_amount',
            'paid_invoice_amount',
            'pending_invoice_amount',
            'overdue_invoice_amount',
            'paid_invoices',
            'pending_invoices',
            'draft_invoices',
            'overdue_invoices',
            'revenue_growth',
            'invoice_growth',
            'current_month_revenue',
            'previous_month_revenue',
            'recent_invoices',
            'recent_contracts',
            'total_tds_amount',
            'current_financial_year',
            'pending_follow_ups',
            'monthly_revenue',
            'contract_stats',
            'top_clients',
            'invoice_status_data',
            'average_invoice_value',
            'invoice_usage',
            'contract_usage',
            'plan_name',
            'features'
        ));
    }

    /**
     * Get dashboard data for AJAX requests.
     */
    public function getData(Request $request)
    {
        $type = $request->get('type');
        $data = $this->dashboardService->getAjaxData(Auth::id(), $type);

        if (empty($data)) {
            return response()->json(['error' => 'Invalid data type'], 400);
        }

        return response()->json($data);
    }
}
