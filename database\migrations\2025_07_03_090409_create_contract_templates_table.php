<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contract_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('type'); // 'nda', 'service_agreement', etc.
            $table->text('content'); // Template with placeholders like {{client_name}}
            $table->json('variables'); // Array of variable names needed for this template
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        // Add foreign key constraint to contracts table
        Schema::table('contracts', function (Blueprint $table) {
            $table->foreign('contract_template_id')->references('id')->on('contract_templates')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop foreign key constraint first
        Schema::table('contracts', function (Blueprint $table) {
            $table->dropForeign(['contract_template_id']);
        });

        Schema::dropIfExists('contract_templates');
    }
};
