<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class NotificationPreference extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'client_id',
        'notification_type',
        'email_enabled',
        'whatsapp_enabled',
        'sms_enabled',
        'preferred_channel',
        'preferred_time',
        'timezone',
        'frequency_limit',
        'quiet_hours_start',
        'quiet_hours_end',
    ];

    protected function casts(): array
    {
        return [
            'email_enabled' => 'boolean',
            'whatsapp_enabled' => 'boolean',
            'sms_enabled' => 'boolean',
            'preferred_time' => 'datetime',
            'frequency_limit' => 'integer',
        ];
    }

    /**
     * Get the user that owns the preference.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the client for this preference.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Check if a channel is enabled.
     */
    public function isChannelEnabled(string $channel): bool
    {
        return match ($channel) {
            'email' => $this->email_enabled,
            'whatsapp' => $this->whatsapp_enabled,
            'sms' => $this->sms_enabled,
            default => false
        };
    }

    /**
     * Get enabled channels.
     */
    public function getEnabledChannels(): array
    {
        $channels = [];
        
        if ($this->email_enabled) $channels[] = 'email';
        if ($this->whatsapp_enabled) $channels[] = 'whatsapp';
        if ($this->sms_enabled) $channels[] = 'sms';
        
        return $channels;
    }

    /**
     * Check if current time is within quiet hours.
     */
    public function isQuietTime(): bool
    {
        if (!$this->quiet_hours_start || !$this->quiet_hours_end) {
            return false;
        }

        $now = now($this->timezone ?? config('app.timezone'));
        $start = $now->copy()->setTimeFromTimeString($this->quiet_hours_start);
        $end = $now->copy()->setTimeFromTimeString($this->quiet_hours_end);

        // Handle overnight quiet hours (e.g., 22:00 to 08:00)
        if ($start->gt($end)) {
            return $now->gte($start) || $now->lte($end);
        }

        return $now->between($start, $end);
    }

    /**
     * Get default preferences for a user.
     */
    public static function getDefaultPreferences(): array
    {
        return [
            'email_enabled' => true,
            'whatsapp_enabled' => false,
            'sms_enabled' => false,
            'preferred_channel' => 'email',
            'preferred_time' => '10:00:00',
            'timezone' => 'Asia/Kolkata',
            'frequency_limit' => 3, // Max 3 notifications per day
            'quiet_hours_start' => '22:00:00',
            'quiet_hours_end' => '08:00:00',
        ];
    }

    /**
     * Create default preferences for notification types.
     */
    public static function createDefaultPreferences(int $userId, ?int $clientId = null): array
    {
        $types = [
            'invoice_sent',
            'payment_reminder',
            'payment_received',
            'contract_expiring',
            'automation_alert'
        ];

        $preferences = [];
        $defaults = self::getDefaultPreferences();

        foreach ($types as $type) {
            $preferences[] = self::create(array_merge($defaults, [
                'user_id' => $userId,
                'client_id' => $clientId,
                'notification_type' => $type,
            ]));
        }

        return $preferences;
    }
}
