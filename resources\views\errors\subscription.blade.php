<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Subscription Error - {{ config('app.name') }}</title>
    @vite(['resources/css/app.css'])
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center px-4">
        <div class="max-w-md w-full text-center">
            <!-- Logo -->
            <div class="mb-8">
                <x-logo class="h-12 mx-auto" />
            </div>

            <!-- Error Content -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <div class="mb-6">
                    <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-orange-100 mb-4">
                        <svg class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                    
                    <h2 class="text-2xl font-semibold text-gray-900 mb-4">Subscription Issue</h2>
                    <p class="text-gray-600 mb-6">
                        {{ $message ?? 'We encountered an issue with your subscription. Please try again or contact support.' }}
                    </p>
                    
                    @if(isset($errorType) && $errorType !== 'general')
                        <div class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
                            <p class="text-sm text-orange-700">
                                <span class="font-medium">Issue Type:</span> 
                                @switch($errorType)
                                    @case('plan_limit')
                                        Plan limitation exceeded
                                        @break
                                    @case('payment_failed')
                                        Payment processing failed
                                        @break
                                    @case('subscription_expired')
                                        Subscription has expired
                                        @break
                                    @case('upgrade_failed')
                                        Upgrade process failed
                                        @break
                                    @default
                                        {{ ucfirst(str_replace('_', ' ', $errorType)) }}
                                @endswitch
                            </p>
                            @if(isset($subscriptionId))
                                <p class="text-sm text-orange-700 mt-1">
                                    <span class="font-medium">Subscription ID:</span> {{ $subscriptionId }}
                                </p>
                            @endif
                        </div>
                    @endif
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3">
                    @if(isset($errorType) && $errorType === 'plan_limit')
                        <a href="{{ route('subscriptions.plans') }}" 
                           class="w-full bg-emerald-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-emerald-700 transition-colors inline-block">
                            Upgrade Plan
                        </a>
                    @else
                        <a href="{{ route('subscriptions.index') }}" 
                           class="w-full bg-emerald-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-emerald-700 transition-colors inline-block">
                            Manage Subscription
                        </a>
                    @endif
                    
                    <a href="{{ route('dashboard') }}" 
                       class="w-full bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors inline-block">
                        Go to Dashboard
                    </a>
                </div>

                <!-- Help Text -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <p class="text-sm text-gray-500">
                        Need help with your subscription? <a href="{{ route('contact') }}" class="text-emerald-600 hover:text-emerald-700">Contact our support team</a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
