<?php

namespace App\Providers;

use App\Models\Client;
use App\Models\Invoice;
use App\Models\User;
use App\Observers\ClientObserver;
use App\Observers\InvoiceObserver;
use App\Observers\UserObserver;
use Illuminate\Support\ServiceProvider;

// Repository Interfaces and Implementations
use App\Contracts\RepositoryInterface;
use App\Repositories\BaseRepository;
use App\Repositories\InvoiceRepository;
use App\Repositories\ClientRepository;
use App\Repositories\ContractRepository;
use App\Repositories\TdsRepository;

// Services
use App\Services\InvoiceService;
use App\Services\ClientService;
use App\Services\ContractService;
use App\Services\TdsService;
use App\Services\DashboardService;
use App\Services\OpenAIService;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        // Register repositories
        $this->app->bind(InvoiceRepository::class, function ($app) {
            return new InvoiceRepository();
        });

        $this->app->bind(ClientRepository::class, function ($app) {
            return new ClientRepository();
        });

        $this->app->bind(ContractRepository::class, function ($app) {
            return new ContractRepository();
        });

        $this->app->bind(TdsRepository::class, function ($app) {
            return new TdsRepository();
        });

        // Register services
        $this->app->bind(InvoiceService::class, function ($app) {
            return new InvoiceService($app->make(InvoiceRepository::class));
        });

        $this->app->bind(ClientService::class, function ($app) {
            return new ClientService($app->make(ClientRepository::class));
        });

        $this->app->bind(ContractService::class, function ($app) {
            return new ContractService($app->make(ContractRepository::class));
        });

        $this->app->bind(TdsService::class, function ($app) {
            return new TdsService($app->make(TdsRepository::class));
        });

        $this->app->bind(DashboardService::class, function ($app) {
            return new DashboardService(
                $app->make(InvoiceRepository::class),
                $app->make(ClientRepository::class),
                $app->make(ContractRepository::class),
                $app->make(TdsRepository::class)
            );
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register model observers
        User::observe(UserObserver::class);
        Client::observe(ClientObserver::class);
        Invoice::observe(InvoiceObserver::class);
    }
}
