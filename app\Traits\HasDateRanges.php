<?php

namespace App\Traits;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

trait HasDateRanges
{
    /**
     * Get current month date range
     */
    public function getCurrentMonthRange(): array
    {
        $start = Carbon::now()->startOfMonth();
        $end = Carbon::now()->endOfMonth();

        return [$start, $end];
    }

    /**
     * Get previous month date range
     */
    public function getPreviousMonthRange(): array
    {
        $start = Carbon::now()->subMonth()->startOfMonth();
        $end = Carbon::now()->subMonth()->endOfMonth();

        return [$start, $end];
    }

    /**
     * Get current year date range
     */
    public function getCurrentYearRange(): array
    {
        $start = Carbon::now()->startOfYear();
        $end = Carbon::now()->endOfYear();

        return [$start, $end];
    }

    /**
     * Get previous year date range
     */
    public function getPreviousYearRange(): array
    {
        $start = Carbon::now()->subYear()->startOfYear();
        $end = Carbon::now()->subYear()->endOfYear();

        return [$start, $end];
    }

    /**
     * Get last N days range
     */
    public function getLastNDaysRange(int $days): array
    {
        $start = Carbon::now()->subDays($days)->startOfDay();
        $end = Carbon::now()->endOfDay();

        return [$start, $end];
    }

    /**
     * Get last N months range
     */
    public function getLastNMonthsRange(int $months): array
    {
        $start = Carbon::now()->subMonths($months)->startOfMonth();
        $end = Carbon::now()->endOfMonth();

        return [$start, $end];
    }

    /**
     * Get financial year range (April to March)
     */
    public function getFinancialYearRange(int $year = null): array
    {
        $year = $year ?? Carbon::now()->year;
        
        // If current month is before April, use previous year
        if (Carbon::now()->month < 4) {
            $year = $year - 1;
        }

        $start = Carbon::create($year, 4, 1)->startOfDay();
        $end = Carbon::create($year + 1, 3, 31)->endOfDay();

        return [$start, $end];
    }

    /**
     * Get current financial year
     */
    public function getCurrentFinancialYear(): string
    {
        $currentYear = Carbon::now()->year;
        
        if (Carbon::now()->month < 4) {
            return ($currentYear - 1) . '-' . $currentYear;
        }
        
        return $currentYear . '-' . ($currentYear + 1);
    }

    /**
     * Apply date range to query
     */
    public function applyDateRange(Builder $query, string $column, Carbon $startDate, Carbon $endDate): Builder
    {
        return $query->whereBetween($column, [$startDate, $endDate]);
    }

    /**
     * Apply current month filter to query
     */
    public function applyCurrentMonthFilter(Builder $query, string $column = 'created_at'): Builder
    {
        [$start, $end] = $this->getCurrentMonthRange();
        return $this->applyDateRange($query, $column, $start, $end);
    }

    /**
     * Apply previous month filter to query
     */
    public function applyPreviousMonthFilter(Builder $query, string $column = 'created_at'): Builder
    {
        [$start, $end] = $this->getPreviousMonthRange();
        return $this->applyDateRange($query, $column, $start, $end);
    }

    /**
     * Apply current year filter to query
     */
    public function applyCurrentYearFilter(Builder $query, string $column = 'created_at'): Builder
    {
        [$start, $end] = $this->getCurrentYearRange();
        return $this->applyDateRange($query, $column, $start, $end);
    }

    /**
     * Apply financial year filter to query
     */
    public function applyFinancialYearFilter(Builder $query, string $column = 'created_at', int $year = null): Builder
    {
        [$start, $end] = $this->getFinancialYearRange($year);
        return $this->applyDateRange($query, $column, $start, $end);
    }

    /**
     * Get month name from date
     */
    public function getMonthName(Carbon $date): string
    {
        return $date->format('F Y');
    }

    /**
     * Get short month name from date
     */
    public function getShortMonthName(Carbon $date): string
    {
        return $date->format('M Y');
    }

    /**
     * Check if date is overdue
     */
    public function isOverdue(Carbon $dueDate): bool
    {
        return $dueDate->isPast();
    }

    /**
     * Get days until due
     */
    public function getDaysUntilDue(Carbon $dueDate): int
    {
        return Carbon::now()->diffInDays($dueDate, false);
    }
}
