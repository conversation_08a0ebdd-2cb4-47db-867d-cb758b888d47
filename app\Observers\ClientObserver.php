<?php

namespace App\Observers;

use App\Models\Client;

class ClientObserver
{
    /**
     * Handle the Client "deleting" event.
     */
    public function deleting(Client $client): void
    {
        // Delete related records in proper order
        $client->followUps()->delete();
        $client->tdsRecords()->delete();
        
        // Delete invoices and their items
        foreach ($client->invoices as $invoice) {
            $invoice->items()->delete();
            $invoice->delete();
        }
        
        // Delete contracts
        $client->contracts()->delete();
    }
}
