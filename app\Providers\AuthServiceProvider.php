<?php

namespace App\Providers;

use App\Models\Client;
use App\Models\Contract;
use App\Models\File;
use App\Models\FollowUp;
use App\Models\Invoice;
use App\Models\Project;
use App\Models\TdsRecord;
use App\Policies\ClientPolicy;
use App\Policies\ContractPolicy;
use App\Policies\FilePolicy;
use App\Policies\FollowUpPolicy;
use App\Policies\InvoicePolicy;
use App\Policies\ProjectPolicy;
use App\Policies\TdsRecordPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Client::class => ClientPolicy::class,
        Contract::class => ContractPolicy::class,
        File::class => FilePolicy::class,
        FollowUp::class => FollowUpPolicy::class,
        Invoice::class => InvoicePolicy::class,
        Project::class => ProjectPolicy::class,
        TdsRecord::class => TdsRecordPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        //
    }
}
