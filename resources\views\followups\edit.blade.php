<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Edit Follow-up') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('followups.update', $followup) }}" x-data="followupForm()">
                        @csrf
                        @method('PATCH')

                        <!-- Template Selection -->
                        <div class="mb-6">
                            <label for="followup_template_id" class="block text-sm font-medium text-gray-700">Follow-up Template</label>
                            <select name="followup_template_id" id="followup_template_id" x-model="selectedTemplate" @change="loadTemplate()"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">Select a template (optional)</option>
                                @foreach($templates as $template)
                                    <option value="{{ $template->id }}" 
                                            {{ old('followup_template_id', $followup->followup_template_id) == $template->id ? 'selected' : '' }}
                                            data-subject="{{ $template->subject }}" data-message="{{ $template->message }}">
                                        {{ $template->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('followup_template_id')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Client Selection -->
                        <div class="mb-6">
                            <label for="client_id" class="block text-sm font-medium text-gray-700">Client *</label>
                            <select name="client_id" id="client_id" required x-model="selectedClient" @change="loadClientInvoices()"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">Select a client</option>
                                @foreach($clients as $client)
                                    <option value="{{ $client->id }}" 
                                            {{ old('client_id', $followup->client_id) == $client->id ? 'selected' : '' }}>
                                        {{ $client->name }}{{ $client->company_name ? ' (' . $client->company_name . ')' : '' }}
                                    </option>
                                @endforeach
                            </select>
                            @error('client_id')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Invoice Selection -->
                        <div class="mb-6">
                            <label for="invoice_id" class="block text-sm font-medium text-gray-700">Related Invoice (Optional)</label>
                            <select name="invoice_id" id="invoice_id" x-model="selectedInvoice"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">No specific invoice (general follow-up)</option>
                                <template x-for="invoice in clientInvoices" :key="invoice.id">
                                    <option :value="invoice.id" 
                                            :selected="invoice.id == {{ old('invoice_id', $followup->invoice_id ?? 'null') }}"
                                            x-text="`${invoice.invoice_number} - ₹${invoice.total_amount} (${invoice.status})`"></option>
                                </template>
                            </select>
                            @error('invoice_id')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Subject -->
                        <div class="mb-6">
                            <label for="subject" class="block text-sm font-medium text-gray-700">Subject *</label>
                            <input type="text" name="subject" id="subject" required x-model="subject"
                                   value="{{ old('subject', $followup->subject) }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('subject')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Message -->
                        <div class="mb-6">
                            <label for="message" class="block text-sm font-medium text-gray-700">Message *</label>
                            <textarea name="message" id="message" rows="8" required x-model="message"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('message', $followup->message) }}</textarea>
                            @error('message')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-2 text-sm text-gray-500">
                                You can use placeholders: {client_name}, {company_name}, {invoice_number}, {invoice_amount}, {due_date}
                            </p>
                        </div>

                        <!-- Scheduled Date -->
                        <div class="mb-6">
                            <label for="scheduled_date" class="block text-sm font-medium text-gray-700">Scheduled Date & Time *</label>
                            <input type="datetime-local" name="scheduled_date" id="scheduled_date" required
                                   value="{{ old('scheduled_date', $followup->scheduled_date->format('Y-m-d\TH:i')) }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('scheduled_date')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="mb-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Internal Notes</label>
                            <textarea name="notes" id="notes" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('notes', $followup->notes) }}</textarea>
                            @error('notes')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-2 text-sm text-gray-500">These notes are for your reference and won't be sent to the client.</p>
                        </div>

                        <!-- Status Information -->
                        @if($followup->status !== 'scheduled')
                            <div class="mb-6 p-4 bg-gray-50 rounded-lg">
                                <h3 class="text-sm font-medium text-gray-900 mb-2">Follow-up Status</h3>
                                <div class="text-sm text-gray-600">
                                    <p><strong>Current Status:</strong> {{ ucfirst($followup->status) }}</p>
                                    @if($followup->sent_at)
                                        <p><strong>Sent At:</strong> {{ $followup->sent_at->format('d/m/Y H:i') }}</p>
                                    @endif
                                    @if($followup->completed_at)
                                        <p><strong>Completed At:</strong> {{ $followup->completed_at->format('d/m/Y H:i') }}</p>
                                    @endif
                                </div>
                            </div>
                        @endif

                        <!-- Preview Section -->
                        <div class="mb-6" x-show="subject || message">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Preview</h3>
                            <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                                <div class="mb-2">
                                    <strong>Subject:</strong> <span x-text="subject"></span>
                                </div>
                                <div>
                                    <strong>Message:</strong>
                                    <div class="mt-2 whitespace-pre-wrap" x-text="message"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-4">
                            <a href="{{ route('followups.show', $followup) }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            @if($followup->status === 'scheduled')
                                <button type="submit" name="action" value="update" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Update Follow-up
                                </button>
                                <button type="submit" name="action" value="send_now" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                    Update & Send Now
                                </button>
                            @else
                                <button type="submit" name="action" value="update" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Update Follow-up
                                </button>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function followupForm() {
            return {
                selectedTemplate: '{{ old('followup_template_id', $followup->followup_template_id) }}',
                selectedClient: '{{ old('client_id', $followup->client_id) }}',
                selectedInvoice: '{{ old('invoice_id', $followup->invoice_id) }}',
                subject: '{{ old('subject', $followup->subject) }}',
                message: `{{ old('message', $followup->message) }}`,
                clientInvoices: [],

                loadTemplate() {
                    if (this.selectedTemplate) {
                        const option = document.querySelector(`option[value="${this.selectedTemplate}"]`);
                        if (option) {
                            this.subject = option.dataset.subject || '';
                            this.message = option.dataset.message || '';
                        }
                    }
                },

                async loadClientInvoices() {
                    if (this.selectedClient) {
                        try {
                            const response = await fetch(`/api/clients/${this.selectedClient}/invoices`);
                            if (response.ok) {
                                this.clientInvoices = await response.json();
                            }
                        } catch (error) {
                            console.error('Error loading client invoices:', error);
                            this.clientInvoices = [];
                        }
                    } else {
                        this.clientInvoices = [];
                    }
                },

                init() {
                    // Load client invoices if client is pre-selected
                    if (this.selectedClient) {
                        this.loadClientInvoices();
                    }
                }
            }
        }
    </script>
</x-app-layout>
