<x-app-layout>
    <x-slot name="header">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
                <h1 class="text-xl sm:text-2xl font-bold text-gray-900">Dashboard</h1>
                <p class="text-gray-600 mt-1 text-sm sm:text-base">Welcome back, {{ Auth::user()->name }}! Here's what's happening with your business.</p>
            </div>
            <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">
                <x-ui.button id="refreshDashboard" variant="secondary" icon="fas fa-sync-alt" class="w-full sm:w-auto">
                    <span class="sm:inline">Refresh</span>
                </x-ui.button>
                <x-ui.button href="{{ route('invoices.create') }}" variant="primary" icon="fas fa-plus" class="w-full sm:w-auto">
                    New Invoice
                </x-ui.button>
            </div>
        </div>
    </x-slot>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 mb-6 sm:mb-8">
        <!-- Total Revenue Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden hover:shadow-md hover:border-gray-200 transition-all duration-300 animate-slideUp">
            <div class="p-4 sm:p-6">
                <div class="flex items-center justify-between">
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-600">Total Revenue</p>
                        <p class="text-xl sm:text-2xl font-bold text-gray-900 mt-1 truncate">₹{{ number_format($total_invoice_amount, 0) }}</p>
                        @if($revenue_growth != 0)
                            <div class="flex items-center mt-2">
                                @if($revenue_growth > 0)
                                    <i class="fas fa-arrow-up text-green-500 text-sm mr-1"></i>
                                    <span class="text-green-600 text-sm font-medium">+{{ number_format($revenue_growth, 1) }}%</span>
                                @else
                                    <i class="fas fa-arrow-down text-red-500 text-sm mr-1"></i>
                                    <span class="text-red-600 text-sm font-medium">{{ number_format($revenue_growth, 1) }}%</span>
                                @endif
                                <span class="text-gray-500 text-sm ml-1 hidden sm:inline">vs last month</span>
                            </div>
                        @endif
                    </div>
                    <div class="p-2 sm:p-3 bg-blue-100 rounded-full ml-3 flex-shrink-0">
                        <i class="fas fa-rupee-sign text-blue-600 text-lg sm:text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Invoices Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Invoices</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">{{ $total_invoices }}</p>
                        @if($invoice_growth != 0)
                            <div class="flex items-center mt-2">
                                @if($invoice_growth > 0)
                                    <i class="fas fa-arrow-up text-green-500 text-sm mr-1"></i>
                                    <span class="text-green-600 text-sm font-medium">+{{ number_format($invoice_growth, 1) }}%</span>
                                @else
                                    <i class="fas fa-arrow-down text-red-500 text-sm mr-1"></i>
                                    <span class="text-red-600 text-sm font-medium">{{ number_format($invoice_growth, 1) }}%</span>
                                @endif
                                <span class="text-gray-500 text-sm ml-1">vs last month</span>
                            </div>
                        @endif
                    </div>
                    <div class="p-3 bg-green-100 rounded-full">
                        <i class="fas fa-file-invoice text-green-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Clients Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Clients</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">{{ $total_clients }}</p>
                        <p class="text-sm text-gray-500 mt-1">Active clients</p>
                    </div>
                    <div class="p-3 bg-orange-100 rounded-full">
                        <i class="fas fa-users text-orange-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pending Amount Card -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Pending Amount</p>
                        <p class="text-2xl font-bold text-gray-900 mt-1">₹{{ number_format($pending_invoice_amount, 0) }}</p>
                        <p class="text-sm text-gray-500 mt-1">{{ $pending_invoices }} invoices</p>
                    </div>
                    <div class="p-3 bg-red-100 rounded-full">
                        <i class="fas fa-clock text-red-600 text-xl"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Plan Usage Indicators (for free plan users) -->
    @if(!auth()->user()->hasRole('admin') && (!auth()->user()->currentPlan || auth()->user()->currentPlan->slug === 'free'))
        <div class="mb-6 sm:mb-8">
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200 p-4 sm:p-6">
                <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-1">{{ $plan_name }} Plan</h3>
                        <p class="text-sm text-gray-600">Track your usage and upgrade when needed</p>
                    </div>
                    <x-ui.button href="{{ route('subscriptions.plans') }}" variant="primary" icon="fas fa-crown" class="w-full sm:w-auto">
                        Upgrade Plan
                    </x-ui.button>
                </div>

                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
                    <!-- Invoice Usage -->
                    <x-ui.usage-indicator
                        type="invoices"
                        :current="$invoice_usage['used']"
                        :limit="$invoice_usage['limit'] === 'unlimited' ? null : $invoice_usage['limit']"
                        :show-upgrade="false" />

                    <!-- Contract Usage -->
                    <x-ui.usage-indicator
                        type="contracts"
                        :current="$contract_usage['used']"
                        :limit="$contract_usage['limit'] === 'unlimited' ? null : $contract_usage['limit']"
                        :show-upgrade="false" />

                    <!-- Feature Access -->
                    <div class="bg-white rounded-lg border border-gray-200 p-4">
                        <h4 class="text-sm font-medium text-gray-700 mb-3">Feature Access</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between text-xs">
                                <span class="text-gray-600">TDS Reports</span>
                                @if($features['tds_reports'])
                                    <i class="fas fa-check text-green-500"></i>
                                @else
                                    <i class="fas fa-times text-red-500"></i>
                                @endif
                            </div>

                            <div class="flex items-center justify-between text-xs">
                                <span class="text-gray-600">AI Assistant</span>
                                @if($features['ai_assistant'])
                                    <i class="fas fa-check text-green-500"></i>
                                @else
                                    <i class="fas fa-times text-red-500"></i>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    @endif

    @if(\App\Services\PlanChecker::canUseAiAssistant())
        <!-- AI Business Insights -->
        <div class="bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl shadow-sm border border-purple-200 overflow-hidden mb-8">
            <div class="px-6 py-4 border-b border-purple-200 bg-gradient-to-r from-purple-100 to-indigo-100">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-500 rounded-lg mr-3">
                            <i class="fas fa-brain text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-purple-900">AI Business Insights</h3>
                            <p class="text-sm text-purple-700">Get AI-powered recommendations for your business</p>
                        </div>
                    </div>
                    <button id="generateInsights"
                            class="bg-purple-500 hover:bg-purple-600 text-white font-medium py-2 px-4 rounded-lg transition-colors flex items-center">
                        <i class="fas fa-magic mr-2"></i>
                        Generate Insights
                    </button>
                </div>
            </div>
            <div id="insightsContent" class="p-6">
                <div class="text-center text-purple-600">
                    <i class="fas fa-lightbulb text-4xl mb-3"></i>
                    <p class="text-lg font-medium">Click "Generate Insights" to get AI-powered business recommendations</p>
                    <p class="text-sm text-purple-500 mt-1">Analyze your revenue patterns, client performance, and growth opportunities</p>
                </div>
            </div>
        </div>
    @endif

    <!-- Revenue Overview and Quick Stats -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <!-- Revenue Chart -->
        <div class="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-100 bg-gray-50/50">
                <h3 class="text-lg font-semibold text-gray-900">Revenue Overview</h3>
                <p class="text-sm text-gray-600">Monthly revenue for the current year</p>
            </div>
            <div class="p-6">
                <canvas id="revenueChart" height="300"></canvas>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="space-y-6">
            <!-- Invoice Status Distribution -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
                <div class="px-6 py-4 border-b border-gray-100 bg-gray-50/50">
                    <h3 class="text-lg font-semibold text-gray-900">Invoice Status</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-600">Paid</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ $paid_invoices }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-600">Pending</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ $pending_invoices }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-600">Overdue</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ $overdue_invoices }}</span>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-3 h-3 bg-gray-400 rounded-full mr-3"></div>
                            <span class="text-sm text-gray-600">Draft</span>
                        </div>
                        <span class="text-sm font-medium text-gray-900">{{ $draft_invoices }}</span>
                    </div>
                </div>
            </div>

            <!-- TDS Summary -->
            <div class="card">
                <div class="card-header">
                    <h3 class="text-lg font-semibold text-gray-900">TDS Summary</h3>
                    <p class="text-sm text-gray-600">{{ $current_financial_year }}</p>
                </div>
                <div class="card-body text-center">
                    <p class="text-2xl font-bold text-gray-900">₹{{ number_format($total_tds_amount, 0) }}</p>
                    <p class="text-sm text-gray-500 mt-1">Total TDS deducted</p>
                    <a href="{{ route('tds.index') }}" class="btn-secondary mt-4 w-full">
                        View TDS Records
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
        <!-- Recent Invoices -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Recent Invoices</h3>
                    <a href="{{ route('invoices.index') }}" class="text-primary-600 hover:text-primary-700 text-sm font-medium">
                        View All
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if($recent_invoices->count() > 0)
                    <div class="space-y-4">
                        @foreach($recent_invoices as $invoice)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $invoice->invoice_number }}</p>
                                    <p class="text-sm text-gray-600">{{ $invoice->client->name }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-900">₹{{ number_format($invoice->total_amount, 0) }}</p>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        @if($invoice->status === 'paid') bg-success-100 text-success-800
                                        @elseif($invoice->status === 'pending') bg-warning-100 text-warning-800
                                        @elseif($invoice->status === 'overdue') bg-danger-100 text-danger-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($invoice->status) }}
                                    </span>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-file-invoice text-gray-400 text-3xl mb-3"></i>
                        <p class="text-gray-500">No invoices yet</p>
                        <a href="{{ route('invoices.create') }}" class="btn-primary mt-3">
                            Create Your First Invoice
                        </a>
                    </div>
                @endif
            </div>
        </div>

        <!-- Overdue Invoices -->
        <div class="card">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Overdue Invoices</h3>
                    <span class="bg-danger-100 text-danger-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                        {{ $overdue_invoices->count() }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                @if($overdue_invoices->count() > 0)
                    <div class="space-y-4">
                        @foreach($overdue_invoices as $invoice)
                            <div class="flex items-center justify-between p-3 bg-danger-50 rounded-lg border border-danger-200">
                                <div>
                                    <p class="font-medium text-gray-900">{{ $invoice->invoice_number }}</p>
                                    <p class="text-sm text-gray-600">{{ $invoice->client->name }}</p>
                                    <p class="text-xs text-danger-600">Due: {{ $invoice->due_date->format('M d, Y') }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="font-medium text-gray-900">₹{{ number_format($invoice->total_amount, 0) }}</p>
                                    <a href="{{ route('invoices.show', $invoice) }}" class="text-primary-600 hover:text-primary-700 text-sm">
                                        View
                                    </a>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-8">
                        <i class="fas fa-check-circle text-success-400 text-3xl mb-3"></i>
                        <p class="text-gray-500">No overdue invoices</p>
                        <p class="text-sm text-gray-400">Great job staying on top of your payments!</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Chart.js Script -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Revenue Chart
        const ctx = document.getElementById('revenueChart').getContext('2d');
        const revenueChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: @json(array_keys($monthly_revenue)),
                datasets: [{
                    label: 'Revenue',
                    data: @json(array_values($monthly_revenue)),
                    borderColor: '#0ea5e9',
                    backgroundColor: 'rgba(14, 165, 233, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '₹' + value.toLocaleString();
                            }
                        }
                    }
                }
            }
        });

        // Refresh Dashboard
        document.getElementById('refreshDashboard').addEventListener('click', function() {
            location.reload();
        });

        // AI Business Insights
        @if(\App\Services\PlanChecker::canUseAiAssistant())
        document.getElementById('generateInsights').addEventListener('click', function() {
            const button = this;
            const contentDiv = document.getElementById('insightsContent');

            // Show loading state
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Analyzing...';
            button.disabled = true;

            contentDiv.innerHTML = `
                <div class="text-center text-purple-600">
                    <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mb-3"></div>
                    <p class="text-lg font-medium">Analyzing your business data...</p>
                    <p class="text-sm text-purple-500 mt-1">This may take a few moments</p>
                </div>
            `;

            fetch('{{ route("ai-assistant.business-insights") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.insights) {
                    contentDiv.innerHTML = `
                        <div class="space-y-4">
                            <div class="bg-white rounded-lg p-4 border border-purple-200">
                                <h4 class="font-semibold text-purple-900 mb-3 flex items-center">
                                    <i class="fas fa-chart-line mr-2"></i>
                                    AI Business Analysis
                                </h4>
                                <div class="text-gray-700 text-sm leading-relaxed">
                                    ${data.insights.replace(/\n/g, '<br>')}
                                </div>
                            </div>
                            <div class="text-xs text-purple-600 flex items-center justify-center">
                                <i class="fas fa-info-circle mr-1"></i>
                                Insights generated based on your business data and AI analysis
                            </div>
                        </div>
                    `;
                } else {
                    contentDiv.innerHTML = `
                        <div class="text-center text-red-600">
                            <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                            <p class="font-medium">Error generating insights</p>
                            <p class="text-sm">${data.error || 'Please try again later'}</p>
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                contentDiv.innerHTML = `
                    <div class="text-center text-red-600">
                        <i class="fas fa-exclamation-triangle text-2xl mb-2"></i>
                        <p class="font-medium">Network error</p>
                        <p class="text-sm">Please check your connection and try again</p>
                    </div>
                `;
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
        });
        @endif
    </script>
</x-app-layout>
