@props([
    'icon' => 'fas fa-inbox',
    'title' => 'No data found',
    'description' => null,
    'action' => null,
    'actionText' => null,
    'actionHref' => null
])

<div class="text-center py-12">
    <div class="mx-auto h-12 w-12 text-gray-400 mb-4">
        @if(str_starts_with($icon, 'fas') || str_starts_with($icon, 'far') || str_starts_with($icon, 'fab'))
            <i class="{{ $icon }} text-4xl"></i>
        @else
            <svg class="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                {!! $icon !!}
            </svg>
        @endif
    </div>
    
    <h3 class="text-lg font-medium text-gray-900 mb-2">{{ $title }}</h3>
    
    @if($description)
        <p class="text-gray-500 mb-6 max-w-sm mx-auto">{{ $description }}</p>
    @endif
    
    @if($action || $actionHref)
        <div class="mt-6">
            @if($actionHref)
                <x-ui.button href="{{ $actionHref }}" variant="primary" icon="fas fa-plus">
                    {{ $actionText ?? 'Add New' }}
                </x-ui.button>
            @elseif($action)
                {{ $action }}
            @endif
        </div>
    @endif
</div>
