<?php

namespace Database\Seeders;

use App\Models\Plan;
use App\Models\PlanFeature;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PlanSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currency = config('services.currency.code', 'USD');

        // Free Plan
        $freePlan = Plan::create([
            'name' => 'Free',
            'slug' => 'free',
            'description' => 'Perfect for getting started with freelancing',
            'price' => 0.00,
            'currency' => $currency,
            'billing_cycle' => 'monthly',
            'sort_order' => 1,
            'is_popular' => false,
            'is_active' => true,
            'features' => [
                '3 invoices/month',
                '1 contract/NDA template',
                'TDS tracker (view only)',
                'Basic client management',
                'Manual follow-up messages'
            ]
        ]);

        // Free Plan Features
        $freePlan->planFeatures()->createMany([
            ['feature_key' => 'invoices_limit', 'feature_value' => '3', 'feature_type' => 'limit'],
            ['feature_key' => 'contracts_limit', 'feature_value' => '1', 'feature_type' => 'limit'],
            ['feature_key' => 'tds_reports', 'feature_value' => 'false', 'feature_type' => 'boolean'],
            ['feature_key' => 'custom_branding', 'feature_value' => 'false', 'feature_type' => 'boolean'],
            ['feature_key' => 'whatsapp_shortcuts', 'feature_value' => 'false', 'feature_type' => 'boolean'],
            ['feature_key' => 'priority_support', 'feature_value' => 'false', 'feature_type' => 'boolean'],
            ['feature_key' => 'multi_client_dashboard', 'feature_value' => 'false', 'feature_type' => 'boolean'],
            ['feature_key' => 'portfolio_generator', 'feature_value' => 'false', 'feature_type' => 'boolean'],
            ['feature_key' => 'early_access', 'feature_value' => 'false', 'feature_type' => 'boolean'],
            ['feature_key' => 'ai_assistant', 'feature_value' => 'false', 'feature_type' => 'boolean'],
        ]);

        // Pro Plan
        $proPlan = Plan::create([
            'name' => 'Pro',
            'slug' => 'pro',
            'description' => 'Everything you need to run your freelance business professionally',
            'price' => $currency === 'USD' ? 9.00 : 199.00, // $9/month for USD, ₹199/month for INR
            'currency' => $currency,
            'billing_cycle' => 'monthly',
            'sort_order' => 2,
            'is_popular' => true,
            'is_active' => true,
            'features' => [
                'Unlimited invoices (no watermark)',
                'Unlimited contracts/NDAs',
                'TDS tracker + downloadable reports',
                'Custom branding (logo + colors)',
                'WhatsApp follow-up shortcuts',
                'Priority support'
            ]
        ]);

        // Pro Plan Features
        $proPlan->planFeatures()->createMany([
            ['feature_key' => 'invoices_limit', 'feature_value' => 'unlimited', 'feature_type' => 'limit'],
            ['feature_key' => 'contracts_limit', 'feature_value' => 'unlimited', 'feature_type' => 'limit'],
            ['feature_key' => 'tds_reports', 'feature_value' => 'true', 'feature_type' => 'boolean'],
            ['feature_key' => 'custom_branding', 'feature_value' => 'true', 'feature_type' => 'boolean'],
            ['feature_key' => 'whatsapp_shortcuts', 'feature_value' => 'true', 'feature_type' => 'boolean'],
            ['feature_key' => 'priority_support', 'feature_value' => 'true', 'feature_type' => 'boolean'],
            ['feature_key' => 'multi_client_dashboard', 'feature_value' => 'false', 'feature_type' => 'boolean'],
            ['feature_key' => 'portfolio_generator', 'feature_value' => 'false', 'feature_type' => 'boolean'],
            ['feature_key' => 'early_access', 'feature_value' => 'false', 'feature_type' => 'boolean'],
            ['feature_key' => 'ai_assistant', 'feature_value' => 'false', 'feature_type' => 'boolean'],
        ]);

        // Business Plan
        $businessPlan = Plan::create([
            'name' => 'Business',
            'slug' => 'business',
            'description' => 'Advanced features for agencies and growing businesses',
            'price' => $currency === 'USD' ? 29.00 : 499.00, // $29/month for USD, ₹499/month for INR
            'currency' => $currency,
            'billing_cycle' => 'monthly',
            'sort_order' => 3,
            'is_popular' => false,
            'is_active' => true,
            'features' => [
                'Everything in Pro',
                'Multi-client dashboard',
                'Shared portfolio generator',
                'Early access to new features',
                'AI document assistant'
            ]
        ]);

        // Business Plan Features
        $businessPlan->planFeatures()->createMany([
            ['feature_key' => 'invoices_limit', 'feature_value' => 'unlimited', 'feature_type' => 'limit'],
            ['feature_key' => 'contracts_limit', 'feature_value' => 'unlimited', 'feature_type' => 'limit'],
            ['feature_key' => 'tds_reports', 'feature_value' => 'true', 'feature_type' => 'boolean'],
            ['feature_key' => 'custom_branding', 'feature_value' => 'true', 'feature_type' => 'boolean'],
            ['feature_key' => 'whatsapp_shortcuts', 'feature_value' => 'true', 'feature_type' => 'boolean'],
            ['feature_key' => 'priority_support', 'feature_value' => 'true', 'feature_type' => 'boolean'],
            ['feature_key' => 'multi_client_dashboard', 'feature_value' => 'true', 'feature_type' => 'boolean'],
            ['feature_key' => 'portfolio_generator', 'feature_value' => 'true', 'feature_type' => 'boolean'],
            ['feature_key' => 'early_access', 'feature_value' => 'true', 'feature_type' => 'boolean'],
            ['feature_key' => 'ai_assistant', 'feature_value' => 'true', 'feature_type' => 'boolean'],
        ]);
    }
}
