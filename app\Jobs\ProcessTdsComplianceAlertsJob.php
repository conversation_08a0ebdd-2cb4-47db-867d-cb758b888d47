<?php

namespace App\Jobs;

use App\Models\User;
use App\Services\TdsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessTdsComplianceAlertsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 180;
    public $tries = 2;
    public $backoff = [30, 60];

    protected ?int $userId;
    protected array $alertData;

    public function __construct(?int $userId = null, array $alertData = [])
    {
        $this->userId = $userId;
        $this->alertData = $alertData;
        $this->onQueue('high');
    }

    public function handle(TdsService $service): void
    {
        if ($this->userId) {
            $this->processUserComplianceAlerts($service);
        } else {
            $this->processAllComplianceAlerts($service);
        }
    }

    protected function processUserComplianceAlerts(TdsService $service): void
    {
        $user = User::find($this->userId);
        if (!$user) {
            Log::warning('User not found for TDS compliance alerts', ['user_id' => $this->userId]);
            return;
        }

        $results = $service->processComplianceAlerts($this->userId);
        
        Log::info('TDS compliance alerts processed for user', [
            'user_id' => $this->userId,
            'alerts_sent' => $results['alerts_sent']
        ]);
    }

    protected function processAllComplianceAlerts(TdsService $service): void
    {
        $results = $service->processAllComplianceAlerts();
        
        Log::info('Batch TDS compliance alerts processed', [
            'users_processed' => $results['users_processed'],
            'total_alerts' => $results['total_alerts']
        ]);
    }

    public function failed(Exception $exception): void
    {
        Log::error('TDS compliance alerts job failed', [
            'user_id' => $this->userId,
            'error' => $exception->getMessage()
        ]);
    }

    public function tags(): array
    {
        $tags = ['tds-compliance'];
        if ($this->userId) {
            $tags[] = "user:{$this->userId}";
        }
        return $tags;
    }
}
