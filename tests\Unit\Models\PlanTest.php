<?php

namespace Tests\Unit\Models;

use App\Models\Plan;
use App\Models\User;
use App\Models\UserSubscription;
use App\Models\PlanFeature;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PlanTest extends TestCase
{
    use RefreshDatabase;

    public function test_plan_has_fillable_attributes()
    {
        $plan = new Plan();
        $fillable = [
            'name', 'slug', 'description', 'price', 'currency', 'billing_cycle',
            'sort_order', 'is_popular', 'is_active', 'features'
        ];

        $this->assertEquals($fillable, $plan->getFillable());
    }

    public function test_plan_has_many_users()
    {
        $plan = Plan::factory()->create();
        $users = User::factory()->count(3)->create(['current_plan_id' => $plan->id]);

        $this->assertCount(3, $plan->users);
        $this->assertInstanceOf(User::class, $plan->users->first());
    }

    public function test_plan_has_many_subscriptions()
    {
        $plan = Plan::factory()->create();
        $subscriptions = UserSubscription::factory()->count(5)->create(['plan_id' => $plan->id]);

        $this->assertCount(5, $plan->subscriptions);
        $this->assertInstanceOf(UserSubscription::class, $plan->subscriptions->first());
    }

    public function test_plan_has_many_features()
    {
        $plan = Plan::factory()->create();

        // Create features with unique feature_key values
        $features = [
            PlanFeature::factory()->create([
                'plan_id' => $plan->id,
                'feature_key' => 'invoices_limit',
                'feature_value' => 'unlimited',
                'feature_type' => 'limit'
            ]),
            PlanFeature::factory()->create([
                'plan_id' => $plan->id,
                'feature_key' => 'has_watermark',
                'feature_value' => 'false',
                'feature_type' => 'boolean'
            ]),
            PlanFeature::factory()->create([
                'plan_id' => $plan->id,
                'feature_key' => 'contracts_limit',
                'feature_value' => '50',
                'feature_type' => 'limit'
            ]),
            PlanFeature::factory()->create([
                'plan_id' => $plan->id,
                'feature_key' => 'api_access',
                'feature_value' => 'true',
                'feature_type' => 'boolean'
            ]),
        ];

        $this->assertCount(4, $plan->planFeatures);
        $this->assertInstanceOf(PlanFeature::class, $plan->planFeatures->first());
    }

    public function test_plan_get_default_currency()
    {
        $defaultCurrency = Plan::getDefaultCurrency();
        $this->assertIsString($defaultCurrency);
        $this->assertEquals('USD', $defaultCurrency); // Based on config
    }

    public function test_plan_get_currency_symbol()
    {
        $currencySymbol = Plan::getCurrencySymbol();
        $this->assertIsString($currencySymbol);
        $this->assertEquals('$', $currencySymbol); // Based on config
    }

    public function test_plan_formatted_price_attribute()
    {
        $freePlan = Plan::factory()->create(['price' => 0]);
        $this->assertEquals('$0', $freePlan->formatted_price);

        $paidPlan = Plan::factory()->create(['price' => 199.99]);
        $this->assertEquals('$200', $paidPlan->formatted_price);

        $expensivePlan = Plan::factory()->create(['price' => 1500.00]);
        $this->assertEquals('$1,500', $expensivePlan->formatted_price);
    }

    public function test_plan_has_plan_features()
    {
        $plan = Plan::factory()->create();

        // Create some plan features
        PlanFeature::factory()->create([
            'plan_id' => $plan->id,
            'feature_key' => 'invoices_limit',
            'feature_value' => 'unlimited',
            'feature_type' => 'limit'
        ]);

        PlanFeature::factory()->create([
            'plan_id' => $plan->id,
            'feature_key' => 'has_watermark',
            'feature_value' => 'false',
            'feature_type' => 'boolean'
        ]);

        $this->assertCount(2, $plan->planFeatures);
        $this->assertInstanceOf(PlanFeature::class, $plan->planFeatures->first());
    }

    public function test_plan_can_be_free()
    {
        $freePlan = Plan::factory()->free()->create();

        $this->assertEquals('Free', $freePlan->name);
        $this->assertEquals(0, $freePlan->price);

        // Check that plan has expected features
        $invoicesLimit = $freePlan->getFeatureValue('invoices_limit');
        $hasWatermark = $freePlan->getFeatureValue('has_watermark');

        $this->assertEquals('3', $invoicesLimit);
        $this->assertEquals('true', $hasWatermark);
    }

    public function test_plan_can_be_pro()
    {
        $proPlan = Plan::factory()->pro()->create();

        $this->assertEquals('Pro', $proPlan->name);
        $this->assertEquals(199, $proPlan->price);

        // Check that plan has expected features
        $invoicesLimit = $proPlan->getFeatureValue('invoices_limit');

        $this->assertEquals('unlimited', $invoicesLimit);
    }

    public function test_plan_can_be_business()
    {
        $businessPlan = Plan::factory()->business()->create();

        $this->assertEquals('Business', $businessPlan->name);
        $this->assertEquals(499, $businessPlan->price);

        // Check that plan has expected features
        $invoicesLimit = $businessPlan->getFeatureValue('invoices_limit');
        $hasAnalytics = $businessPlan->getFeatureValue('analytics');

        $this->assertEquals('unlimited', $invoicesLimit);
        $this->assertEquals('true', $hasAnalytics);
    }

    public function test_plan_can_be_popular()
    {
        $popularPlan = Plan::factory()->create(['is_popular' => true]);
        $regularPlan = Plan::factory()->create(['is_popular' => false]);

        $this->assertTrue($popularPlan->is_popular);
        $this->assertFalse($regularPlan->is_popular);
    }

    public function test_plan_can_be_active_or_inactive()
    {
        $activePlan = Plan::factory()->create(['is_active' => true]);
        $inactivePlan = Plan::factory()->create(['is_active' => false]);

        $this->assertTrue($activePlan->is_active);
        $this->assertFalse($inactivePlan->is_active);
    }

    public function test_plan_billing_cycle_options()
    {
        $monthlyPlan = Plan::factory()->create(['billing_cycle' => 'monthly']);
        $yearlyPlan = Plan::factory()->create(['billing_cycle' => 'yearly']);

        $this->assertEquals('monthly', $monthlyPlan->billing_cycle);
        $this->assertEquals('yearly', $yearlyPlan->billing_cycle);
    }

    public function test_plan_sort_order()
    {
        $plan1 = Plan::factory()->create(['sort_order' => 1]);
        $plan2 = Plan::factory()->create(['sort_order' => 2]);
        $plan3 = Plan::factory()->create(['sort_order' => 3]);

        $sortedPlans = Plan::orderBy('sort_order')->get();

        $this->assertEquals($plan1->id, $sortedPlans->first()->id);
        $this->assertEquals($plan3->id, $sortedPlans->last()->id);
    }

    public function test_plan_slug_is_unique()
    {
        $plan1 = Plan::factory()->create(['slug' => 'unique-plan']);
        
        $this->assertEquals('unique-plan', $plan1->slug);
        
        // Second plan should have different slug
        $plan2 = Plan::factory()->create();
        $this->assertNotEquals('unique-plan', $plan2->slug);
    }

    public function test_plan_has_feature_method()
    {
        $plan = Plan::factory()->pro()->create();

        // Test getFeature method returns PlanFeature object
        $invoicesFeature = $plan->getFeature('invoices_limit');
        $watermarkFeature = $plan->getFeature('has_watermark');
        $nonExistentFeature = $plan->getFeature('api_access');

        $this->assertInstanceOf(PlanFeature::class, $invoicesFeature);
        $this->assertInstanceOf(PlanFeature::class, $watermarkFeature);
        $this->assertNull($nonExistentFeature);

        // Test getFeatureValue method
        $this->assertEquals('unlimited', $plan->getFeatureValue('invoices_limit'));
        $this->assertEquals('false', $plan->getFeatureValue('has_watermark'));
        $this->assertNull($plan->getFeatureValue('api_access'));
    }

    public function test_plan_price_is_decimal()
    {
        $plan = Plan::factory()->create(['price' => 199.99]);

        $this->assertIsFloat($plan->price);
        $this->assertEquals(199.99, $plan->price);
    }
}
