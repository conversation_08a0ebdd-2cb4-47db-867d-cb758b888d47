<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\UserSubscription;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class RazorpayService
{
    private $keyId;
    private $keySecret;
    private $baseUrl;

    public function __construct()
    {
        $this->keyId = config('services.razorpay.key_id');
        $this->keySecret = config('services.razorpay.key_secret');
        $this->baseUrl = 'https://api.razorpay.com/v1';
    }

    /**
     * Create Razorpay subscription.
     */
    public function createSubscription(Payment $payment)
    {
        $subscription = $payment->userSubscription;
        $plan = $subscription->plan;

        // First create a plan if it doesn't exist
        $planId = $this->getOrCreatePlan($plan);

        $subscriptionData = [
            'plan_id' => $planId,
            'customer_notify' => 1,
            'total_count' => 120, // 10 years worth of monthly payments
            'notes' => [
                'user_id' => $payment->user_id,
                'payment_id' => $payment->id,
            ],
        ];

        $response = Http::withBasicAuth($this->keyId, $this->keySecret)
            ->post($this->baseUrl . '/subscriptions', $subscriptionData);

        if ($response->successful()) {
            $responseData = $response->json();
            
            // Update payment with Razorpay subscription ID
            $payment->update([
                'gateway_payment_id' => $responseData['id'],
                'gateway_response' => $responseData,
            ]);

            // Update subscription with Razorpay data
            $subscription->update([
                'gateway_subscription_id' => $responseData['id'],
                'gateway_data' => $responseData,
            ]);

            return $responseData;
        }

        throw new \Exception('Failed to create Razorpay subscription: ' . $response->body());
    }

    /**
     * Get or create Razorpay plan.
     */
    private function getOrCreatePlan($plan)
    {
        // Convert price to paise (Razorpay uses smallest currency unit)
        $amountInPaise = $plan->price * 100;

        $planData = [
            'period' => 'monthly',
            'interval' => 1,
            'item' => [
                'name' => 'Freeligo ' . $plan->name . ' Plan',
                'description' => $plan->description,
                'amount' => $amountInPaise,
                'currency' => $plan->currency,
            ],
            'notes' => [
                'plan_slug' => $plan->slug,
                'plan_id' => $plan->id,
            ],
        ];

        $response = Http::withBasicAuth($this->keyId, $this->keySecret)
            ->post($this->baseUrl . '/plans', $planData);

        if ($response->successful()) {
            return $response->json()['id'];
        }

        throw new \Exception('Failed to create Razorpay plan: ' . $response->body());
    }

    /**
     * Create Razorpay order for one-time payment.
     */
    public function createOrder(Payment $payment)
    {
        $amountInPaise = $payment->amount * 100;

        $orderData = [
            'amount' => $amountInPaise,
            'currency' => $payment->currency,
            'receipt' => 'payment_' . $payment->id,
            'notes' => [
                'user_id' => $payment->user_id,
                'payment_id' => $payment->id,
                'type' => $payment->type,
            ],
        ];

        $response = Http::withBasicAuth($this->keyId, $this->keySecret)
            ->post($this->baseUrl . '/orders', $orderData);

        if ($response->successful()) {
            $responseData = $response->json();
            
            $payment->update([
                'gateway_payment_id' => $responseData['id'],
                'gateway_response' => $responseData,
            ]);

            return $responseData;
        }

        throw new \Exception('Failed to create Razorpay order: ' . $response->body());
    }

    /**
     * Verify payment signature.
     */
    public function verifyPaymentSignature($paymentId, $orderId, $signature)
    {
        $expectedSignature = hash_hmac('sha256', $orderId . '|' . $paymentId, $this->keySecret);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Handle Razorpay webhook.
     */
    public function handleWebhook($payload, $signature)
    {
        // Verify webhook signature
        if (!$this->verifyWebhookSignature($payload, $signature)) {
            throw new \Exception('Invalid Razorpay webhook signature');
        }

        $event = json_decode($payload, true);
        
        switch ($event['event']) {
            case 'subscription.activated':
                $this->handleSubscriptionActivated($event);
                break;
            case 'subscription.cancelled':
                $this->handleSubscriptionCancelled($event);
                break;
            case 'payment.captured':
                $this->handlePaymentCaptured($event);
                break;
            case 'invoice.paid':
                $this->handleInvoicePaid($event);
                break;
            default:
                Log::info('Unhandled Razorpay webhook event: ' . $event['event']);
        }
    }

    /**
     * Verify Razorpay webhook signature.
     */
    private function verifyWebhookSignature($payload, $signature)
    {
        $webhookSecret = config('services.razorpay.webhook_secret');
        $expectedSignature = hash_hmac('sha256', $payload, $webhookSecret);
        return hash_equals($expectedSignature, $signature);
    }

    /**
     * Handle subscription activated event.
     */
    private function handleSubscriptionActivated($event)
    {
        $subscriptionId = $event['payload']['subscription']['entity']['id'];
        
        $subscription = UserSubscription::where('gateway_subscription_id', $subscriptionId)->first();
        if ($subscription) {
            $subscription->update(['status' => 'active']);
            
            $payment = $subscription->payments()->where('status', 'pending')->first();
            if ($payment) {
                $payment->markAsCompleted($event);
            }
        }
    }

    /**
     * Handle subscription cancelled event.
     */
    private function handleSubscriptionCancelled($event)
    {
        $subscriptionId = $event['payload']['subscription']['entity']['id'];
        
        $subscription = UserSubscription::where('gateway_subscription_id', $subscriptionId)->first();
        if ($subscription) {
            $subscription->cancel();
        }
    }

    /**
     * Handle payment captured event.
     */
    private function handlePaymentCaptured($event)
    {
        $paymentData = $event['payload']['payment']['entity'];
        $orderId = $paymentData['order_id'];
        
        $payment = Payment::where('gateway_payment_id', $orderId)->first();
        if ($payment) {
            $payment->markAsCompleted($event);
            
            // If this is a subscription payment, activate the subscription
            if ($payment->userSubscription) {
                $payment->userSubscription->update(['status' => 'active']);
            }
        }
    }

    /**
     * Handle invoice paid event (for recurring payments).
     */
    private function handleInvoicePaid($event)
    {
        $invoiceData = $event['payload']['invoice']['entity'];
        $subscriptionId = $invoiceData['subscription_id'];
        
        $subscription = UserSubscription::where('gateway_subscription_id', $subscriptionId)->first();
        if ($subscription) {
            // Create new payment record for recurring payment
            Payment::create([
                'user_id' => $subscription->user_id,
                'user_subscription_id' => $subscription->id,
                'gateway_payment_id' => $invoiceData['id'],
                'gateway' => 'razorpay',
                'amount' => $invoiceData['amount'] / 100, // Convert from paise
                'currency' => $invoiceData['currency'],
                'status' => 'completed',
                'type' => 'renewal',
                'gateway_response' => $event,
                'paid_at' => now(),
            ]);
        }
    }

    /**
     * Cancel Razorpay subscription.
     */
    public function cancelSubscription($subscriptionId, $cancelAtCycleEnd = false)
    {
        $cancelData = [
            'cancel_at_cycle_end' => $cancelAtCycleEnd ? 1 : 0,
        ];

        $response = Http::withBasicAuth($this->keyId, $this->keySecret)
            ->post($this->baseUrl . "/subscriptions/{$subscriptionId}/cancel", $cancelData);

        return $response->successful();
    }

    /**
     * Get subscription details.
     */
    public function getSubscription($subscriptionId)
    {
        $response = Http::withBasicAuth($this->keyId, $this->keySecret)
            ->get($this->baseUrl . "/subscriptions/{$subscriptionId}");

        if ($response->successful()) {
            return $response->json();
        }

        return null;
    }
}
