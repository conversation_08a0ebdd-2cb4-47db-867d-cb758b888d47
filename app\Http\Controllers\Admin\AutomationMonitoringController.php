<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\QueueMonitoringService;
use App\Services\AutomationAnalyticsService;
use App\Services\RecurringInvoiceService;
use App\Services\SmartPaymentReminderService;
use App\Services\TdsService;
use App\Services\WorkflowAutomationService;
use App\Models\WorkflowExecution;
use App\Models\RecurringInvoice;
use App\Models\FollowUp;
use App\Models\TdsRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AutomationMonitoringController extends Controller
{
    protected QueueMonitoringService $queueService;
    protected AutomationAnalyticsService $analyticsService;
    protected RecurringInvoiceService $recurringService;
    protected SmartPaymentReminderService $reminderService;
    protected TdsService $tdsService;
    protected WorkflowAutomationService $workflowService;

    public function __construct(
        QueueMonitoringService $queueService,
        AutomationAnalyticsService $analyticsService,
        RecurringInvoiceService $recurringService,
        SmartPaymentReminderService $reminderService,
        TdsService $tdsService,
        WorkflowAutomationService $workflowService
    ) {
        $this->queueService = $queueService;
        $this->analyticsService = $analyticsService;
        $this->recurringService = $recurringService;
        $this->reminderService = $reminderService;
        $this->tdsService = $tdsService;
        $this->workflowService = $workflowService;
    }

    /**
     * Show automation monitoring dashboard.
     */
    public function dashboard(Request $request)
    {
        $period = $request->get('period', '7_days');
        
        // Queue statistics
        $queueStats = $this->queueService->getQueueStatistics();
        $queueHealth = $this->queueService->getQueueHealth();
        $queuePerformance = $this->queueService->getPerformanceMetrics();
        
        // Automation statistics
        $automationStats = $this->getAutomationStatistics($period);
        $executionMetrics = $this->getExecutionMetrics($period);
        $errorAnalysis = $this->getErrorAnalysis($period);
        
        // Recent activities
        $recentExecutions = $this->getRecentExecutions();
        $failedJobs = $this->queueService->getFailedJobs(10);
        
        return view('admin.automation.dashboard', compact(
            'queueStats',
            'queueHealth',
            'queuePerformance',
            'automationStats',
            'executionMetrics',
            'errorAnalysis',
            'recentExecutions',
            'failedJobs',
            'period'
        ));
    }

    /**
     * Get automation statistics for the specified period.
     */
    protected function getAutomationStatistics(string $period): array
    {
        $days = $this->getPeriodDays($period);
        $startDate = Carbon::now()->subDays($days);

        return [
            'recurring_invoices' => [
                'total_processed' => RecurringInvoice::where('last_generated_at', '>=', $startDate)->count(),
                'successful' => RecurringInvoice::where('last_generated_at', '>=', $startDate)
                    ->where('status', 'active')->count(),
                'failed' => RecurringInvoice::where('last_generated_at', '>=', $startDate)
                    ->where('status', 'failed')->count(),
            ],
            'payment_reminders' => [
                'total_sent' => FollowUp::where('created_at', '>=', $startDate)
                    ->where('type', 'payment_reminder')->count(),
                'successful' => FollowUp::where('created_at', '>=', $startDate)
                    ->where('type', 'payment_reminder')
                    ->where('status', 'completed')->count(),
                'pending' => FollowUp::where('created_at', '>=', $startDate)
                    ->where('type', 'payment_reminder')
                    ->where('status', 'pending')->count(),
            ],
            'tds_calculations' => [
                'total_processed' => TdsRecord::where('created_at', '>=', $startDate)
                    ->where('is_auto_calculated', true)->count(),
                'successful' => TdsRecord::where('created_at', '>=', $startDate)
                    ->where('is_auto_calculated', true)
                    ->where('compliance_status', 'compliant')->count(),
                'pending_review' => TdsRecord::where('created_at', '>=', $startDate)
                    ->where('is_auto_calculated', true)
                    ->where('compliance_status', 'pending')->count(),
            ],
            'workflow_executions' => [
                'total_executed' => WorkflowExecution::where('created_at', '>=', $startDate)->count(),
                'successful' => WorkflowExecution::where('created_at', '>=', $startDate)
                    ->where('status', 'completed')->count(),
                'failed' => WorkflowExecution::where('created_at', '>=', $startDate)
                    ->where('status', 'failed')->count(),
            ],
        ];
    }

    /**
     * Get execution metrics for charts.
     */
    protected function getExecutionMetrics(string $period): array
    {
        $days = $this->getPeriodDays($period);
        $startDate = Carbon::now()->subDays($days);

        // Daily execution counts
        $dailyExecutions = WorkflowExecution::where('created_at', '>=', $startDate)
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as total'),
                DB::raw('SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as successful'),
                DB::raw('SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Performance metrics
        $avgExecutionTime = WorkflowExecution::where('created_at', '>=', $startDate)
            ->where('status', 'completed')
            ->avg('execution_time');

        return [
            'daily_executions' => $dailyExecutions,
            'avg_execution_time' => round($avgExecutionTime ?? 0, 2),
            'success_rate' => $this->calculateSuccessRate($startDate),
        ];
    }

    /**
     * Get error analysis data.
     */
    protected function getErrorAnalysis(string $period): array
    {
        $days = $this->getPeriodDays($period);
        $startDate = Carbon::now()->subDays($days);

        $errorTypes = WorkflowExecution::where('created_at', '>=', $startDate)
            ->where('status', 'failed')
            ->select('error_type', DB::raw('COUNT(*) as count'))
            ->groupBy('error_type')
            ->orderBy('count', 'desc')
            ->get();

        $errorTrends = WorkflowExecution::where('created_at', '>=', $startDate)
            ->where('status', 'failed')
            ->select(
                DB::raw('DATE(created_at) as date'),
                DB::raw('COUNT(*) as error_count')
            )
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        return [
            'error_types' => $errorTypes,
            'error_trends' => $errorTrends,
            'total_errors' => $errorTypes->sum('count'),
        ];
    }

    /**
     * Get recent automation executions.
     */
    protected function getRecentExecutions(): array
    {
        return WorkflowExecution::with(['workflow', 'user'])
            ->latest()
            ->take(20)
            ->get()
            ->toArray();
    }

    /**
     * Calculate success rate for the period.
     */
    protected function calculateSuccessRate(Carbon $startDate): float
    {
        $total = WorkflowExecution::where('created_at', '>=', $startDate)->count();
        $successful = WorkflowExecution::where('created_at', '>=', $startDate)
            ->where('status', 'completed')->count();

        return $total > 0 ? round(($successful / $total) * 100, 2) : 0;
    }

    /**
     * Convert period string to days.
     */
    protected function getPeriodDays(string $period): int
    {
        return match($period) {
            '24_hours' => 1,
            '7_days' => 7,
            '30_days' => 30,
            '90_days' => 90,
            default => 7,
        };
    }

    /**
     * Get queue management data via AJAX.
     */
    public function getQueueData(Request $request)
    {
        $type = $request->get('type', 'statistics');

        return match($type) {
            'statistics' => response()->json($this->queueService->getQueueStatistics()),
            'health' => response()->json($this->queueService->getQueueHealth()),
            'performance' => response()->json($this->queueService->getPerformanceMetrics()),
            'failed_jobs' => response()->json($this->queueService->getFailedJobs(20)),
            default => response()->json(['error' => 'Invalid type']),
        };
    }

    /**
     * Retry failed jobs.
     */
    public function retryFailedJobs(Request $request)
    {
        $jobIds = $request->get('job_ids', []);
        $retried = $this->queueService->retryFailedJobs($jobIds);

        return response()->json([
            'success' => true,
            'retried_count' => $retried,
            'message' => "Successfully retried {$retried} jobs"
        ]);
    }

    /**
     * Clear failed jobs.
     */
    public function clearFailedJobs(Request $request)
    {
        $jobIds = $request->get('job_ids', []);
        $cleared = $this->queueService->clearFailedJobs($jobIds);

        return response()->json([
            'success' => true,
            'cleared_count' => $cleared,
            'message' => "Successfully cleared {$cleared} failed jobs"
        ]);
    }

    /**
     * Get automation logs.
     */
    public function getLogs(Request $request)
    {
        $type = $request->get('type', 'all');
        $limit = $request->get('limit', 50);
        $page = $request->get('page', 1);

        $query = WorkflowExecution::with(['workflow', 'user']);

        if ($type !== 'all') {
            $query->where('workflow_type', $type);
        }

        $logs = $query->latest()
            ->paginate($limit, ['*'], 'page', $page);

        return response()->json($logs);
    }
}
