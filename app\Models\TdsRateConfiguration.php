<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TdsRateConfiguration extends Model
{
    use HasFactory;

    protected $fillable = [
        'service_type',
        'client_type',
        'tds_rate',
        'threshold_amount',
        'section_code',
        'description',
        'is_active',
        'financial_year',
        'conditions',
    ];

    protected function casts(): array
    {
        return [
            'tds_rate' => 'decimal:2',
            'threshold_amount' => 'decimal:2',
            'is_active' => 'boolean',
            'conditions' => 'array',
        ];
    }

    /**
     * Scope to get active configurations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get configurations for current financial year.
     */
    public function scopeCurrentYear($query)
    {
        $currentYear = $this->getCurrentFinancialYear();
        return $query->where('financial_year', $currentYear);
    }

    /**
     * Scope to get configurations by service type.
     */
    public function scopeForServiceType($query, string $serviceType)
    {
        return $query->where('service_type', $serviceType);
    }

    /**
     * Scope to get configurations by client type.
     */
    public function scopeForClientType($query, string $clientType)
    {
        return $query->where('client_type', $clientType);
    }

    /**
     * Get the applicable TDS rate for given criteria.
     */
    public static function getApplicableRate(string $serviceType, string $clientType, float $amount, string $financialYear = null): ?self
    {
        $financialYear = $financialYear ?? self::getCurrentFinancialYear();
        
        return self::active()
            ->where('financial_year', $financialYear)
            ->where('service_type', $serviceType)
            ->where('client_type', $clientType)
            ->where(function ($query) use ($amount) {
                $query->whereNull('threshold_amount')
                      ->orWhere('threshold_amount', '<=', $amount);
            })
            ->first();
    }

    /**
     * Check if conditions are met for this configuration.
     */
    public function conditionsMet(array $context): bool
    {
        if (empty($this->conditions)) {
            return true;
        }

        foreach ($this->conditions as $condition) {
            if (!$this->evaluateCondition($condition, $context)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate a single condition.
     */
    protected function evaluateCondition(array $condition, array $context): bool
    {
        $field = $condition['field'] ?? '';
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? '';
        
        $contextValue = $context[$field] ?? null;
        
        return match ($operator) {
            '=' => $contextValue == $value,
            '!=' => $contextValue != $value,
            '>' => $contextValue > $value,
            '>=' => $contextValue >= $value,
            '<' => $contextValue < $value,
            '<=' => $contextValue <= $value,
            'in' => in_array($contextValue, (array) $value),
            'not_in' => !in_array($contextValue, (array) $value),
            'contains' => str_contains($contextValue, $value),
            default => false,
        };
    }

    /**
     * Get current financial year.
     */
    protected static function getCurrentFinancialYear(): string
    {
        $currentDate = now();
        $year = $currentDate->year;
        
        // Financial year starts from April 1st
        if ($currentDate->month >= 4) {
            return $year . '-' . ($year + 1);
        } else {
            return ($year - 1) . '-' . $year;
        }
    }

    /**
     * Get all service types.
     */
    public static function getServiceTypes(): array
    {
        return [
            'professional_services' => 'Professional Services',
            'technical_services' => 'Technical Services',
            'consultancy' => 'Consultancy',
            'software_development' => 'Software Development',
            'design_services' => 'Design Services',
            'marketing_services' => 'Marketing Services',
            'legal_services' => 'Legal Services',
            'accounting_services' => 'Accounting Services',
            'other' => 'Other Services',
        ];
    }

    /**
     * Get all client types.
     */
    public static function getClientTypes(): array
    {
        return [
            'individual' => 'Individual',
            'company' => 'Company',
            'partnership' => 'Partnership',
            'government' => 'Government',
            'ngo' => 'NGO',
            'trust' => 'Trust',
            'other' => 'Other',
        ];
    }

    /**
     * Get common TDS sections.
     */
    public static function getTdsSections(): array
    {
        return [
            '194J' => 'Section 194J - Professional/Technical Services',
            '194C' => 'Section 194C - Contracts',
            '194I' => 'Section 194I - Rent',
            '194H' => 'Section 194H - Commission/Brokerage',
            '194G' => 'Section 194G - Commission on Sale of Lottery Tickets',
            '194A' => 'Section 194A - Interest other than on Securities',
            '194' => 'Section 194 - Interest on Securities',
        ];
    }
}
