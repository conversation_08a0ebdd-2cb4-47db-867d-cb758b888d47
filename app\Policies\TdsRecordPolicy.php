<?php

namespace App\Policies;

use App\Models\TdsRecord;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class TdsRecordPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return false;
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TdsRecord $tdsRecord): bool
    {
        return $user->can('view tds') && $user->id === $tdsRecord->user_id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->can('create tds');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TdsRecord $tdsRecord): bool
    {
        return $user->can('edit tds') && $user->id === $tdsRecord->user_id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TdsRecord $tdsRecord): bool
    {
        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TdsRecord $tdsRecord): bool
    {
        return false;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TdsRecord $tdsRecord): bool
    {
        return false;
    }
}
