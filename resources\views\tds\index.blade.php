<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">TDS Records</h1>
                <p class="text-gray-600 mt-1">Track and manage Tax Deducted at Source for all clients</p>
            </div>
            <div class="flex items-center space-x-3">
                <x-ui.button href="{{ route('tds.export') }}{{ request()->getQueryString() ? '?' . request()->getQueryString() : '' }}"
                           variant="success" icon="fas fa-file-excel">
                    Export to Excel
                </x-ui.button>
                <x-ui.button href="{{ route('tds.summary') }}" variant="primary" icon="fas fa-chart-bar">
                    TDS Summary
                </x-ui.button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Search and Filter -->
            <x-ui.card class="mb-6">
                <form method="GET" action="{{ route('tds.index') }}" class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <x-ui.input
                            name="search"
                            value="{{ request('search') }}"
                            placeholder="Search by client name or certificate number..."
                            icon="fas fa-search" />
                    </div>
                    <div>
                        <x-ui.input type="select" name="financial_year">
                            <option value="">All Financial Years</option>
                            @foreach($financialYears as $year)
                                <option value="{{ $year }}" {{ request('financial_year') === $year ? 'selected' : '' }}>
                                    {{ $year }}
                                </option>
                            @endforeach
                        </x-ui.input>
                    </div>
                    <div>
                        <x-ui.input type="select" name="client_id">
                            <option value="">All Clients</option>
                            @foreach($clients as $client)
                                <option value="{{ $client->id }}" {{ request('client_id') == $client->id ? 'selected' : '' }}>
                                    {{ $client->name }}
                                </option>
                            @endforeach
                        </x-ui.input>
                    </div>
                    <div class="flex gap-2">
                        <x-ui.button type="submit" variant="primary" icon="fas fa-search">
                            Search
                        </x-ui.button>
                        <x-ui.button href="{{ route('tds.index') }}" variant="secondary" icon="fas fa-times">
                            Clear
                        </x-ui.button>
                    </div>
                </form>
            </x-ui.card>

            <!-- TDS Summary Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"></path>
                                        <path fill-rule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Total TDS</div>
                                <div class="text-2xl font-bold text-gray-900">₹{{ number_format($totalTdsAmount, 2) }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Current FY</div>
                                <div class="text-2xl font-bold text-gray-900">₹{{ number_format($totalTdsAmount, 2) }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-yellow-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">With Certificates</div>
                                <div class="text-2xl font-bold text-gray-900">{{ $withCertificates }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                                    <svg class="w-5 h-5 text-red-600" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-500">Pending Certificates</div>
                                <div class="text-2xl font-bold text-gray-900">{{ $pendingCertificates }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TDS Records Table -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    @if($tdsRecords->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Invoice
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Client
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            TDS Amount
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Financial Year
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Certificate
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($tdsRecords as $record)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">{{ $record->invoice->invoice_number }}</div>
                                                <div class="text-sm text-gray-500">{{ $record->invoice->invoice_date->format('d/m/Y') }}</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ $record->client->name }}</div>
                                                @if($record->client->company_name)
                                                    <div class="text-sm text-gray-500">{{ $record->client->company_name }}</div>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">₹{{ number_format($record->tds_amount, 2) }}</div>
                                                <div class="text-sm text-gray-500">{{ $record->tds_percentage }}%</div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $record->financial_year }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($record->certificate_number)
                                                    <div class="text-sm font-medium text-gray-900">{{ $record->certificate_number }}</div>
                                                    @if($record->certificate_date)
                                                        <div class="text-sm text-gray-500">{{ $record->certificate_date->format('d/m/Y') }}</div>
                                                    @endif
                                                @else
                                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                                                        Pending
                                                    </span>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="{{ route('tds.show', $record) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                                    <a href="{{ route('invoices.show', $record->invoice) }}" class="text-blue-600 hover:text-blue-900">Invoice</a>
                                                    @if(!$record->certificate_number)
                                                        <button type="button" onclick="updateCertificate({{ $record->id }})" class="text-green-600 hover:text-green-900">
                                                            Add Certificate
                                                        </button>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="mt-6">
                            {{ $tdsRecords->links() }}
                        </div>
                    @else
                        <div class="text-center py-12">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No TDS records</h3>
                            <p class="mt-1 text-sm text-gray-500">TDS records will appear here when invoices with TDS are created.</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Certificate Update Modal -->
    <div id="certificateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Update TDS Certificate</h3>
                <form id="certificateForm" method="POST">
                    @csrf
                    @method('PATCH')
                    <div class="mb-4">
                        <label for="certificate_number" class="block text-sm font-medium text-gray-700">Certificate Number</label>
                        <input type="text" name="certificate_number" id="certificate_number" required
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="mb-4">
                        <label for="certificate_date" class="block text-sm font-medium text-gray-700">Certificate Date</label>
                        <input type="date" name="certificate_date" id="certificate_date" required
                               class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeCertificateModal()" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                            Cancel
                        </button>
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                            Update Certificate
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        function updateCertificate(recordId) {
            document.getElementById('certificateForm').action = `/tds/${recordId}/certificate`;
            document.getElementById('certificateModal').classList.remove('hidden');
        }

        function closeCertificateModal() {
            document.getElementById('certificateModal').classList.add('hidden');
            document.getElementById('certificateForm').reset();
        }

        // Close modal when clicking outside
        document.getElementById('certificateModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeCertificateModal();
            }
        });
    </script>
</x-app-layout>
