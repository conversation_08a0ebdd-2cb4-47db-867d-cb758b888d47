<?php

namespace App\Console\Commands;

use App\Services\TdsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessAutomatedTdsCalculations extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'tds:process-automated-calculations 
                            {--dry-run : Run without making changes}
                            {--user-id= : Process for specific user only}';

    /**
     * The console command description.
     */
    protected $description = 'Process automated TDS calculations for pending invoices';

    protected TdsService $tdsService;

    /**
     * Create a new command instance.
     */
    public function __construct(TdsService $tdsService)
    {
        parent::__construct();
        $this->tdsService = $tdsService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting automated TDS calculations...');
        
        $startTime = microtime(true);
        
        try {
            if ($this->option('dry-run')) {
                $this->warn('Running in DRY-RUN mode - no changes will be made');
            }

            $results = $this->tdsService->processAutomatedCalculations();

            $this->displayResults($results);
            
            $executionTime = round(microtime(true) - $startTime, 2);
            $this->info("Automated TDS calculations completed in {$executionTime} seconds");

            // Log the results
            Log::info('Automated TDS calculations completed', [
                'results' => $results,
                'execution_time' => $executionTime,
                'dry_run' => $this->option('dry-run'),
            ]);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Failed to process automated TDS calculations: ' . $e->getMessage());
            
            Log::error('Automated TDS calculations failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * Display processing results.
     */
    protected function displayResults(array $results): void
    {
        $this->newLine();
        $this->info('=== Processing Results ===');
        
        $this->table(
            ['Metric', 'Count'],
            [
                ['Invoices Processed', $results['processed']],
                ['TDS Calculated', $results['calculated']],
                ['Failed', $results['failed']],
            ]
        );

        if (!empty($results['errors'])) {
            $this->newLine();
            $this->error('Errors encountered:');
            
            foreach ($results['errors'] as $error) {
                $this->line("  • Invoice ID {$error['invoice_id']}: {$error['error']}");
            }
        }

        // Calculate success rate
        if ($results['processed'] > 0) {
            $successRate = round(($results['calculated'] / $results['processed']) * 100, 1);
            $this->newLine();
            $this->info("Success Rate: {$successRate}%");
        }
    }
}
