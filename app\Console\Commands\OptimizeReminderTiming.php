<?php

namespace App\Console\Commands;

use App\Services\SmartPaymentReminderService;
use Illuminate\Console\Command;

class OptimizeReminderTiming extends Command
{
    protected $signature = 'reminders:optimize-timing';
    protected $description = 'Optimize reminder timing using AI and client behavior analysis';

    protected $smartReminderService;

    public function __construct(SmartPaymentReminderService $smartReminderService)
    {
        parent::__construct();
        $this->smartReminderService = $smartReminderService;
    }

    public function handle(): int
    {
        $this->info('Optimizing reminder timing...');

        $results = $this->smartReminderService->optimizeReminderTiming();

        $this->info("Optimized {$results['optimized']} reminders successfully.");
        
        if ($results['failed'] > 0) {
            $this->warn("Failed to optimize {$results['failed']} reminders.");
            foreach ($results['errors'] as $error) {
                $this->error("Reminder ID {$error['reminder_id']}: {$error['error']}");
            }
        }

        return Command::SUCCESS;
    }
}
