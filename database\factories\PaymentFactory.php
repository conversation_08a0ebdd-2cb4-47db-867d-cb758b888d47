<?php

namespace Database\Factories;

use App\Models\Payment;
use App\Models\User;
use App\Models\UserSubscription;
use Illuminate\Database\Eloquent\Factories\Factory;

class PaymentFactory extends Factory
{
    protected $model = Payment::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'user_subscription_id' => UserSubscription::factory(),
            'amount' => 19.99,
            'currency' => 'USD',
            'status' => 'completed',
            'gateway' => 'paypal',
            'gateway_payment_id' => 'test_payment_id',
            'gateway_subscription_id' => 'test_subscription_id',
            'metadata' => json_encode(['source' => 'test']),
        ];
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'gateway_payment_id' => null,
        ]);
    }

    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'completed',
            'gateway_payment_id' => $this->faker->uuid(),
            'completed_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'gateway_payment_id' => $this->faker->uuid(),
            'failed_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'failure_reason' => $this->faker->sentence(),
        ]);
    }

    public function paypal(): static
    {
        return $this->state(fn (array $attributes) => [
            'gateway' => 'paypal',
            'gateway_payment_id' => 'PAYID-' . strtoupper($this->faker->bothify('???????')),
            'gateway_subscription_id' => 'I-' . strtoupper($this->faker->bothify('??????????')),
        ]);
    }

    public function razorpay(): static
    {
        return $this->state(fn (array $attributes) => [
            'gateway' => 'razorpay',
            'gateway_payment_id' => 'pay_' . $this->faker->bothify('??????????'),
            'gateway_subscription_id' => 'sub_' . $this->faker->bothify('??????????'),
        ]);
    }
}
