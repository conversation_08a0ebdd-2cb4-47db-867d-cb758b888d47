<?php

namespace App\Http\Controllers;

use App\Models\Client;
use App\Models\Contract;
use App\Models\ContractTemplate;
use App\Services\ContractService;
use App\Services\ClientService;
use App\Services\PlanChecker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Barryvdh\DomPDF\Facade\Pdf;

class ContractController extends Controller
{
    protected ContractService $contractService;
    protected ClientService $clientService;

    public function __construct(ContractService $contractService, ClientService $clientService)
    {
        $this->contractService = $contractService;
        $this->clientService = $clientService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $contracts = $this->contractService->getContractsForUser(Auth::id(), $request);
        $clients = $this->clientService->getClientsForUser(Auth::id());

        return view('contracts.index', compact('contracts', 'clients'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Check if user can create contracts
        if (!$this->contractService->canCreateContract(Auth::id())) {
            return redirect()->route('upgrade')
                ->with('error', 'You have reached your contract limit. Upgrade to Pro for unlimited contracts.');
        }

        $clients = $this->clientService->getClientsForUser(Auth::id());
        $templates = ContractTemplate::active()->get();

        return view('contracts.create', compact('clients', 'templates'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        // Check if user can create contracts
        if (!$this->contractService->canCreateContract(Auth::id())) {
            return redirect()->route('contracts.index')
                ->with('error', 'You have reached your contract limit. Upgrade to Pro for unlimited contracts.');
        }

        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
            'contract_template_id' => 'required|exists:contract_templates,id',
            'title' => 'required|string|max:255',
            'variables' => 'nullable|array',
        ]);

        try {
            $contract = $this->contractService->createContract($validated);

            return redirect()->route('contracts.show', $contract)
                            ->with('success', 'Contract created successfully.');
        } catch (\Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['variables' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(Contract $contract)
    {
        $this->authorize('view', $contract);
        $contract->load(['client', 'contractTemplate']);

        return view('contracts.show', compact('contract'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Contract $contract)
    {
        $this->authorize('update', $contract);

        if ($contract->status === 'signed') {
            return redirect()->route('contracts.show', $contract)
                            ->with('error', 'Cannot edit signed contracts.');
        }

        $clients = $this->clientService->getClientsForUser(Auth::id());
        $templates = ContractTemplate::active()->get();

        return view('contracts.edit', compact('contract', 'clients', 'templates'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Contract $contract)
    {
        $this->authorize('update', $contract);

        if ($contract->status === 'signed') {
            return redirect()->route('contracts.show', $contract)
                            ->with('error', 'Cannot edit signed contracts.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'variables' => 'nullable|array',
        ]);

        $this->contractService->updateContract($contract, $validated);

        return redirect()->route('contracts.show', $contract)
                        ->with('success', 'Contract updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Contract $contract)
    {
        $this->authorize('delete', $contract);

        $result = $this->contractService->deleteContract($contract);

        if (!$result['success']) {
            return redirect()->route('contracts.index')
                            ->with('error', $result['message']);
        }

        return redirect()->route('contracts.index')
                        ->with('success', 'Contract deleted successfully.');
    }

    /**
     * Download contract as PDF.
     */
    public function download(Contract $contract)
    {
        $this->authorize('view', $contract);
        $contract->load(['client', 'user']);

        try {
            $pdf = Pdf::loadView('contracts.pdf', compact('contract'));
            $filename = str_replace(' ', '_', $contract->title) . '.pdf';

            return $pdf->download($filename);
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to generate PDF. Please try again or contact support.');
        }
    }

    /**
     * Send contract to client.
     */
    public function send(Contract $contract)
    {
        $this->authorize('update', $contract);

        if ($contract->status !== 'draft') {
            return redirect()->back()
                            ->with('error', 'Only draft contracts can be sent.');
        }

        $this->contractService->sendContract($contract);

        return redirect()->back()
                        ->with('success', 'Contract sent to client successfully.');
    }

    /**
     * Mark contract as signed.
     */
    public function markAsSigned(Contract $contract)
    {
        $this->authorize('update', $contract);

        $this->contractService->markAsSigned($contract);

        return redirect()->back()
                        ->with('success', 'Contract marked as signed.');
    }

    /**
     * Get template content for preview.
     */
    public function getTemplate(ContractTemplate $template)
    {
        return response()->json([
            'content' => $template->content,
            'variables' => $template->variables,
        ]);
    }
}
