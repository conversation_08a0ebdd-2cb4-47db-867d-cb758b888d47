<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkflowMetric extends Model
{
    use HasFactory;

    protected $fillable = [
        'workflow_id',
        'metric_date',
        'executions_count',
        'successful_executions',
        'failed_executions',
        'average_execution_time',
        'success_rate',
        'performance_data',
        'error_summary',
    ];

    protected function casts(): array
    {
        return [
            'metric_date' => 'date',
            'executions_count' => 'integer',
            'successful_executions' => 'integer',
            'failed_executions' => 'integer',
            'average_execution_time' => 'decimal:3',
            'success_rate' => 'decimal:2',
            'performance_data' => 'array',
            'error_summary' => 'array',
        ];
    }

    /**
     * Get the workflow that owns the metric.
     */
    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    /**
     * Calculate and update metrics for a workflow on a specific date.
     */
    public static function updateMetricsForWorkflow(int $workflowId, \Carbon\Carbon $date): self
    {
        $executions = WorkflowExecution::where('workflow_id', $workflowId)
            ->whereDate('created_at', $date)
            ->get();

        $executionsCount = $executions->count();
        $successfulExecutions = $executions->where('status', 'completed')->count();
        $failedExecutions = $executions->where('status', 'failed')->count();
        
        $averageExecutionTime = null;
        if ($executionsCount > 0) {
            $totalTime = $executions->whereNotNull('execution_time_seconds')->sum('execution_time_seconds');
            $averageExecutionTime = $totalTime / $executionsCount;
        }

        $successRate = $executionsCount > 0 ? ($successfulExecutions / $executionsCount) * 100 : 0;

        $performanceData = [
            'peak_execution_time' => $executions->max('execution_time_seconds'),
            'min_execution_time' => $executions->min('execution_time_seconds'),
            'total_actions_executed' => $executions->sum('actions_executed'),
            'total_actions_failed' => $executions->sum('actions_failed'),
            'retry_count' => $executions->sum('retry_count'),
        ];

        $errorSummary = static::generateErrorSummary($executions->where('status', 'failed'));

        return static::updateOrCreate(
            [
                'workflow_id' => $workflowId,
                'metric_date' => $date->toDateString(),
            ],
            [
                'executions_count' => $executionsCount,
                'successful_executions' => $successfulExecutions,
                'failed_executions' => $failedExecutions,
                'average_execution_time' => $averageExecutionTime,
                'success_rate' => round($successRate, 2),
                'performance_data' => $performanceData,
                'error_summary' => $errorSummary,
            ]
        );
    }

    /**
     * Generate error summary from failed executions.
     */
    protected static function generateErrorSummary($failedExecutions): array
    {
        $errorCounts = [];
        $errorTypes = [];

        foreach ($failedExecutions as $execution) {
            if ($execution->error_message) {
                $errorType = static::categorizeError($execution->error_message);
                $errorTypes[$errorType] = ($errorTypes[$errorType] ?? 0) + 1;
                
                $errorKey = substr($execution->error_message, 0, 100);
                $errorCounts[$errorKey] = ($errorCounts[$errorKey] ?? 0) + 1;
            }
        }

        return [
            'error_types' => $errorTypes,
            'common_errors' => array_slice($errorCounts, 0, 5, true),
            'total_unique_errors' => count($errorCounts),
        ];
    }

    /**
     * Categorize error by type.
     */
    protected static function categorizeError(string $errorMessage): string
    {
        $errorMessage = strtolower($errorMessage);

        if (str_contains($errorMessage, 'timeout') || str_contains($errorMessage, 'time out')) {
            return 'timeout';
        }
        
        if (str_contains($errorMessage, 'connection') || str_contains($errorMessage, 'network')) {
            return 'network';
        }
        
        if (str_contains($errorMessage, 'permission') || str_contains($errorMessage, 'unauthorized')) {
            return 'permission';
        }
        
        if (str_contains($errorMessage, 'validation') || str_contains($errorMessage, 'invalid')) {
            return 'validation';
        }
        
        if (str_contains($errorMessage, 'not found') || str_contains($errorMessage, '404')) {
            return 'not_found';
        }
        
        if (str_contains($errorMessage, 'database') || str_contains($errorMessage, 'sql')) {
            return 'database';
        }

        return 'other';
    }

    /**
     * Get performance trend for workflow.
     */
    public static function getPerformanceTrend(int $workflowId, int $days = 30): array
    {
        $metrics = static::where('workflow_id', $workflowId)
            ->where('metric_date', '>=', now()->subDays($days))
            ->orderBy('metric_date')
            ->get();

        return [
            'dates' => $metrics->pluck('metric_date')->map(fn($date) => $date->format('Y-m-d'))->toArray(),
            'success_rates' => $metrics->pluck('success_rate')->toArray(),
            'execution_counts' => $metrics->pluck('executions_count')->toArray(),
            'average_execution_times' => $metrics->pluck('average_execution_time')->toArray(),
            'trend_analysis' => static::analyzeTrend($metrics),
        ];
    }

    /**
     * Analyze performance trend.
     */
    protected static function analyzeTrend($metrics): array
    {
        if ($metrics->count() < 2) {
            return ['trend' => 'insufficient_data'];
        }

        $recentMetrics = $metrics->take(-7); // Last 7 days
        $olderMetrics = $metrics->take(-14)->take(7); // Previous 7 days

        $recentAvgSuccessRate = $recentMetrics->avg('success_rate');
        $olderAvgSuccessRate = $olderMetrics->avg('success_rate');

        $recentAvgExecutionTime = $recentMetrics->avg('average_execution_time');
        $olderAvgExecutionTime = $olderMetrics->avg('average_execution_time');

        $successRateTrend = $recentAvgSuccessRate > $olderAvgSuccessRate ? 'improving' : 
                           ($recentAvgSuccessRate < $olderAvgSuccessRate ? 'declining' : 'stable');

        $performanceTrend = $recentAvgExecutionTime < $olderAvgExecutionTime ? 'improving' : 
                           ($recentAvgExecutionTime > $olderAvgExecutionTime ? 'declining' : 'stable');

        return [
            'success_rate_trend' => $successRateTrend,
            'performance_trend' => $performanceTrend,
            'recent_avg_success_rate' => round($recentAvgSuccessRate, 2),
            'older_avg_success_rate' => round($olderAvgSuccessRate, 2),
            'recent_avg_execution_time' => round($recentAvgExecutionTime, 3),
            'older_avg_execution_time' => round($olderAvgExecutionTime, 3),
        ];
    }

    /**
     * Get workflow performance score.
     */
    public function getPerformanceScore(): float
    {
        $successRateScore = $this->success_rate * 0.4; // 40% weight
        $executionTimeScore = $this->getExecutionTimeScore() * 0.3; // 30% weight
        $reliabilityScore = $this->getReliabilityScore() * 0.3; // 30% weight

        return min(100, $successRateScore + $executionTimeScore + $reliabilityScore);
    }

    /**
     * Get execution time score (lower is better).
     */
    protected function getExecutionTimeScore(): float
    {
        if (!$this->average_execution_time) {
            return 30; // Default score
        }

        // Score based on execution time (lower time = higher score)
        if ($this->average_execution_time <= 1) {
            return 30;
        } elseif ($this->average_execution_time <= 5) {
            return 25;
        } elseif ($this->average_execution_time <= 10) {
            return 20;
        } elseif ($this->average_execution_time <= 30) {
            return 15;
        } else {
            return 10;
        }
    }

    /**
     * Get reliability score based on error patterns.
     */
    protected function getReliabilityScore(): float
    {
        $errorTypes = $this->error_summary['error_types'] ?? [];
        $totalErrors = array_sum($errorTypes);

        if ($totalErrors === 0) {
            return 30;
        }

        // Penalize based on error diversity and frequency
        $errorDiversity = count($errorTypes);
        $diversityPenalty = min(10, $errorDiversity * 2);
        $frequencyPenalty = min(20, $totalErrors);

        return max(0, 30 - $diversityPenalty - $frequencyPenalty);
    }

    /**
     * Scope for recent metrics.
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('metric_date', '>=', now()->subDays($days));
    }

    /**
     * Scope for high performing workflows.
     */
    public function scopeHighPerforming($query, float $minSuccessRate = 90.0)
    {
        return $query->where('success_rate', '>=', $minSuccessRate);
    }

    /**
     * Scope for workflows needing attention.
     */
    public function scopeNeedsAttention($query, float $maxSuccessRate = 70.0)
    {
        return $query->where('success_rate', '<=', $maxSuccessRate)
                    ->orWhere('failed_executions', '>', 0);
    }
}
