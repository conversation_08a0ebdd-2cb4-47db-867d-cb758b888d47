<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Time Tracking</h1>
                <p class="text-gray-600 mt-1">Track your time and manage productivity</p>
            </div>
            <div class="flex space-x-3">
                <?php if(!$activeTimer): ?>
                    <?php if($projects->count() > 0): ?>
                        <button onclick="openStartTimerModal()"
                                class="bg-green-600 hover:bg-green-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2">
                            <i class="fas fa-play mr-2"></i>Start Timer
                        </button>
                    <?php else: ?>
                        <button disabled
                                class="bg-gray-400 text-white font-medium py-2.5 px-4 rounded-lg cursor-not-allowed opacity-60">
                            <i class="fas fa-play mr-2"></i>Start Timer
                        </button>
                    <?php endif; ?>
                <?php endif; ?>
                <a href="<?php echo e(route('time-tracking.create')); ?>"
                   class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                    <i class="fas fa-plus mr-2"></i>Manual Entry
                </a>
            </div>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

            <!-- Active Timer Section -->
            <?php if($activeTimer): ?>
                <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl p-6 mb-6 shadow-sm">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <div class="w-4 h-4 bg-green-500 rounded-full animate-pulse mr-4"></div>
                            <div>
                                <h3 class="font-semibold text-green-800 text-lg">Timer Running</h3>
                                <p class="text-green-700 font-medium">
                                    <?php echo e($activeTimer->project->name); ?>

                                    <?php if($activeTimer->task): ?>
                                        - <?php echo e($activeTimer->task->title); ?>

                                    <?php endif; ?>
                                </p>
                                <p class="text-sm text-green-600">
                                    <?php echo e($activeTimer->description); ?>

                                </p>
                                <div class="flex items-center justify-between text-xs text-green-500 mt-1">
                                    <span>Started: <?php echo e($activeTimer->start_time->format('M j, Y g:i A')); ?></span>
                                    <?php if($activeTimer->hourly_rate): ?>
                                        <span class="bg-green-100 text-green-700 px-2 py-1 rounded-full font-medium">
                                            ₹<?php echo e(number_format($activeTimer->hourly_rate, 0)); ?>/hr
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="text-center">
                                <div class="text-2xl font-mono font-bold text-green-800" id="timer-display">
                                    <?php echo e($activeTimer->getElapsedTimeAttribute()); ?>

                                </div>
                                <div class="text-xs text-green-600">Elapsed Time</div>
                            </div>
                            <div class="flex space-x-2">
                                <button onclick="pauseTimer()"
                                        class="bg-yellow-600 hover:bg-yellow-700 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2">
                                    <i class="fas fa-pause mr-2"></i>Pause
                                </button>
                                <button onclick="stopTimer()"
                                        class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2">
                                    <i class="fas fa-stop mr-2"></i>Stop
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- No Projects State -->
            <?php if(!$activeTimer && $projects->count() === 0): ?>
                <div class="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-6">
                    <div class="text-center">
                        <div class="w-16 h-16 mx-auto mb-4 bg-yellow-100 rounded-full flex items-center justify-center">
                            <i class="fas fa-project-diagram text-yellow-600 text-2xl"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-yellow-800 mb-2">No Projects Available</h3>
                        <p class="text-yellow-700 mb-4">You need to create a project before you can start tracking time.</p>
                        <a href="<?php echo e(route('projects.create')); ?>"
                           class="inline-flex items-center px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white font-medium rounded-lg transition-colors">
                            <i class="fas fa-plus mr-2"></i>Create Your First Project
                        </a>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Time Entries List -->
                    <?php if($timeEntries->count() > 0): ?>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Date & Time
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Project
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Description
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Duration
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Rate
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Billable
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <?php $__currentLoopData = $timeEntries; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $entry): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <div><?php echo e($entry->start_time->format('M j, Y')); ?></div>
                                                <div class="text-xs text-gray-500">
                                                    <?php echo e($entry->start_time->format('g:i A')); ?>

                                                    <?php if($entry->end_time): ?>
                                                        - <?php echo e($entry->end_time->format('g:i A')); ?>

                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">
                                                    <?php echo e($entry->project->name); ?>

                                                </div>
                                                <?php if($entry->task): ?>
                                                    <div class="text-xs text-gray-500">
                                                        <?php echo e($entry->task->title); ?>

                                                    </div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 text-sm text-gray-900">
                                                <?php echo e($entry->description); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php echo e($entry->getFormattedDurationAttribute()); ?>

                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                <?php if($entry->hourly_rate): ?>
                                                    ₹<?php echo e(number_format($entry->hourly_rate, 0)); ?>/hr
                                                <?php else: ?>
                                                    <span class="text-gray-400">-</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <?php if($entry->is_billable): ?>
                                                    <span class="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                                                        Billable
                                                    </span>
                                                <?php else: ?>
                                                    <span class="px-2 py-1 text-xs font-medium bg-gray-100 text-gray-800 rounded-full">
                                                        Non-billable
                                                    </span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex space-x-2">
                                                    <a href="<?php echo e(route('time-tracking.edit', $entry)); ?>" 
                                                       class="text-blue-600 hover:text-blue-900">
                                                        <i class="fas fa-edit"></i>
                                                    </a>
                                                    <form method="POST" action="<?php echo e(route('time-tracking.destroy', $entry)); ?>" 
                                                          class="inline" onsubmit="return confirm('Are you sure?')">
                                                        <?php echo csrf_field(); ?>
                                                        <?php echo method_field('DELETE'); ?>
                                                        <button type="submit" class="text-red-600 hover:text-red-900">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>

                        <div class="mt-6">
                            <?php echo e($timeEntries->links()); ?>

                        </div>
                    <?php else: ?>
                        <div class="text-center py-12">
                            <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                <i class="fas fa-clock text-3xl text-gray-400"></i>
                            </div>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No time entries yet</h3>
                            <p class="text-gray-500 mb-6">Start tracking your time to see entries here.</p>
                            <?php if($projects->count() > 0): ?>
                                <a href="<?php echo e(route('time-tracking.create')); ?>" 
                                   class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg transition-colors">
                                    <i class="fas fa-plus mr-2"></i>Add Time Entry
                                </a>
                            <?php else: ?>
                                <p class="text-sm text-gray-400">Create a project first to start tracking time.</p>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- Start Timer Modal -->
    <div id="startTimerModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
        <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div class="mt-3">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Start Timer</h3>
                    <button onclick="closeStartTimerModal()" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <form id="startTimerForm">
                    <?php echo csrf_field(); ?>
                    <div class="mb-4">
                        <label for="project_id" class="block text-sm font-medium text-gray-700 mb-2">Project *</label>
                        <select name="project_id" id="project_id" required
                                class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">Select a project</option>
                            <?php $__empty_1 = true; $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                <option value="<?php echo e($project->id); ?>"><?php echo e($project->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                <option value="" disabled>No projects available</option>
                            <?php endif; ?>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="task_id" class="block text-sm font-medium text-gray-700 mb-2">Task (Optional)</label>
                        <select name="task_id" id="task_id"
                                class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500">
                            <option value="">No specific task</option>
                        </select>
                    </div>

                    <div class="mb-4">
                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description (Optional)</label>
                        <input type="text" name="description" id="description"
                               class="w-full border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                               placeholder="What are you working on? (optional)">
                    </div>

                    <div class="mb-6">
                        <label for="hourly_rate" class="block text-sm font-medium text-gray-700 mb-2">Hourly Rate</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <span class="text-gray-500 sm:text-sm">₹</span>
                            </div>
                            <input type="number" name="hourly_rate" id="hourly_rate" step="0.01" min="0"
                                   class="w-full pl-8 border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500"
                                   placeholder="0.00">
                        </div>
                        <p class="mt-1 text-xs text-gray-500">Rate will be auto-filled from project settings</p>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button type="button" onclick="closeStartTimerModal()"
                                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            Cancel
                        </button>
                        <button type="submit"
                                class="px-4 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                            <i class="fas fa-play mr-2"></i>Start Timer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <?php $__env->startPush('scripts'); ?>
    <script>
        // Modal functions
        function openStartTimerModal() {
            document.getElementById('startTimerModal').classList.remove('hidden');
        }

        function closeStartTimerModal() {
            document.getElementById('startTimerModal').classList.add('hidden');
            document.getElementById('startTimerForm').reset();
            document.getElementById('task_id').innerHTML = '<option value="">No specific task</option>';
            document.getElementById('hourly_rate').value = '';
        }

        // Load tasks and hourly rate when project is selected
        document.getElementById('project_id').addEventListener('change', function() {
            const projectId = this.value;
            const taskSelect = document.getElementById('task_id');
            const hourlyRateInput = document.getElementById('hourly_rate');

            taskSelect.innerHTML = '<option value="">No specific task</option>';
            hourlyRateInput.value = '';

            if (projectId) {
                // Load tasks
                fetch(`/projects/${projectId}/tasks`)
                    .then(response => response.json())
                    .then(tasks => {
                        tasks.forEach(task => {
                            const option = document.createElement('option');
                            option.value = task.id;
                            option.textContent = task.title;
                            taskSelect.appendChild(option);
                        });
                    })
                    .catch(error => console.error('Error loading tasks:', error));

                // Load project details for hourly rate
                fetch(`/projects/${projectId}/details`)
                    .then(response => response.json())
                    .then(project => {
                        if (project.hourly_rate) {
                            hourlyRateInput.value = project.hourly_rate;
                        }
                    })
                    .catch(error => console.error('Error loading project details:', error));
            }
        });

        // Start timer form submission
        document.getElementById('startTimerForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            fetch('<?php echo e(route("time-tracking.start")); ?>', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    closeStartTimerModal();
                    location.reload();
                } else {
                    alert(data.message || 'Error starting timer');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error starting timer');
            });
        });

        // Timer functionality
        function stopTimer() {
            if (confirm('Are you sure you want to stop the timer?')) {
                fetch('<?php echo e(route("time-tracking.stop")); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        location.reload();
                    } else {
                        alert(data.message || 'Error stopping timer');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error stopping timer');
                });
            }
        }

        function pauseTimer() {
            fetch('<?php echo e(route("time-tracking.pause")); ?>', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert(data.message || 'Error pausing timer');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error pausing timer');
            });
        }

        // Update timer display every second
        <?php if($activeTimer): ?>
        setInterval(function() {
            const startTime = new Date('<?php echo e($activeTimer->start_time->toISOString()); ?>');
            const now = new Date();
            const elapsed = Math.floor((now - startTime) / 1000);

            const hours = Math.floor(elapsed / 3600);
            const minutes = Math.floor((elapsed % 3600) / 60);
            const seconds = elapsed % 60;

            const display = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
            document.getElementById('timer-display').textContent = display;
        }, 1000);
        <?php endif; ?>

        // Close modal when clicking outside
        document.getElementById('startTimerModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeStartTimerModal();
            }
        });
    </script>
    <?php $__env->stopPush(); ?>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\freeligo\resources\views/time-tracking/index.blade.php ENDPATH**/ ?>