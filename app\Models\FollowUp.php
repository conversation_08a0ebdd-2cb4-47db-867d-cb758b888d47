<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class FollowUp extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'invoice_id',
        'type',
        'message',
        'method',
        'delivery_channels',
        'status',
        'scheduled_at',
        'sent_at',
        'effectiveness_score',
        'client_response_data',
        'client_viewed_at',
        'client_responded_at',
        'response_type',
        'ai_insights',
        'auto_generated',
        'template_used',
        'personalization_data',
        'retry_count',
        'optimal_send_time',
    ];

    protected function casts(): array
    {
        return [
            'scheduled_at' => 'datetime',
            'sent_at' => 'datetime',
            'client_viewed_at' => 'datetime',
            'client_responded_at' => 'datetime',
            'optimal_send_time' => 'datetime',
            'auto_generated' => 'boolean',
            'effectiveness_score' => 'decimal:2',
            'retry_count' => 'integer',
            'delivery_channels' => 'array',
            'client_response_data' => 'array',
            'ai_insights' => 'array',
            'personalization_data' => 'array',
        ];
    }

    /**
     * Get the user that owns the follow-up.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the invoice that owns the follow-up.
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class);
    }

    /**
     * Scope a query to only include pending follow-ups.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope a query to only include overdue follow-ups.
     */
    public function scopeOverdue($query)
    {
        return $query->where('scheduled_at', '<', now())->where('status', 'pending');
    }

    /**
     * Scope a query to only include sent follow-ups.
     */
    public function scopeSent($query)
    {
        return $query->where('status', 'sent');
    }

    /**
     * Scope a query to only include auto-generated follow-ups.
     */
    public function scopeAutoGenerated($query)
    {
        return $query->where('auto_generated', true);
    }

    /**
     * Scope a query to only include follow-ups with client responses.
     */
    public function scopeWithResponse($query)
    {
        return $query->whereNotNull('client_responded_at');
    }

    /**
     * Record client viewing the follow-up.
     */
    public function recordClientView(): void
    {
        if (!$this->client_viewed_at) {
            $this->update(['client_viewed_at' => now()]);
        }
    }

    /**
     * Record client response to the follow-up.
     */
    public function recordClientResponse(string $responseType, array $responseData = []): void
    {
        $this->update([
            'client_responded_at' => now(),
            'response_type' => $responseType,
            'client_response_data' => $responseData,
        ]);
    }

    /**
     * Calculate effectiveness score based on response and timing.
     */
    public function calculateEffectivenessScore(): float
    {
        $score = 0;

        // Base score for being sent
        if ($this->sent_at) {
            $score += 20;
        }

        // Score for client viewing
        if ($this->client_viewed_at) {
            $score += 30;

            // Bonus for quick viewing (within 24 hours)
            $viewTime = $this->client_viewed_at->diffInHours($this->sent_at);
            if ($viewTime <= 24) {
                $score += 10;
            }
        }

        // Score for client response
        if ($this->client_responded_at) {
            $score += 40;

            // Bonus for positive response
            if (in_array($this->response_type, ['payment_made', 'payment_scheduled', 'acknowledged'])) {
                $score += 10;
            }
        }

        return min($score, 100);
    }

    /**
     * Update effectiveness score.
     */
    public function updateEffectivenessScore(): void
    {
        $score = $this->calculateEffectivenessScore();
        $this->update(['effectiveness_score' => $score]);
    }

    /**
     * Check if follow-up was effective.
     */
    public function isEffective(): bool
    {
        return $this->effectiveness_score >= 60;
    }

    /**
     * Get AI insights for this follow-up.
     */
    public function getAIInsights(): array
    {
        return $this->ai_insights ?? [];
    }

    /**
     * Update AI insights.
     */
    public function updateAIInsights(array $insights): void
    {
        $this->update(['ai_insights' => $insights]);
    }
}
