<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ProjectMember extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'user_id',
        'role',
        'hourly_rate',
        'can_track_time',
        'can_manage_tasks',
        'can_view_reports',
        'joined_at',
        'left_at',
    ];

    protected function casts(): array
    {
        return [
            'hourly_rate' => 'decimal:2',
            'can_track_time' => 'boolean',
            'can_manage_tasks' => 'boolean',
            'can_view_reports' => 'boolean',
            'joined_at' => 'datetime',
            'left_at' => 'datetime',
        ];
    }

    /**
     * Get the project that owns the member.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the user for this project member.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope a query to only include active members.
     */
    public function scopeActive($query)
    {
        return $query->whereNull('left_at');
    }

    /**
     * Scope a query to only include members with specific role.
     */
    public function scopeWithRole($query, string $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Check if member can perform specific action.
     */
    public function canPerformAction(string $action): bool
    {
        return match($action) {
            'track_time' => $this->can_track_time,
            'manage_tasks' => $this->can_manage_tasks || in_array($this->role, ['owner', 'manager']),
            'view_reports' => $this->can_view_reports || in_array($this->role, ['owner', 'manager']),
            'manage_project' => in_array($this->role, ['owner', 'manager']),
            'delete_project' => $this->role === 'owner',
            default => false,
        };
    }

    /**
     * Check if member is active.
     */
    public function getIsActiveAttribute(): bool
    {
        return is_null($this->left_at);
    }

    /**
     * Get role display name.
     */
    public function getRoleDisplayNameAttribute(): string
    {
        return match($this->role) {
            'owner' => 'Project Owner',
            'manager' => 'Project Manager',
            'member' => 'Team Member',
            'viewer' => 'Viewer',
            default => ucfirst($this->role),
        };
    }

    /**
     * Leave the project.
     */
    public function leave(): void
    {
        $this->update(['left_at' => now()]);
    }

    /**
     * Rejoin the project.
     */
    public function rejoin(): void
    {
        $this->update(['left_at' => null]);
    }
}
