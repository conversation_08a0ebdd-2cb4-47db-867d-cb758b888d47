/* FontAwesome Icons */
@import '@fortawesome/fontawesome-free/css/all.css';

/* Toastify Styles */
@import 'toastify-js/src/toastify.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Utilities */
@layer utilities {
    .shadow-soft {
        box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
    }

    .scrollbar-hide::-webkit-scrollbar {
        display: none;
    }

    /* Animation utilities */
    .animate-fadeIn {
        animation: fadeIn 0.5s ease-in-out;
    }

    .animate-slideUp {
        animation: slideUp 0.3s ease-out;
    }

    .animate-slideDown {
        animation: slideDown 0.3s ease-out;
    }

    .animate-scaleIn {
        animation: scaleIn 0.2s ease-out;
    }

    .animate-pulse-soft {
        animation: pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    }
}

/* Keyframe animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes pulseSoft {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}

/* Smooth transitions for all elements */
* {
    @apply transition-colors duration-200;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    @apply bg-gray-100;
}

::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
}
