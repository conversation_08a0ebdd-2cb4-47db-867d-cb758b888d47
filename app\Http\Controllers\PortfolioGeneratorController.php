<?php

namespace App\Http\Controllers;

use App\Services\PlanChecker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class PortfolioGeneratorController extends Controller
{
    /**
     * Display portfolio generator.
     */
    public function index()
    {
        // Check if user can use portfolio generator
        if (!PlanChecker::canUsePortfolioGenerator()) {
            return PlanChecker::redirectToUpgrade('portfolio_generator');
        }

        $user = Auth::user();
        
        // Get user's portfolio data
        $portfolioData = [
            'total_projects' => $user->contracts->where('status', 'signed')->count(),
            'total_revenue' => $user->invoices->where('status', 'paid')->sum('total_amount'),
            'client_count' => $user->clients->count(),
            'avg_project_value' => $this->calculateAverageProjectValue($user),
            'top_skills' => $this->extractTopSkills($user),
            'recent_projects' => $this->getRecentProjects($user),
        ];

        return view('portfolio-generator.index', compact('portfolioData'));
    }

    /**
     * Generate portfolio content.
     */
    public function generate(Request $request)
    {
        // Check if user can use portfolio generator
        if (!PlanChecker::canUsePortfolioGenerator()) {
            return response()->json(['error' => 'Portfolio Generator requires Business plan'], 403);
        }

        $validated = $request->validate([
            'template' => 'required|in:modern,classic,creative,minimal',
            'sections' => 'required|array',
            'sections.*' => 'in:about,skills,projects,testimonials,contact',
            'style' => 'required|in:professional,creative,technical,corporate'
        ]);

        $user = Auth::user();
        
        // Generate portfolio sections based on user data
        $portfolio = [
            'template' => $validated['template'],
            'style' => $validated['style'],
            'sections' => $this->generatePortfolioSections($user, $validated['sections']),
            'preview_url' => route('portfolio.preview', ['user' => $user->id]),
            'download_url' => route('portfolio.download', ['user' => $user->id])
        ];

        return response()->json($portfolio);
    }

    /**
     * Preview generated portfolio.
     */
    public function preview(Request $request)
    {
        // Check if user can use portfolio generator
        if (!PlanChecker::canUsePortfolioGenerator()) {
            return PlanChecker::redirectToUpgrade('portfolio_generator');
        }

        $user = Auth::user();
        $portfolioData = $this->getPortfolioData($user);

        return view('portfolio-generator.preview', compact('portfolioData'));
    }

    /**
     * Download portfolio as PDF.
     */
    public function download(Request $request)
    {
        // Check if user can use portfolio generator
        if (!PlanChecker::canUsePortfolioGenerator()) {
            return PlanChecker::redirectToUpgrade('portfolio_generator');
        }

        $user = Auth::user();
        $portfolioData = $this->getPortfolioData($user);

        // Generate PDF (placeholder)
        $pdf = app('dompdf.wrapper');
        $pdf->loadView('portfolio-generator.pdf', compact('portfolioData'));

        return $pdf->download("portfolio-{$user->name}.pdf");
    }

    /**
     * Calculate average project value.
     */
    private function calculateAverageProjectValue($user)
    {
        $signedContracts = $user->contracts->where('status', 'signed');
        $totalRevenue = $user->invoices->where('status', 'paid')->sum('total_amount');
        
        return $signedContracts->count() > 0 ? $totalRevenue / $signedContracts->count() : 0;
    }

    /**
     * Extract top skills from contracts and projects.
     */
    private function extractTopSkills($user)
    {
        // Placeholder for skill extraction logic
        return ['Web Development', 'UI/UX Design', 'Project Management', 'Consulting'];
    }

    /**
     * Get recent projects.
     */
    private function getRecentProjects($user)
    {
        return $user->contracts()
            ->where('status', 'signed')
            ->with('client')
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($contract) {
                return [
                    'title' => $contract->title,
                    'client' => $contract->client->name,
                    'completed_date' => $contract->signed_date,
                    'description' => substr($contract->content, 0, 200) . '...'
                ];
            });
    }

    /**
     * Generate portfolio sections.
     */
    private function generatePortfolioSections($user, $sections)
    {
        $generated = [];
        
        foreach ($sections as $section) {
            switch ($section) {
                case 'about':
                    $generated['about'] = "Professional freelancer with {$user->contracts->count()} completed projects and {$user->clients->count()} satisfied clients.";
                    break;
                case 'skills':
                    $generated['skills'] = $this->extractTopSkills($user);
                    break;
                case 'projects':
                    $generated['projects'] = $this->getRecentProjects($user);
                    break;
                case 'testimonials':
                    $generated['testimonials'] = []; // Placeholder
                    break;
                case 'contact':
                    $generated['contact'] = [
                        'email' => $user->email,
                        'name' => $user->name
                    ];
                    break;
            }
        }
        
        return $generated;
    }

    /**
     * Get complete portfolio data.
     */
    private function getPortfolioData($user)
    {
        return [
            'user' => $user,
            'stats' => [
                'projects' => $user->contracts->where('status', 'signed')->count(),
                'clients' => $user->clients->count(),
                'revenue' => $user->invoices->where('status', 'paid')->sum('total_amount')
            ],
            'recent_projects' => $this->getRecentProjects($user),
            'skills' => $this->extractTopSkills($user)
        ];
    }
}
