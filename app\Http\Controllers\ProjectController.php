<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreProjectRequest;
use App\Http\Requests\UpdateProjectRequest;
use App\Models\Project;
use App\Services\ProjectService;
use App\Services\ClientService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProjectController extends Controller
{
    protected ProjectService $projectService;
    protected ClientService $clientService;

    public function __construct(ProjectService $projectService, ClientService $clientService)
    {
        $this->projectService = $projectService;
        $this->clientService = $clientService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $projects = $this->projectService->getProjectsForUser(Auth::id(), $request);
        $clients = $this->clientService->getClientsForUser(Auth::id());

        return view('projects.index', compact('projects', 'clients'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Check if user can create projects
        if (!$this->projectService->canCreateProject(Auth::id())) {
            return redirect()->route('upgrade')
                ->with('error', 'You have reached your project limit. Upgrade your plan for more projects.');
        }

        $clients = $this->clientService->getClientsForUser(Auth::id());

        return view('projects.create', compact('clients'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreProjectRequest $request)
    {
        // Check if user can create projects
        if (!$this->projectService->canCreateProject(Auth::id())) {
            return redirect()->route('projects.index')
                ->with('error', 'You have reached your project limit. Upgrade your plan for more projects.');
        }

        $validated = $request->validated();
        $this->projectService->createProject($validated);

        return redirect()->route('projects.index')
                        ->with('success', 'Project created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Project $project)
    {
        $this->authorize('view', $project);
        $project->load(['client', 'tasks.assignedUser', 'timeEntries.user', 'projectMembers.user']);

        return view('projects.show', compact('project'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Project $project)
    {
        $this->authorize('update', $project);

        $clients = $this->clientService->getClientsForUser(Auth::id());

        return view('projects.edit', compact('project', 'clients'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateProjectRequest $request, Project $project)
    {
        $this->authorize('update', $project);

        $validated = $request->validated();
        $this->projectService->updateProject($project, $validated);

        return redirect()->route('projects.show', $project)
                        ->with('success', 'Project updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Project $project)
    {
        $this->authorize('delete', $project);

        $this->projectService->deleteProject($project);

        return redirect()->route('projects.index')
                        ->with('success', 'Project deleted successfully.');
    }

    /**
     * Show project dashboard.
     */
    public function dashboard(Project $project)
    {
        $this->authorize('view', $project);

        $project->load(['client', 'tasks.assignedUser', 'timeEntries.user', 'projectMembers.user']);
        $projectStats = $this->projectService->getProjectStats($project);

        return view('projects.dashboard', compact('project', 'projectStats'));
    }

    /**
     * Get tasks for a project (AJAX endpoint).
     */
    public function getTasks(Project $project)
    {
        $this->authorize('view', $project);

        $tasks = $project->tasks()->select('id', 'title', 'status')->get();

        return response()->json($tasks);
    }

    /**
     * Get project details for time tracking (AJAX endpoint).
     */
    public function getDetails(Project $project)
    {
        $this->authorize('view', $project);

        return response()->json([
            'id' => $project->id,
            'name' => $project->name,
            'hourly_rate' => $project->hourly_rate,
            'currency' => $project->currency ?? 'INR'
        ]);
    }

    /**
     * Update project status.
     */
    public function updateStatus(Request $request, Project $project)
    {
        $this->authorize('update', $project);

        $validated = $request->validate([
            'status' => 'required|in:planning,active,on_hold,completed,cancelled',
        ]);

        $this->projectService->updateProjectStatus($project, $validated['status']);

        return response()->json([
            'success' => true,
            'message' => 'Project status updated successfully.',
            'project' => $project->fresh()
        ]);
    }

    /**
     * Add member to project.
     */
    public function addMember(Request $request, Project $project)
    {
        $this->authorize('update', $project);

        $validated = $request->validate([
            'user_id' => 'required|exists:users,id',
            'role' => 'required|in:member,manager',
            'hourly_rate' => 'nullable|numeric|min:0',
        ]);

        try {
            $this->projectService->addProjectMember($project, $validated);

            return response()->json([
                'success' => true,
                'message' => 'Member added successfully.'
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 422);
        }
    }

    /**
     * Remove member from project.
     */
    public function removeMember(Project $project, $memberId)
    {
        $this->authorize('update', $project);

        $this->projectService->removeProjectMember($project, $memberId);

        return response()->json([
            'success' => true,
            'message' => 'Member removed successfully.'
        ]);
    }

    /**
     * Update project member.
     */
    public function updateMember(Request $request, Project $project, $memberId)
    {
        $this->authorize('update', $project);

        $validated = $request->validate([
            'role' => 'required|in:member,manager',
            'hourly_rate' => 'nullable|numeric|min:0',
        ]);

        $member = $this->projectService->updateProjectMember($project, $memberId, $validated);

        return response()->json([
            'success' => true,
            'message' => 'Member updated successfully.',
            'member' => $member->load('user')
        ]);
    }
}
