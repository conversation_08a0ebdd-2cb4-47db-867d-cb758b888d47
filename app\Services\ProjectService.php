<?php

namespace App\Services;

use App\Models\Project;
use App\Models\ProjectMember;
use App\Models\Client;
use App\Models\User;
use App\Repositories\ProjectRepository;
use App\Services\PlanChecker;
use App\Traits\HasCalculations;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Exception;

class ProjectService
{
    use HasCalculations;

    protected ProjectRepository $projectRepository;

    public function __construct(ProjectRepository $projectRepository)
    {
        $this->projectRepository = $projectRepository;
    }

    /**
     * Create a new project
     */
    public function createProject(array $data): Project
    {
        return DB::transaction(function () use ($data) {
            $project = Project::create([
                'user_id' => Auth::id(),
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'status' => $data['status'] ?? 'planning',
                'priority' => $data['priority'] ?? 'medium',
                'start_date' => $data['start_date'] ?? now(),
                'due_date' => $data['due_date'] ?? null,
                'budget' => $data['budget'] ?? null,
                'hourly_rate' => $data['hourly_rate'] ?? null,
                'billing_type' => $data['billing_type'] ?? 'hourly',
                'is_billable' => $data['is_billable'] ?? true,
                'notes' => $data['notes'] ?? null,
                'custom_fields' => $data['custom_fields'] ?? null,
                'progress_percentage' => 0,
            ]);

            $project->save();

            return $project->load(['client']);
        });
    }

    /**
     * Update an existing project
     */
    public function updateProject(Project $project, array $data): Project
    {
        return DB::transaction(function () use ($project, $data) {
            // Update basic project data
            $project->update([
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'status' => $data['status'],
                'priority' => $data['priority'] ?? $project->priority,
                'due_date' => $data['due_date'] ?? null,
                'budget' => $data['budget'] ?? null,
                'hourly_rate' => $data['hourly_rate'] ?? null,
                'billing_type' => $data['billing_type'] ?? $project->billing_type,
                'is_billable' => $data['is_billable'] ?? $project->is_billable,
                'notes' => $data['notes'] ?? null,
                'custom_fields' => $data['custom_fields'] ?? null,
            ]);

            // Mark as completed if status changed to completed
            if ($data['status'] === 'completed' && $project->status !== 'completed') {
                $project->completed_date = now();
                $project->progress_percentage = 100;
            }

            $project->save();

            return $project->load(['client']);
        });
    }

    /**
     * Delete a project
     */
    public function deleteProject(Project $project): bool
    {
        return DB::transaction(function () use ($project) {
            // Delete related records
            $project->tasks()->delete();
            $project->timeEntries()->delete();
            $project->projectMembers()->delete();

            return $project->delete();
        });
    }

    /**
     * Get projects for user with filtering
     */
    public function getProjectsForUser(int $userId, $request = null): \Illuminate\Contracts\Pagination\LengthAwarePaginator
    {
        return $this->projectRepository->getProjectsForUser($userId, $request);
    }

    /**
     * Check if user can create projects
     */
    public function canCreateProject(int $userId): bool
    {
        return PlanChecker::canCreateProject();
    }

    /**
     * Update project status
     */
    public function updateProjectStatus(Project $project, string $status): Project
    {
        $project->update(['status' => $status]);

        if ($status === 'completed') {
            $project->update([
                'completed_date' => now(),
                'progress_percentage' => 100
            ]);
        }

        return $project;
    }

    /**
     * Add member to project
     */
    public function addProjectMember(Project $project, array $data): ProjectMember
    {
        // Check if user is already a member
        if ($project->projectMembers()->where('user_id', $data['user_id'])->exists()) {
            throw new Exception('User is already a member of this project.');
        }

        return $project->projectMembers()->create($data);
    }

    /**
     * Remove member from project
     */
    public function removeProjectMember(Project $project, int $memberId): bool
    {
        $member = $project->projectMembers()->findOrFail($memberId);
        return $member->delete();
    }

    /**
     * Update project member
     */
    public function updateProjectMember(Project $project, int $memberId, array $data): ProjectMember
    {
        $member = $project->projectMembers()->findOrFail($memberId);
        $member->update($data);
        return $member;
    }

    /**
     * Get project statistics
     */
    public function getProjectStats(Project $project): array
    {
        $totalTasks = $project->tasks()->count();
        $completedTasks = $project->tasks()->where('status', 'completed')->count();
        $totalTimeSpent = $project->timeEntries()->sum('duration');
        $totalBudgetUsed = $project->timeEntries()->sum('billable_amount');

        return [
            'total_tasks' => $totalTasks,
            'completed_tasks' => $completedTasks,
            'pending_tasks' => $totalTasks - $completedTasks,
            'completion_percentage' => $totalTasks > 0 ? round(($completedTasks / $totalTasks) * 100, 2) : 0,
            'total_time_spent' => $totalTimeSpent,
            'total_budget_used' => $totalBudgetUsed,
            'budget_remaining' => $project->budget ? $project->budget - $totalBudgetUsed : null,
            'is_over_budget' => $project->budget ? $totalBudgetUsed > $project->budget : false,
            'team_members' => $project->projectMembers()->count(),
        ];
    }

    /**
     * Get project statistics for user
     */
    public function getProjectStatsForUser(int $userId): array
    {
        return $this->projectRepository->getStatsForUser($userId);
    }

    /**
     * Duplicate a project
     */
    public function duplicateProject(Project $project): Project
    {
        return DB::transaction(function () use ($project) {
            $newProject = $project->replicate();
            $newProject->name = $project->name . ' (Copy)';
            $newProject->status = 'planning';
            $newProject->completed_date = null;
            $newProject->progress_percentage = 0;
            $newProject->save();

            // Duplicate tasks
            foreach ($project->tasks as $task) {
                $newTask = $task->replicate();
                $newTask->project_id = $newProject->id;
                $newTask->status = 'todo';
                $newTask->completed_date = null;
                $newTask->save();
            }

            // Duplicate project members
            foreach ($project->projectMembers as $member) {
                $newMember = $member->replicate();
                $newMember->project_id = $newProject->id;
                $newMember->save();
            }

            return $newProject->load(['client', 'tasks', 'projectMembers']);
        });
    }
}
