<?php

namespace App\Exports;

use App\Models\TdsRecord;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class TdsReportExport implements FromCollection, WithHeadings, WithMapping
{
    protected $financialYear;
    protected $clientId;

    public function __construct($financialYear, $clientId = null)
    {
        $this->financialYear = $financialYear;
        $this->clientId = $clientId;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Auth::user()->tdsRecords()
                          ->with(['client', 'invoice'])
                          ->forFinancialYear($this->financialYear);

        if ($this->clientId) {
            $query->where('client_id', $this->clientId);
        }

        return $query->orderBy('deduction_date')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Financial Year',
            'Client Name',
            'Company Name',
            'Invoice Number',
            'Invoice Date',
            'Invoice Amount',
            'TDS Percentage',
            'TDS Amount',
            'Net Received',
            'Deduction Date',
            'TDS Certificate Number',
        ];
    }

    /**
     * @param TdsRecord $tdsRecord
     * @return array
     */
    public function map($tdsRecord): array
    {
        return [
            $tdsRecord->financial_year,
            $tdsRecord->client?->name ?? 'N/A',
            $tdsRecord->client?->company_name ?? 'N/A',
            $tdsRecord->invoice?->invoice_number ?? 'N/A',
            $tdsRecord->invoice?->invoice_date?->format('d/m/Y') ?? 'N/A',
            $tdsRecord->invoice_amount,
            $tdsRecord->tds_percentage . '%',
            $tdsRecord->tds_amount,
            $tdsRecord->net_received,
            $tdsRecord->deduction_date->format('d/m/Y'),
            $tdsRecord->tds_certificate_number ?? 'N/A',
        ];
    }
}
