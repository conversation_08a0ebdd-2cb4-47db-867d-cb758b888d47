<?php

namespace App\Services;

use App\Models\Workflow;
use App\Models\WorkflowExecution;
use App\Models\FollowUp;
use App\Models\Invoice;
use App\Models\RecurringInvoice;
use App\Models\TdsRecord;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AutomationAnalyticsService
{
    /**
     * Get comprehensive automation dashboard data.
     */
    public function getDashboardData(int $userId, string $period = '30_days'): array
    {
        $dateRange = $this->getDateRange($period);

        return [
            'overview' => $this->getOverviewMetrics($userId, $dateRange),
            'workflow_performance' => $this->getWorkflowPerformance($userId, $dateRange),
            'notification_analytics' => $this->getNotificationAnalytics($userId, $dateRange),
            'automation_savings' => $this->getAutomationSavings($userId, $dateRange),
            'recurring_invoice_stats' => $this->getRecurringInvoiceStats($userId, $dateRange),
            'payment_reminder_effectiveness' => $this->getPaymentReminderEffectiveness($userId, $dateRange),
            'tds_automation_summary' => $this->getTdsAutomationSummary($userId, $dateRange),
            'trends' => $this->getAutomationTrends($userId, $dateRange),
        ];
    }

    /**
     * Get overview metrics.
     */
    protected function getOverviewMetrics(int $userId, array $dateRange): array
    {
        $totalWorkflows = Workflow::where('user_id', $userId)->count();
        $activeWorkflows = Workflow::where('user_id', $userId)->active()->count();
        
        $executions = WorkflowExecution::whereHas('workflow', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->whereBetween('created_at', $dateRange);

        $totalExecutions = $executions->count();
        $successfulExecutions = $executions->successful()->count();
        $failedExecutions = $executions->failed()->count();

        $automatedInvoices = Invoice::where('user_id', $userId)
            ->where('is_automated', true)
            ->whereBetween('created_at', $dateRange)
            ->count();

        $automatedFollowUps = FollowUp::where('user_id', $userId)
            ->where('is_automated', true)
            ->whereBetween('created_at', $dateRange)
            ->count();

        return [
            'total_workflows' => $totalWorkflows,
            'active_workflows' => $activeWorkflows,
            'total_executions' => $totalExecutions,
            'successful_executions' => $successfulExecutions,
            'failed_executions' => $failedExecutions,
            'success_rate' => $totalExecutions > 0 ? round(($successfulExecutions / $totalExecutions) * 100, 2) : 0,
            'automated_invoices' => $automatedInvoices,
            'automated_followups' => $automatedFollowUps,
            'time_saved_hours' => $this->calculateTimeSaved($totalExecutions),
        ];
    }

    /**
     * Get workflow performance data.
     */
    protected function getWorkflowPerformance(int $userId, array $dateRange): array
    {
        $workflows = Workflow::where('user_id', $userId)
            ->withCount(['executions' => function ($query) use ($dateRange) {
                $query->whereBetween('created_at', $dateRange);
            }])
            ->with(['executions' => function ($query) use ($dateRange) {
                $query->whereBetween('created_at', $dateRange)
                    ->select('workflow_id', 'status', 'created_at', 'completed_at');
            }])
            ->get();

        return $workflows->map(function ($workflow) {
            $executions = $workflow->executions;
            $successful = $executions->where('status', 'completed')->count();
            $failed = $executions->where('status', 'failed')->count();
            $total = $executions->count();

            $avgExecutionTime = $executions->filter(function ($execution) {
                return $execution->completed_at && $execution->created_at;
            })->avg(function ($execution) {
                return $execution->created_at->diffInSeconds($execution->completed_at);
            });

            return [
                'id' => $workflow->id,
                'name' => $workflow->name,
                'trigger_type' => $workflow->trigger_type,
                'total_executions' => $total,
                'successful_executions' => $successful,
                'failed_executions' => $failed,
                'success_rate' => $total > 0 ? round(($successful / $total) * 100, 2) : 0,
                'avg_execution_time_seconds' => round($avgExecutionTime ?? 0, 2),
                'is_active' => $workflow->is_active,
                'last_executed' => $workflow->last_executed_at,
            ];
        })->toArray();
    }

    /**
     * Get notification analytics.
     */
    protected function getNotificationAnalytics(int $userId, array $dateRange): array
    {
        $followUps = FollowUp::where('user_id', $userId)
            ->whereBetween('created_at', $dateRange)
            ->get();

        $byMethod = $followUps->groupBy('method')->map(function ($group) {
            return [
                'total' => $group->count(),
                'sent' => $group->where('status', 'sent')->count(),
                'pending' => $group->where('status', 'scheduled')->count(),
                'failed' => $group->where('status', 'failed')->count(),
            ];
        });

        $byType = $followUps->groupBy('type')->map(function ($group) {
            return [
                'total' => $group->count(),
                'response_rate' => $this->calculateResponseRate($group),
            ];
        });

        return [
            'total_notifications' => $followUps->count(),
            'by_method' => $byMethod->toArray(),
            'by_type' => $byType->toArray(),
            'overall_response_rate' => $this->calculateResponseRate($followUps),
        ];
    }

    /**
     * Get automation savings data.
     */
    protected function getAutomationSavings(int $userId, array $dateRange): array
    {
        $totalExecutions = WorkflowExecution::whereHas('workflow', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })->whereBetween('created_at', $dateRange)->count();

        $automatedInvoices = Invoice::where('user_id', $userId)
            ->where('is_automated', true)
            ->whereBetween('created_at', $dateRange)
            ->count();

        $automatedFollowUps = FollowUp::where('user_id', $userId)
            ->where('is_automated', true)
            ->whereBetween('created_at', $dateRange)
            ->count();

        // Estimate time savings (in minutes)
        $timeSavings = [
            'invoice_processing' => $automatedInvoices * 15, // 15 min per invoice
            'follow_up_creation' => $automatedFollowUps * 5, // 5 min per follow-up
            'workflow_executions' => $totalExecutions * 3, // 3 min per workflow
        ];

        $totalMinutesSaved = array_sum($timeSavings);
        $hoursSaved = round($totalMinutesSaved / 60, 2);

        // Estimate cost savings (assuming ₹500/hour)
        $costSavings = $hoursSaved * 500;

        return [
            'time_saved_minutes' => $totalMinutesSaved,
            'time_saved_hours' => $hoursSaved,
            'estimated_cost_savings' => $costSavings,
            'breakdown' => $timeSavings,
            'productivity_increase' => $this->calculateProductivityIncrease($totalExecutions),
        ];
    }

    /**
     * Get recurring invoice statistics.
     */
    protected function getRecurringInvoiceStats(int $userId, array $dateRange): array
    {
        $recurringInvoices = RecurringInvoice::where('user_id', $userId)->get();
        
        $generatedInvoices = Invoice::where('user_id', $userId)
            ->whereNotNull('recurring_invoice_id')
            ->whereBetween('created_at', $dateRange)
            ->get();

        $autoSentInvoices = $generatedInvoices->where('is_auto_sent', true);

        return [
            'total_recurring_setups' => $recurringInvoices->count(),
            'active_recurring' => $recurringInvoices->where('status', 'active')->count(),
            'invoices_generated' => $generatedInvoices->count(),
            'auto_sent_invoices' => $autoSentInvoices->count(),
            'total_recurring_revenue' => $generatedInvoices->sum('total_amount'),
            'avg_invoice_value' => $generatedInvoices->avg('total_amount'),
            'frequency_breakdown' => $recurringInvoices->groupBy('frequency')->map->count(),
        ];
    }

    /**
     * Get payment reminder effectiveness.
     */
    protected function getPaymentReminderEffectiveness(int $userId, array $dateRange): array
    {
        $reminders = FollowUp::where('user_id', $userId)
            ->where('type', 'payment_reminder')
            ->whereBetween('created_at', $dateRange)
            ->with('invoice')
            ->get();

        $paidAfterReminder = $reminders->filter(function ($reminder) {
            return $reminder->invoice && 
                   $reminder->invoice->status === 'paid' &&
                   $reminder->invoice->paid_date &&
                   $reminder->sent_at &&
                   $reminder->invoice->paid_date->gte($reminder->sent_at);
        });

        $avgPaymentTime = $paidAfterReminder->avg(function ($reminder) {
            return $reminder->sent_at->diffInDays($reminder->invoice->paid_date);
        });

        return [
            'total_reminders_sent' => $reminders->count(),
            'payments_after_reminder' => $paidAfterReminder->count(),
            'effectiveness_rate' => $reminders->count() > 0 ? 
                round(($paidAfterReminder->count() / $reminders->count()) * 100, 2) : 0,
            'avg_payment_time_days' => round($avgPaymentTime ?? 0, 1),
            'total_amount_collected' => $paidAfterReminder->sum('invoice.total_amount'),
        ];
    }

    /**
     * Get TDS automation summary.
     */
    protected function getTdsAutomationSummary(int $userId, array $dateRange): array
    {
        $tdsRecords = TdsRecord::where('user_id', $userId)
            ->whereBetween('created_at', $dateRange)
            ->get();

        $autoCalculated = $tdsRecords->where('is_auto_calculated', true);

        return [
            'total_tds_records' => $tdsRecords->count(),
            'auto_calculated_records' => $autoCalculated->count(),
            'automation_rate' => $tdsRecords->count() > 0 ? 
                round(($autoCalculated->count() / $tdsRecords->count()) * 100, 2) : 0,
            'total_tds_amount' => $tdsRecords->sum('tds_amount'),
            'auto_calculated_amount' => $autoCalculated->sum('tds_amount'),
            'compliance_score' => $this->calculateTdsComplianceScore($tdsRecords),
        ];
    }

    /**
     * Get automation trends.
     */
    protected function getAutomationTrends(int $userId, array $dateRange): array
    {
        $dailyExecutions = WorkflowExecution::whereHas('workflow', function ($query) use ($userId) {
            $query->where('user_id', $userId);
        })
        ->whereBetween('created_at', $dateRange)
        ->selectRaw('DATE(created_at) as date, COUNT(*) as executions, 
                     SUM(CASE WHEN status = "completed" THEN 1 ELSE 0 END) as successful')
        ->groupBy('date')
        ->orderBy('date')
        ->get();

        return [
            'daily_executions' => $dailyExecutions->toArray(),
            'growth_rate' => $this->calculateGrowthRate($dailyExecutions),
            'peak_automation_day' => $dailyExecutions->sortByDesc('executions')->first(),
        ];
    }

    /**
     * Helper methods.
     */
    protected function getDateRange(string $period): array
    {
        return match ($period) {
            '7_days' => [now()->subDays(7), now()],
            '30_days' => [now()->subDays(30), now()],
            '90_days' => [now()->subDays(90), now()],
            '1_year' => [now()->subYear(), now()],
            default => [now()->subDays(30), now()]
        };
    }

    protected function calculateTimeSaved(int $executions): float
    {
        return round(($executions * 5) / 60, 2); // 5 minutes per execution, converted to hours
    }

    protected function calculateResponseRate($followUps): float
    {
        if ($followUps->isEmpty()) return 0;

        $responded = $followUps->filter(function ($followUp) {
            return $followUp->invoice && 
                   $followUp->invoice->status === 'paid' &&
                   $followUp->invoice->paid_date &&
                   $followUp->sent_at &&
                   $followUp->invoice->paid_date->diffInDays($followUp->sent_at) <= 7;
        })->count();

        return round(($responded / $followUps->count()) * 100, 2);
    }

    protected function calculateProductivityIncrease(int $executions): float
    {
        // Estimate productivity increase based on automation usage
        return min(round($executions * 0.5, 2), 50); // Cap at 50%
    }

    protected function calculateTdsComplianceScore($tdsRecords): float
    {
        if ($tdsRecords->isEmpty()) return 100;

        $compliantRecords = $tdsRecords->filter(function ($record) {
            return !empty($record->certificate_number) && $record->tds_percentage > 0;
        })->count();

        return round(($compliantRecords / $tdsRecords->count()) * 100, 2);
    }

    protected function calculateGrowthRate($dailyData): float
    {
        if ($dailyData->count() < 2) return 0;

        $first = $dailyData->first();
        $last = $dailyData->last();

        if ($first->executions == 0) return 0;

        return round((($last->executions - $first->executions) / $first->executions) * 100, 2);
    }
}
