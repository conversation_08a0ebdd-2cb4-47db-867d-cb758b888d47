<x-app-layout>
    <x-slot name="header">
        <h2 class="font-semibold text-xl text-gray-800 leading-tight">
            {{ __('Create Follow-up') }}
        </h2>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('followups.store') }}" x-data="followupForm()">
                        @csrf

                        <!-- Template Selection -->
                        <div class="mb-6">
                            <label for="followup_template_id" class="block text-sm font-medium text-gray-700">Follow-up Template</label>
                            <select name="followup_template_id" id="followup_template_id" x-model="selectedTemplate" @change="loadTemplate()"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">Select a template (optional)</option>
                                @foreach($templates as $template)
                                    <option value="{{ $template->id }}" data-subject="{{ $template->subject }}" data-message="{{ $template->message }}">
                                        {{ $template->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('followup_template_id')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Client Selection -->
                        <div class="mb-6">
                            <label for="client_id" class="block text-sm font-medium text-gray-700">Client *</label>
                            <select name="client_id" id="client_id" required x-model="selectedClient" @change="loadClientInvoices()"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">Select a client</option>
                                @foreach($clients as $client)
                                    <option value="{{ $client->id }}" {{ old('client_id') == $client->id ? 'selected' : '' }}>
                                        {{ $client->name }}{{ $client->company_name ? ' (' . $client->company_name . ')' : '' }}
                                    </option>
                                @endforeach
                            </select>
                            @error('client_id')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Invoice Selection -->
                        <div class="mb-6">
                            <label for="invoice_id" class="block text-sm font-medium text-gray-700">Related Invoice (Optional)</label>
                            <select name="invoice_id" id="invoice_id" x-model="selectedInvoice"
                                    class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">No specific invoice (general follow-up)</option>
                                <template x-for="invoice in clientInvoices" :key="invoice.id">
                                    <option :value="invoice.id" x-text="`${invoice.invoice_number} - ₹${invoice.total_amount} (${invoice.status})`"></option>
                                </template>
                            </select>
                            @error('invoice_id')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Subject -->
                        <div class="mb-6">
                            <label for="subject" class="block text-sm font-medium text-gray-700">Subject *</label>
                            <input type="text" name="subject" id="subject" required x-model="subject"
                                   value="{{ old('subject') }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('subject')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Message -->
                        <div class="mb-6">
                            <div class="flex justify-between items-center mb-2">
                                <label for="message" class="block text-sm font-medium text-gray-700">Message *</label>
                                @if(\App\Services\PlanChecker::canUseAiAssistant())
                                    <button type="button" id="generateFollowupMessage"
                                            class="bg-purple-500 hover:bg-purple-600 text-white text-xs font-medium py-1 px-3 rounded-lg transition-colors flex items-center">
                                        <i class="fas fa-magic mr-1"></i>
                                        AI Suggest
                                    </button>
                                @endif
                            </div>
                            <textarea name="message" id="message" rows="8" required x-model="message"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"
                                      placeholder="Write your follow-up message here...">{{ old('message') }}</textarea>
                            @error('message')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <div class="mt-2 space-y-1">
                                <p class="text-sm text-gray-500">
                                    You can use placeholders: {client_name}, {company_name}, {invoice_number}, {invoice_amount}, {due_date}
                                </p>
                                @if(\App\Services\PlanChecker::canUseAiAssistant())
                                    <p class="text-xs text-purple-600">
                                        <i class="fas fa-lightbulb mr-1"></i>
                                        Click "AI Suggest" to generate a personalized message based on client relationship and payment history
                                    </p>
                                @endif
                            </div>
                        </div>

                        <!-- Scheduled Date -->
                        <div class="mb-6">
                            <label for="scheduled_date" class="block text-sm font-medium text-gray-700">Scheduled Date & Time *</label>
                            <input type="datetime-local" name="scheduled_date" id="scheduled_date" required
                                   value="{{ old('scheduled_date', now()->addDay()->format('Y-m-d\TH:i')) }}"
                                   class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('scheduled_date')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="mb-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Internal Notes</label>
                            <textarea name="notes" id="notes" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('notes') }}</textarea>
                            @error('notes')
                                <p class="mt-2 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            <p class="mt-2 text-sm text-gray-500">These notes are for your reference and won't be sent to the client.</p>
                        </div>

                        <!-- Preview Section -->
                        <div class="mb-6" x-show="subject || message">
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Preview</h3>
                            <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                                <div class="mb-2">
                                    <strong>Subject:</strong> <span x-text="subject"></span>
                                </div>
                                <div>
                                    <strong>Message:</strong>
                                    <div class="mt-2 whitespace-pre-wrap" x-text="message"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="flex items-center justify-end space-x-4">
                            <a href="{{ route('followups.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" name="action" value="schedule" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Schedule Follow-up
                            </button>
                            <button type="submit" name="action" value="send_now" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                                Send Now
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function followupForm() {
            return {
                selectedTemplate: '',
                selectedClient: '{{ old('client_id') }}',
                selectedInvoice: '{{ old('invoice_id') }}',
                subject: '{{ old('subject') }}',
                message: '{{ old('message') }}',
                clientInvoices: [],

                loadTemplate() {
                    if (this.selectedTemplate) {
                        const option = document.querySelector(`option[value="${this.selectedTemplate}"]`);
                        if (option) {
                            this.subject = option.dataset.subject || '';
                            this.message = option.dataset.message || '';
                        }
                    }
                },

                async loadClientInvoices() {
                    if (this.selectedClient) {
                        try {
                            const response = await fetch(`/api/clients/${this.selectedClient}/invoices`);
                            if (response.ok) {
                                this.clientInvoices = await response.json();
                            }
                        } catch (error) {
                            console.error('Error loading client invoices:', error);
                            this.clientInvoices = [];
                        }
                    } else {
                        this.clientInvoices = [];
                    }
                    this.selectedInvoice = '';
                },

                init() {
                    // Load client invoices if client is pre-selected
                    if (this.selectedClient) {
                        this.loadClientInvoices();
                    }
                }
            }
        }

        // AI Follow-up Message Generation
        @if(\App\Services\PlanChecker::canUseAiAssistant())
        document.getElementById('generateFollowupMessage').addEventListener('click', function() {
            const clientSelect = document.getElementById('client_id');
            const invoiceSelect = document.getElementById('invoice_id');
            const typeSelect = document.getElementById('type');
            const messageTextarea = document.getElementById('message');
            const button = this;

            if (!clientSelect.value) {
                alert('Please select a client first');
                return;
            }

            if (!invoiceSelect.value) {
                alert('Please select an invoice first');
                return;
            }

            // Show loading state
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Generating...';
            button.disabled = true;

            // Get selected invoice data
            const selectedInvoiceOption = invoiceSelect.options[invoiceSelect.selectedIndex];
            const invoiceData = selectedInvoiceOption ? {
                number: selectedInvoiceOption.dataset.number || '',
                amount: selectedInvoiceOption.dataset.amount || 0,
                dueDate: selectedInvoiceOption.dataset.dueDate || '',
                daysOverdue: selectedInvoiceOption.dataset.daysOverdue || 0
            } : {};

            // Get client name
            const selectedClientOption = clientSelect.options[clientSelect.selectedIndex];
            const clientName = selectedClientOption ? selectedClientOption.text.split(' (')[0] : '';

            fetch('{{ route("ai-assistant.generate") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    type: 'followup',
                    prompt: `Generate a ${typeSelect.value} follow-up message for ${clientName}`,
                    context: {
                        client_name: clientName,
                        invoice_number: invoiceData.number,
                        amount: invoiceData.amount,
                        days_overdue: invoiceData.daysOverdue,
                        type: typeSelect.value,
                        relationship: 'professional'
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.content) {
                    messageTextarea.value = data.content;
                    // Show success message
                    const successMsg = document.createElement('div');
                    successMsg.className = 'mt-2 text-sm text-green-600 flex items-center';
                    successMsg.innerHTML = '<i class="fas fa-check mr-1"></i>AI message generated successfully!';
                    messageTextarea.parentNode.appendChild(successMsg);
                    setTimeout(() => successMsg.remove(), 3000);
                } else {
                    alert('Error: ' + (data.error || 'Failed to generate message'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while generating the message');
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
        });
        @endif
    </script>
</x-app-layout>
