<?php

namespace App\Http\Controllers;

use App\Models\TdsRecord;
use App\Services\PlanChecker;
use App\Services\TdsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Maatwebsite\Excel\Facades\Excel;
use App\Exports\TdsReportExport;

class TdsController extends Controller
{
    protected TdsService $tdsService;

    public function __construct(TdsService $tdsService)
    {
        $this->tdsService = $tdsService;
    }
    /**
     * Display TDS records.
     */
    public function index(Request $request)
    {
        // Check if user can access TDS reports
        if (!PlanChecker::canAccessTdsReports()) {
            return PlanChecker::redirectToUpgrade('tds_reports');
        }

        $tdsRecords = $this->tdsService->getTdsRecordsForUser(Auth::id(), $request);
        $clients = Auth::user()->clients()->get();

        $financialYear = $request->get('financial_year', TdsRecord::getCurrentFinancialYear());
        $summary = $this->tdsService->getTdsSummary(Auth::id(), $financialYear);

        // Get available financial years
        $financialYears = Auth::user()->tdsRecords()
                                   ->select('financial_year')
                                   ->distinct()
                                   ->orderBy('financial_year', 'desc')
                                   ->pluck('financial_year');

        return view('tds.index', compact(
            'tdsRecords',
            'clients',
            'financialYear',
            'financialYears'
        ) + $summary);
    }

    /**
     * Show TDS record details.
     */
    public function show(TdsRecord $tdsRecord)
    {
        // Check if user can access TDS reports
        if (!PlanChecker::canAccessTdsReports()) {
            return PlanChecker::redirectToUpgrade('tds_reports');
        }

        $this->authorize('view', $tdsRecord);
        $tdsRecord->load(['client', 'invoice', 'user']);

        return view('tds.show', compact('tdsRecord'));
    }

    /**
     * Update TDS certificate number.
     */
    public function updateCertificate(Request $request, TdsRecord $tdsRecord)
    {
        $this->authorize('update', $tdsRecord);

        $validated = $request->validate([
            'tds_certificate_number' => 'required|string|max:255',
        ]);

        $this->tdsService->updateTdsRecord($tdsRecord, $validated);

        return redirect()->back()
                        ->with('success', 'TDS certificate number updated successfully.');
    }

    /**
     * Export TDS report.
     */
    public function export(Request $request)
    {
        // Check if user can access TDS reports
        if (!PlanChecker::canAccessTdsReports()) {
            return PlanChecker::redirectToUpgrade('tds_reports');
        }

        $financialYear = $request->get('financial_year', TdsRecord::getCurrentFinancialYear());
        $clientId = $request->get('client_id');

        return Excel::download(
            new TdsReportExport($financialYear, $clientId),
            "TDS_Report_{$financialYear}.xlsx"
        );
    }

    /**
     * Get TDS summary by client.
     */
    public function summary(Request $request)
    {
        // Check if user can access TDS reports
        if (!PlanChecker::canAccessTdsReports()) {
            return PlanChecker::redirectToUpgrade('tds_reports');
        }

        $financialYear = $request->get('financial_year', TdsRecord::getCurrentFinancialYear());
        $summary = $this->tdsService->getTdsSummary(Auth::id(), $financialYear);

        return view('tds.summary', compact('summary', 'financialYear'));
    }
}
