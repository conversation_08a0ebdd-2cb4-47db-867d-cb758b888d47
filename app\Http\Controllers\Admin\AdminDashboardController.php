<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\Contract;
use App\Models\TdsRecord;
use App\Models\FollowUp;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\View\View;

class AdminDashboardController extends Controller
{

    /**
     * Display the admin dashboard.
     */
    public function index(): View
    {
        // System-wide statistics
        $totalUsers = User::count();
        $totalFreelancers = User::role('freelancer')->count();
        $totalClients = Client::count();
        $totalInvoices = Invoice::count();
        $totalContracts = Contract::count();
        $totalTdsRecords = TdsRecord::count();
        $totalFollowUps = FollowUp::count();

        // Financial statistics
        $totalRevenue = Invoice::where('status', 'paid')->sum('total_amount');
        $pendingRevenue = Invoice::where('status', '!=', 'paid')->sum('total_amount');
        $totalTdsAmount = TdsRecord::sum('tds_amount');

        // Recent activity
        $recentUsers = User::latest()->take(5)->get();
        $recentInvoices = Invoice::with(['user', 'client'])->latest()->take(5)->get();
        $recentContracts = Contract::with(['user', 'client'])->latest()->take(5)->get();

        // Monthly revenue trend
        $monthlyRevenue = Invoice::where('status', 'paid')
            ->select(
                DB::raw('YEAR(created_at) as year'),
                DB::raw('MONTH(created_at) as month'),
                DB::raw('SUM(total_amount) as total')
            )
            ->groupBy('year', 'month')
            ->orderBy('year', 'desc')
            ->orderBy('month', 'desc')
            ->take(12)
            ->get();

        // Top performing freelancers
        $topFreelancers = User::role('freelancer')
            ->withCount(['invoices', 'clients'])
            ->withSum(['invoices as total_revenue' => function($query) {
                $query->where('status', 'paid');
            }], 'total_amount')
            ->orderBy('total_revenue', 'desc')
            ->take(10)
            ->get();

        // System health metrics
        $overdueInvoices = Invoice::where('status', '!=', 'paid')
            ->where('due_date', '<', now())
            ->count();

        $pendingFollowUps = FollowUp::where('status', 'pending')
            ->where('scheduled_at', '<=', now())
            ->count();

        return view('admin.dashboard', compact(
            'totalUsers',
            'totalFreelancers',
            'totalClients',
            'totalInvoices',
            'totalContracts',
            'totalTdsRecords',
            'totalFollowUps',
            'totalRevenue',
            'pendingRevenue',
            'totalTdsAmount',
            'recentUsers',
            'recentInvoices',
            'recentContracts',
            'monthlyRevenue',
            'topFreelancers',
            'overdueInvoices',
            'pendingFollowUps'
        ));
    }

    /**
     * Get dashboard data for AJAX requests.
     */
    public function getData(Request $request)
    {
        $data = [
            'totalUsers' => User::count(),
            'totalRevenue' => Invoice::where('status', 'paid')->sum('total_amount'),
            'pendingInvoices' => Invoice::where('status', '!=', 'paid')->count(),
            'overdueInvoices' => Invoice::where('status', '!=', 'paid')
                ->where('due_date', '<', now())
                ->count(),
        ];

        return response()->json($data);
    }
}
