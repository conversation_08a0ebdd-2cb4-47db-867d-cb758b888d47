<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove client_user_id from projects table (no data to migrate)
        Schema::table('projects', function (Blueprint $table) {
            $table->dropForeign(['client_user_id']);
            $table->dropIndex(['client_user_id', 'status']);
            $table->dropColumn('client_user_id');
        });

        // Make client_id required in projects
        Schema::table('projects', function (Blueprint $table) {
            $table->foreignId('client_id')->nullable(false)->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Make client_id nullable again
        Schema::table('projects', function (Blueprint $table) {
            $table->foreignId('client_id')->nullable()->change();
        });

        // Restore client_user_id to projects
        Schema::table('projects', function (Blueprint $table) {
            $table->foreignId('client_user_id')->nullable()->after('client_id')->constrained('users')->onDelete('cascade');
            $table->index(['client_user_id', 'status']);
        });
    }
};
