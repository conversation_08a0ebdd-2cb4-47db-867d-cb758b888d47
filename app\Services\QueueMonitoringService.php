<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class QueueMonitoringService
{
    /**
     * Get queue statistics.
     */
    public function getQueueStatistics(): array
    {
        $stats = [
            'pending_jobs' => $this->getPendingJobsCount(),
            'failed_jobs' => $this->getFailedJobsCount(),
            'processed_today' => $this->getProcessedJobsToday(),
            'average_processing_time' => $this->getAverageProcessingTime(),
            'queue_health' => $this->getQueueHealth(),
            'worker_status' => $this->getWorkerStatus(),
            'job_types' => $this->getJobTypeDistribution(),
            'hourly_throughput' => $this->getHourlyThroughput(),
        ];

        return $stats;
    }

    /**
     * Get pending jobs count by queue.
     */
    public function getPendingJobsCount(): array
    {
        $queues = ['high', 'default', 'low', 'bulk', 'analytics', 'notifications', 'retry', 'ai'];
        $counts = [];

        foreach ($queues as $queue) {
            $counts[$queue] = DB::table('jobs')
                ->where('queue', $queue)
                ->count();
        }

        $counts['total'] = array_sum($counts);
        return $counts;
    }

    /**
     * Get failed jobs count.
     */
    public function getFailedJobsCount(): int
    {
        return DB::table('failed_jobs')->count();
    }

    /**
     * Get processed jobs today.
     */
    public function getProcessedJobsToday(): int
    {
        return Cache::remember('queue_processed_today', 300, function () {
            // This would need to be tracked in a separate table or log
            // For now, we'll estimate based on successful jobs
            return DB::table('jobs')
                ->where('created_at', '>=', now()->startOfDay())
                ->count();
        });
    }

    /**
     * Get average processing time.
     */
    public function getAverageProcessingTime(): float
    {
        return Cache::remember('queue_avg_processing_time', 600, function () {
            // This would need job execution time tracking
            // For now, return a placeholder
            return 15.5; // seconds
        });
    }

    /**
     * Get queue health status.
     */
    public function getQueueHealth(): array
    {
        $pendingJobs = $this->getPendingJobsCount();
        $failedJobs = $this->getFailedJobsCount();
        
        $health = [
            'status' => 'healthy',
            'issues' => [],
            'score' => 100
        ];

        // Check for high pending job counts
        if ($pendingJobs['high'] > 50) {
            $health['issues'][] = 'High priority queue backlog';
            $health['score'] -= 20;
        }

        if ($pendingJobs['total'] > 500) {
            $health['issues'][] = 'Total queue backlog is high';
            $health['score'] -= 15;
        }

        // Check for failed jobs
        if ($failedJobs > 100) {
            $health['issues'][] = 'High number of failed jobs';
            $health['score'] -= 25;
        }

        // Check for stuck jobs (older than 1 hour)
        $stuckJobs = DB::table('jobs')
            ->where('created_at', '<', now()->subHour())
            ->count();

        if ($stuckJobs > 10) {
            $health['issues'][] = 'Jobs appear to be stuck';
            $health['score'] -= 30;
        }

        // Determine overall status
        if ($health['score'] >= 80) {
            $health['status'] = 'healthy';
        } elseif ($health['score'] >= 60) {
            $health['status'] = 'warning';
        } else {
            $health['status'] = 'critical';
        }

        return $health;
    }

    /**
     * Get worker status.
     */
    public function getWorkerStatus(): array
    {
        // This would need integration with queue worker monitoring
        // For now, return placeholder data
        return [
            'active_workers' => 8,
            'total_workers' => 10,
            'worker_utilization' => 80,
            'last_heartbeat' => now()->subMinutes(2)->toISOString()
        ];
    }

    /**
     * Get job type distribution.
     */
    public function getJobTypeDistribution(): array
    {
        $distribution = Cache::remember('queue_job_distribution', 300, function () {
            $jobs = DB::table('jobs')
                ->select('payload')
                ->get();

            $types = [];
            foreach ($jobs as $job) {
                $payload = json_decode($job->payload, true);
                $className = $payload['displayName'] ?? 'Unknown';
                $types[$className] = ($types[$className] ?? 0) + 1;
            }

            return $types;
        });

        return $distribution;
    }

    /**
     * Get hourly throughput for the last 24 hours.
     */
    public function getHourlyThroughput(): array
    {
        return Cache::remember('queue_hourly_throughput', 600, function () {
            $throughput = [];
            
            for ($i = 23; $i >= 0; $i--) {
                $hour = now()->subHours($i)->format('H:00');
                // This would need actual job completion tracking
                $throughput[$hour] = rand(10, 100); // Placeholder data
            }

            return $throughput;
        });
    }

    /**
     * Get failed jobs with details.
     */
    public function getFailedJobsDetails(int $limit = 50): array
    {
        return DB::table('failed_jobs')
            ->orderBy('failed_at', 'desc')
            ->limit($limit)
            ->get()
            ->map(function ($job) {
                $payload = json_decode($job->payload, true);
                return [
                    'id' => $job->id,
                    'uuid' => $job->uuid,
                    'connection' => $job->connection,
                    'queue' => $job->queue,
                    'job_class' => $payload['displayName'] ?? 'Unknown',
                    'exception' => $job->exception,
                    'failed_at' => $job->failed_at,
                ];
            })
            ->toArray();
    }

    /**
     * Retry failed jobs.
     */
    public function retryFailedJobs(array $jobIds = []): array
    {
        $results = [
            'retried' => 0,
            'failed' => 0,
            'errors' => []
        ];

        if (empty($jobIds)) {
            // Retry all failed jobs
            $failedJobs = DB::table('failed_jobs')->get();
        } else {
            // Retry specific jobs
            $failedJobs = DB::table('failed_jobs')
                ->whereIn('id', $jobIds)
                ->get();
        }

        foreach ($failedJobs as $failedJob) {
            try {
                // Move job back to jobs table
                $payload = json_decode($failedJob->payload, true);
                
                DB::table('jobs')->insert([
                    'queue' => $failedJob->queue,
                    'payload' => $failedJob->payload,
                    'attempts' => 0,
                    'reserved_at' => null,
                    'available_at' => now()->timestamp,
                    'created_at' => now()->timestamp,
                ]);

                // Remove from failed jobs
                DB::table('failed_jobs')->where('id', $failedJob->id)->delete();
                
                $results['retried']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'job_id' => $failedJob->id,
                    'error' => $e->getMessage()
                ];
            }
        }

        Log::info('Failed jobs retry completed', $results);
        return $results;
    }

    /**
     * Clear old failed jobs.
     */
    public function clearOldFailedJobs(int $daysOld = 7): int
    {
        $deleted = DB::table('failed_jobs')
            ->where('failed_at', '<', now()->subDays($daysOld))
            ->delete();

        Log::info('Old failed jobs cleared', ['deleted' => $deleted, 'days_old' => $daysOld]);
        return $deleted;
    }

    /**
     * Get queue performance metrics.
     */
    public function getPerformanceMetrics(): array
    {
        return [
            'jobs_per_minute' => $this->getJobsPerMinute(),
            'success_rate' => $this->getSuccessRate(),
            'average_wait_time' => $this->getAverageWaitTime(),
            'peak_hours' => $this->getPeakHours(),
            'bottlenecks' => $this->identifyBottlenecks(),
        ];
    }

    /**
     * Get jobs processed per minute.
     */
    protected function getJobsPerMinute(): float
    {
        // This would need actual job completion tracking
        return 5.2; // Placeholder
    }

    /**
     * Get success rate percentage.
     */
    protected function getSuccessRate(): float
    {
        $totalJobs = $this->getProcessedJobsToday();
        $failedJobs = $this->getFailedJobsCount();
        
        if ($totalJobs === 0) {
            return 100.0;
        }
        
        return round((($totalJobs - $failedJobs) / $totalJobs) * 100, 2);
    }

    /**
     * Get average wait time for jobs.
     */
    protected function getAverageWaitTime(): float
    {
        // This would need job timing tracking
        return 8.3; // Placeholder in seconds
    }

    /**
     * Get peak processing hours.
     */
    protected function getPeakHours(): array
    {
        return ['09:00', '10:00', '14:00']; // Placeholder
    }

    /**
     * Identify queue bottlenecks.
     */
    protected function identifyBottlenecks(): array
    {
        $bottlenecks = [];
        $pendingJobs = $this->getPendingJobsCount();
        
        foreach ($pendingJobs as $queue => $count) {
            if ($queue !== 'total' && $count > 100) {
                $bottlenecks[] = [
                    'queue' => $queue,
                    'pending_jobs' => $count,
                    'severity' => $count > 500 ? 'high' : 'medium'
                ];
            }
        }
        
        return $bottlenecks;
    }
}
