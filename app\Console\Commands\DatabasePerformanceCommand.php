<?php

namespace App\Console\Commands;

use App\Services\DatabaseOptimizationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class DatabasePerformanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'db:performance 
                            {--analyze : Analyze database performance}
                            {--report : Generate performance report}
                            {--optimize : Show optimization suggestions}';

    /**
     * The console command description.
     */
    protected $description = 'Analyze and optimize database performance';

    /**
     * Database optimization service.
     */
    protected DatabaseOptimizationService $optimizationService;

    /**
     * Create a new command instance.
     */
    public function __construct(DatabaseOptimizationService $optimizationService)
    {
        parent::__construct();
        $this->optimizationService = $optimizationService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Database Performance Analysis Tool');
        $this->line('=====================================');

        if ($this->option('analyze')) {
            return $this->analyzePerformance();
        }

        if ($this->option('report')) {
            return $this->generateReport();
        }

        if ($this->option('optimize')) {
            return $this->showOptimizations();
        }

        // Default: show all options
        $this->showMenu();
        return 0;
    }

    /**
     * Analyze database performance.
     */
    protected function analyzePerformance(): int
    {
        $this->info('Analyzing database performance...');
        
        $analysis = $this->optimizationService->analyzePerformance();
        
        // Display table sizes
        $this->line("\n📊 Table Sizes:");
        if (isset($analysis['table_sizes']['error'])) {
            $this->error($analysis['table_sizes']['error']);
        } else {
            $headers = ['Table', 'Size (MB)', 'Rows'];
            $rows = [];
            foreach ($analysis['table_sizes'] as $table => $data) {
                $rows[] = [$table, $data['size_mb'], number_format($data['rows'])];
            }
            $this->table($headers, $rows);
        }

        // Display slow queries
        $this->line("\n🐌 Performance Issues:");
        foreach ($analysis['slow_queries'] as $category => $issues) {
            $this->warn($category);
            foreach ($issues as $issue) {
                $this->line("  • $issue");
            }
        }

        // Display missing indexes
        $this->line("\n📈 Index Suggestions:");
        foreach ($analysis['missing_indexes'] as $suggestion) {
            $this->line("  • $suggestion");
        }

        return 0;
    }

    /**
     * Generate performance report.
     */
    protected function generateReport(): int
    {
        $this->info('Generating performance report...');
        
        $report = $this->optimizationService->generatePerformanceReport();
        
        // Display health score
        $healthScore = $report['health_score'];
        $this->line("\n🏥 Database Health Score: {$healthScore['score']}/100 (Grade: {$healthScore['grade']})");
        $this->line("Status: {$healthScore['status']}");
        
        if (!empty($healthScore['issues'])) {
            $this->line("\n⚠️  Issues Found:");
            foreach ($healthScore['issues'] as $issue) {
                $this->warn("  • $issue");
            }
        }

        // Display recommendations
        $recommendations = $report['optimizations'];
        
        $this->line("\n🚀 Immediate Actions:");
        foreach ($recommendations['immediate_actions'] as $action) {
            $this->line("  • $action");
        }

        $this->line("\n📈 Performance Improvements:");
        foreach ($recommendations['performance_improvements'] as $improvement) {
            $this->line("  • $improvement");
        }

        $this->line("\n📊 Monitoring Recommendations:");
        foreach ($recommendations['monitoring'] as $monitor) {
            $this->line("  • $monitor");
        }

        return 0;
    }

    /**
     * Show optimization suggestions.
     */
    protected function showOptimizations(): int
    {
        $this->info('Database Optimization Suggestions');
        
        $optimizations = $this->optimizationService->optimizeCommonQueries();
        
        // Dashboard optimizations
        $this->line("\n📊 Dashboard Optimizations:");
        $dashboard = $optimizations['dashboard'];
        $this->line("Cache Duration: {$dashboard['cache_duration']}");
        foreach ($dashboard['optimizations'] as $opt) {
            $this->line("  • $opt");
        }

        // Listing optimizations
        $this->line("\n📋 Listing Query Optimizations:");
        $listings = $optimizations['listings'];
        foreach ($listings as $key => $value) {
            $this->line("  • " . ucfirst(str_replace('_', ' ', $key)) . ": $value");
        }

        // Relationship optimizations
        $this->line("\n🔗 Relationship Loading Optimizations:");
        $relationships = $optimizations['relationships'];
        
        $this->line("N+1 Query Fixes:");
        foreach ($relationships['n_plus_one_fixes'] as $fix) {
            $this->line("  • $fix");
        }
        
        $this->line("Lazy Loading:");
        foreach ($relationships['lazy_loading'] as $lazy) {
            $this->line("  • $lazy");
        }

        return 0;
    }

    /**
     * Show command menu.
     */
    protected function showMenu(): void
    {
        $this->line("\nAvailable options:");
        $this->line("  --analyze   Analyze current database performance");
        $this->line("  --report    Generate comprehensive performance report");
        $this->line("  --optimize  Show optimization suggestions");
        
        $this->line("\nExamples:");
        $this->line("  php artisan db:performance --analyze");
        $this->line("  php artisan db:performance --report");
        $this->line("  php artisan db:performance --optimize");
    }

    /**
     * Check database connection.
     */
    protected function checkConnection(): bool
    {
        try {
            DB::connection()->getPdo();
            return true;
        } catch (\Exception $e) {
            $this->error('Database connection failed: ' . $e->getMessage());
            return false;
        }
    }
}
