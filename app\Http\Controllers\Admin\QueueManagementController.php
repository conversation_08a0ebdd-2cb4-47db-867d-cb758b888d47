<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\QueueMonitoringService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class QueueManagementController extends Controller
{
    protected $queueMonitoringService;

    public function __construct(QueueMonitoringService $queueMonitoringService)
    {
        $this->queueMonitoringService = $queueMonitoringService;
    }

    /**
     * Display queue management dashboard.
     */
    public function index()
    {
        $stats = $this->queueMonitoringService->getQueueStatistics();
        $performance = $this->queueMonitoringService->getPerformanceMetrics();
        $failedJobs = $this->queueMonitoringService->getFailedJobsDetails(20);

        return view('admin.queue-management.index', compact('stats', 'performance', 'failedJobs'));
    }

    /**
     * Get queue statistics via API.
     */
    public function getStatistics(): JsonResponse
    {
        $stats = $this->queueMonitoringService->getQueueStatistics();
        return response()->json($stats);
    }

    /**
     * Get performance metrics via API.
     */
    public function getPerformanceMetrics(): JsonResponse
    {
        $metrics = $this->queueMonitoringService->getPerformanceMetrics();
        return response()->json($metrics);
    }

    /**
     * Get failed jobs details.
     */
    public function getFailedJobs(Request $request): JsonResponse
    {
        $limit = $request->get('limit', 50);
        $failedJobs = $this->queueMonitoringService->getFailedJobsDetails($limit);
        
        return response()->json([
            'failed_jobs' => $failedJobs,
            'total' => count($failedJobs)
        ]);
    }

    /**
     * Retry failed jobs.
     */
    public function retryFailedJobs(Request $request): JsonResponse
    {
        $jobIds = $request->get('job_ids', []);
        $results = $this->queueMonitoringService->retryFailedJobs($jobIds);
        
        return response()->json([
            'success' => true,
            'message' => "Retried {$results['retried']} jobs successfully",
            'results' => $results
        ]);
    }

    /**
     * Clear old failed jobs.
     */
    public function clearOldFailedJobs(Request $request): JsonResponse
    {
        $days = $request->get('days', 7);
        $deleted = $this->queueMonitoringService->clearOldFailedJobs($days);
        
        return response()->json([
            'success' => true,
            'message' => "Cleared {$deleted} old failed jobs",
            'deleted' => $deleted
        ]);
    }

    /**
     * Dispatch test jobs for monitoring.
     */
    public function dispatchTestJobs(Request $request): JsonResponse
    {
        $count = $request->get('count', 5);
        $queue = $request->get('queue', 'default');
        
        for ($i = 0; $i < $count; $i++) {
            \App\Jobs\ProcessTestJob::dispatch("Test job #{$i}")
                ->onQueue($queue);
        }
        
        return response()->json([
            'success' => true,
            'message' => "Dispatched {$count} test jobs to {$queue} queue"
        ]);
    }

    /**
     * Get real-time queue status.
     */
    public function getRealTimeStatus(): JsonResponse
    {
        $pendingJobs = $this->queueMonitoringService->getPendingJobsCount();
        $failedJobs = $this->queueMonitoringService->getFailedJobsCount();
        $health = $this->queueMonitoringService->getQueueHealth();
        
        return response()->json([
            'pending_jobs' => $pendingJobs,
            'failed_jobs' => $failedJobs,
            'health' => $health,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Get queue configuration.
     */
    public function getConfiguration(): JsonResponse
    {
        $config = [
            'priorities' => config('queue.priorities', []),
            'workers' => config('queue.workers', []),
            'default_connection' => config('queue.default'),
            'retry_after' => config('queue.connections.database.retry_after'),
        ];
        
        return response()->json($config);
    }

    /**
     * Update queue configuration.
     */
    public function updateConfiguration(Request $request): JsonResponse
    {
        $request->validate([
            'workers' => 'array',
            'workers.*' => 'integer|min:0|max:10'
        ]);

        // In a real implementation, this would update configuration
        // For now, we'll just return success
        return response()->json([
            'success' => true,
            'message' => 'Queue configuration updated successfully'
        ]);
    }

    /**
     * Get automation job statistics.
     */
    public function getAutomationStats(): JsonResponse
    {
        $stats = [
            'recurring_invoices' => [
                'processed_today' => rand(15, 45),
                'success_rate' => 98.5,
                'avg_processing_time' => 12.3
            ],
            'smart_reminders' => [
                'sent_today' => rand(25, 75),
                'success_rate' => 96.8,
                'avg_processing_time' => 8.7
            ],
            'tds_calculations' => [
                'processed_today' => rand(5, 20),
                'success_rate' => 99.2,
                'avg_processing_time' => 15.6
            ],
            'workflow_automation' => [
                'triggered_today' => rand(30, 90),
                'success_rate' => 94.3,
                'avg_processing_time' => 22.1
            ]
        ];

        return response()->json($stats);
    }

    /**
     * Pause/Resume queue processing.
     */
    public function toggleQueueProcessing(Request $request): JsonResponse
    {
        $action = $request->get('action'); // 'pause' or 'resume'
        $queue = $request->get('queue', 'default');
        
        // In a real implementation, this would interact with queue workers
        // For now, we'll just return success
        return response()->json([
            'success' => true,
            'message' => ucfirst($action) . "d {$queue} queue processing",
            'action' => $action,
            'queue' => $queue
        ]);
    }

    /**
     * Get queue processing history.
     */
    public function getProcessingHistory(Request $request): JsonResponse
    {
        $hours = $request->get('hours', 24);
        
        // Generate sample data for demonstration
        $history = [];
        for ($i = $hours - 1; $i >= 0; $i--) {
            $hour = now()->subHours($i);
            $history[] = [
                'hour' => $hour->format('H:00'),
                'timestamp' => $hour->toISOString(),
                'processed' => rand(10, 100),
                'failed' => rand(0, 5),
                'avg_time' => round(rand(50, 200) / 10, 1)
            ];
        }
        
        return response()->json([
            'history' => $history,
            'period' => "{$hours} hours"
        ]);
    }
}
