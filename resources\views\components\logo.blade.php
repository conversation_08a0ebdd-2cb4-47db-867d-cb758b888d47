@props(['class' => 'h-8 w-auto', 'showText' => true, 'size' => 'default'])

@php
$brandName = config('app.brand_name', 'Freeligo');
$brandColor = config('app.brand_color', '#10B981');
$brandGradientFrom = config('app.brand_gradient_from', '#059669');
$brandGradientTo = config('app.brand_gradient_to', '#0d9488');

$sizes = [
    'sm' => 'w-6 h-6',
    'default' => 'w-8 h-8',
    'md' => 'w-10 h-10',
    'lg' => 'w-12 h-12',
    'xl' => 'w-16 h-16'
];

$logoSize = $sizes[$size] ?? $sizes['default'];
$textSize = match($size) {
    'sm' => 'text-lg',
    'default' => 'text-xl',
    'md' => 'text-2xl',
    'lg' => 'text-3xl',
    'xl' => 'text-4xl',
    default => 'text-xl'
};
@endphp

<div {{ $attributes->merge(['class' => "flex items-center space-x-2 {$class}"]) }}>
    <!-- Logo Icon -->
    <div class="{{ $logoSize }} rounded-xl flex items-center justify-center shadow-lg"
         style="background: linear-gradient(135deg, {{ $brandGradientFrom }}, {{ $brandGradientTo }});">
        <svg class="w-1/2 h-1/2 text-white" fill="currentColor" viewBox="0 0 24 24">
            <!-- Professional document/freelance icon -->
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
            <path d="M14 2v6h6"/>
            <path d="M16 13H8"/>
            <path d="M16 17H8"/>
            <path d="M10 9H8"/>
        </svg>
    </div>

    @if($showText)
        <!-- Brand Text -->
        <span class="{{ $textSize }} font-bold" style="color: {{ $brandColor }};">
            {{ $brandName }}
        </span>
    @endif
</div>