<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('files', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name'); // Original filename
            $table->string('filename'); // Stored filename (with hash)
            $table->string('path'); // Storage path
            $table->string('disk')->default('public'); // Storage disk
            $table->string('mime_type');
            $table->unsignedBigInteger('size'); // File size in bytes
            $table->string('extension', 10);
            $table->text('description')->nullable();
            $table->json('metadata')->nullable(); // Additional file metadata
            $table->boolean('is_public')->default(false);
            $table->timestamp('last_accessed_at')->nullable();
            $table->timestamps();

            // Indexes for better performance
            $table->index(['user_id', 'created_at']);
            $table->index(['mime_type']);
            $table->index(['extension']);
            $table->index(['is_public']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('files');
    }
};
