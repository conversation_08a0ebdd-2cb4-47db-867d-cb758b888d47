<?php

namespace Database\Factories;

use App\Models\TdsRecord;
use App\Models\User;
use App\Models\Client;
use App\Models\Invoice;
use Illuminate\Database\Eloquent\Factories\Factory;

class TdsRecordFactory extends Factory
{
    protected $model = TdsRecord::class;

    public function definition(): array
    {
        $invoiceAmount = fake()->randomFloat(2, 10000, 100000);
        $tdsPercentage = fake()->randomElement([2, 10]);
        $tdsAmount = ($invoiceAmount * $tdsPercentage) / 100;
        $netReceived = $invoiceAmount - $tdsAmount;

        return [
            'user_id' => User::factory(),
            'client_id' => Client::factory(),
            'invoice_id' => Invoice::factory(),
            'financial_year' => fake()->randomElement(['2023-24', '2024-25', '2025-26']),
            'invoice_amount' => $invoiceAmount,
            'tds_percentage' => $tdsPercentage,
            'tds_amount' => $tdsAmount,
            'net_received' => $netReceived,
            'deduction_date' => fake()->dateTimeBetween('-1 year', 'now'),
            'tds_certificate_number' => fake()->optional()->regexify('[A-Z]{3}[0-9]{7}'),
        ];
    }

    public function currentFinancialYear(): static
    {
        return $this->state(fn (array $attributes) => [
            'financial_year' => '2024-25',
        ]);
    }

    public function previousFinancialYear(): static
    {
        return $this->state(fn (array $attributes) => [
            'financial_year' => '2023-24',
        ]);
    }

    public function q1(): static
    {
        return $this->state(fn (array $attributes) => [
            'deduction_date' => fake()->dateTimeBetween('2024-04-01', '2024-06-30'),
        ]);
    }

    public function q2(): static
    {
        return $this->state(fn (array $attributes) => [
            'deduction_date' => fake()->dateTimeBetween('2024-07-01', '2024-09-30'),
        ]);
    }

    public function q3(): static
    {
        return $this->state(fn (array $attributes) => [
            'deduction_date' => fake()->dateTimeBetween('2024-10-01', '2024-12-31'),
        ]);
    }

    public function q4(): static
    {
        return $this->state(fn (array $attributes) => [
            'deduction_date' => fake()->dateTimeBetween('2025-01-01', '2025-03-31'),
        ]);
    }

    public function withCertificate(): static
    {
        return $this->state(fn (array $attributes) => [
            'tds_certificate_number' => fake()->regexify('[A-Z]{3}[0-9]{7}'),
        ]);
    }

    public function withoutCertificate(): static
    {
        return $this->state(fn (array $attributes) => [
            'tds_certificate_number' => null,
        ]);
    }

    public function tds2Percent(): static
    {
        return $this->state(function (array $attributes) {
            $tdsAmount = ($attributes['invoice_amount'] * 2) / 100;
            return [
                'tds_percentage' => 2,
                'tds_amount' => $tdsAmount,
            ];
        });
    }

    public function tds10Percent(): static
    {
        return $this->state(function (array $attributes) {
            $tdsAmount = ($attributes['invoice_amount'] * 10) / 100;
            return [
                'tds_percentage' => 10,
                'tds_amount' => $tdsAmount,
            ];
        });
    }

    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    public function forClient(Client $client): static
    {
        return $this->state(fn (array $attributes) => [
            'client_id' => $client->id,
            'user_id' => $client->user_id,
        ]);
    }

    public function forInvoice(Invoice $invoice): static
    {
        return $this->state(function (array $attributes) use ($invoice) {
            $netReceived = $invoice->total_amount - $invoice->tds_amount;
            return [
                'invoice_id' => $invoice->id,
                'user_id' => $invoice->user_id,
                'client_id' => $invoice->client_id,
                'invoice_amount' => $invoice->total_amount,
                'tds_amount' => $invoice->tds_amount,
                'tds_percentage' => $invoice->tds_percentage,
                'net_received' => $netReceived,
            ];
        });
    }
}
