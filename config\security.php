<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Security Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains security-related configuration options for the
    | Freeligo application in production environment.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | HTTPS Configuration
    |--------------------------------------------------------------------------
    */
    'force_https' => env('FORCE_HTTPS', env('APP_ENV') === 'production'),
    'secure_cookies' => env('SECURE_COOKIES', env('APP_ENV') === 'production'),
    'same_site_cookies' => env('SAME_SITE_COOKIES', 'strict'),

    /*
    |--------------------------------------------------------------------------
    | Content Security Policy
    |--------------------------------------------------------------------------
    */
    'csp' => [
        'enabled' => env('CSP_ENABLED', env('APP_ENV') === 'production'),
        'report_only' => env('CSP_REPORT_ONLY', env('APP_ENV') !== 'production'),
        'directives' => [
            'default-src' => "'self'",
            'script-src' => "'self' 'unsafe-inline' 'unsafe-eval' https://js.paypal.com https://www.paypal.com https://checkout.razorpay.com https://cdn.tailwindcss.com",
            'style-src' => "'self' 'unsafe-inline' https://fonts.googleapis.com https://cdn.tailwindcss.com",
            'font-src' => "'self' https://fonts.gstatic.com data:",
            'img-src' => "'self' data: https: blob:",
            'connect-src' => "'self' https://api.paypal.com https://api.razorpay.com ws: wss:",
            'frame-src' => "'self' https://js.paypal.com https://www.paypal.com",
            'object-src' => "'none'",
            'base-uri' => "'self'",
            'form-action' => "'self'",
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Headers
    |--------------------------------------------------------------------------
    */
    'headers' => [
        'x-frame-options' => 'DENY',
        'x-content-type-options' => 'nosniff',
        'x-xss-protection' => '1; mode=block',
        'referrer-policy' => 'strict-origin-when-cross-origin',
        'permissions-policy' => 'camera=(), microphone=(), geolocation=(), payment=()',
        'strict-transport-security' => 'max-age=31536000; includeSubDomains; preload',
        'x-permitted-cross-domain-policies' => 'none',
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limiting' => [
        'enabled' => true,
        'per_minute' => env('RATE_LIMIT_PER_MINUTE', 60),
        'api_per_minute' => env('API_RATE_LIMIT_PER_MINUTE', 100),
        'login_attempts' => 5,
        'login_decay_minutes' => 15,
    ],

    /*
    |--------------------------------------------------------------------------
    | File Upload Security
    |--------------------------------------------------------------------------
    */
    'uploads' => [
        'max_file_size' => 10240, // 10MB in KB
        'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx'],
        'allowed_mime_types' => [
            'image/jpeg',
            'image/png',
            'image/gif',
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ],
        'scan_uploads' => env('SCAN_UPLOADS', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Security
    |--------------------------------------------------------------------------
    */
    'session' => [
        'regenerate_on_login' => true,
        'invalidate_on_logout' => true,
        'timeout_minutes' => 120,
    ],

];
