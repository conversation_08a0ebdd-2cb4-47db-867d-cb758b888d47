<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->unsignedBigInteger('contract_template_id')->nullable();
            $table->string('title');
            $table->text('content');
            $table->json('variables')->nullable(); // Store filled template variables
            $table->enum('status', ['draft', 'sent', 'signed', 'cancelled'])->default('draft');
            $table->date('sent_date')->nullable();
            $table->string('pdf_path')->nullable();
            $table->date('signed_date')->nullable();
            $table->date('expiry_date')->nullable();
            $table->string('signed_by')->nullable();
            $table->date('cancelled_date')->nullable();

            // Performance indexes
            $table->index('status');
            $table->index(['user_id', 'status']);
            $table->index(['client_id', 'status']);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contracts');
    }
};
