<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\Plan;
use App\Models\UserSubscription;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;

class AssignFreePlanToUsers extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'users:assign-free-plan 
                            {--dry-run : Show what would be done without making changes}
                            {--force : Force assignment even if user has a plan}';

    /**
     * The console command description.
     */
    protected $description = 'Assign free plan to users who don\'t have any plan assigned';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        // Get free plan
        $freePlan = Plan::where('slug', 'free')->where('is_active', true)->first();
        
        if (!$freePlan) {
            $this->error('Free plan not found! Please run the PlanSeeder first.');
            return Command::FAILURE;
        }

        // Get users without plans (excluding admins)
        $query = User::whereDoesntHave('roles', function ($q) {
            $q->where('name', 'admin');
        });

        if (!$force) {
            $query->whereNull('current_plan_id');
        }

        $users = $query->get();

        if ($users->isEmpty()) {
            $this->info('No users found that need plan assignment.');
            return Command::SUCCESS;
        }

        $this->info("Found {$users->count()} users that need free plan assignment:");

        $headers = ['ID', 'Name', 'Email', 'Current Plan', 'Action'];
        $rows = [];

        foreach ($users as $user) {
            $currentPlan = $user->currentPlan ? $user->currentPlan->name : 'None';
            $action = $dryRun ? 'Would assign Free plan' : 'Assigning Free plan';
            
            $rows[] = [
                $user->id,
                $user->name,
                $user->email,
                $currentPlan,
                $action
            ];
        }

        $this->table($headers, $rows);

        if ($dryRun) {
            $this->info('This was a dry run. Use --force to actually assign plans.');
            return Command::SUCCESS;
        }

        if (!$this->confirm('Do you want to proceed with assigning free plans to these users?')) {
            $this->info('Operation cancelled.');
            return Command::SUCCESS;
        }

        $successCount = 0;
        $errorCount = 0;

        foreach ($users as $user) {
            try {
                DB::transaction(function () use ($user, $freePlan) {
                    // Cancel any existing active subscriptions
                    $user->subscriptions()->where('status', 'active')->update(['status' => 'cancelled']);

                    // Create new free subscription
                    UserSubscription::create([
                        'user_id' => $user->id,
                        'plan_id' => $freePlan->id,
                        'status' => 'active',
                        'starts_at' => now(),
                        'amount_paid' => 0,
                        'currency' => config('services.currency.code', 'USD'),
                    ]);

                    // Update user's current plan
                    $user->update(['current_plan_id' => $freePlan->id]);
                });

                $successCount++;
                $this->info("✓ Assigned free plan to {$user->name} ({$user->email})");

            } catch (\Exception $e) {
                $errorCount++;
                $this->error("✗ Failed to assign plan to {$user->name} ({$user->email}): {$e->getMessage()}");
            }
        }

        $this->info("\nOperation completed:");
        $this->info("✓ Successfully assigned: {$successCount}");
        if ($errorCount > 0) {
            $this->error("✗ Failed: {$errorCount}");
        }

        return Command::SUCCESS;
    }
}
