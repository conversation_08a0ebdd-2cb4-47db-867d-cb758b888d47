<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tds_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->string('financial_year'); // e.g., '2024-25'
            $table->decimal('invoice_amount', 12, 2);
            $table->decimal('tds_percentage', 5, 2);
            $table->decimal('tds_amount', 12, 2);
            $table->decimal('net_received', 12, 2);
            $table->date('deduction_date');
            $table->string('tds_certificate_number')->nullable();

            // Performance indexes
            $table->index('financial_year');
            $table->index('deduction_date');
            $table->index(['user_id', 'financial_year']);
            $table->index(['client_id', 'financial_year']);

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tds_records');
    }
};
