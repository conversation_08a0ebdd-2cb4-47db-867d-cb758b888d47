<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tds_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->string('financial_year'); // e.g., '2024-25'
            $table->decimal('invoice_amount', 12, 2);
            $table->decimal('tds_percentage', 5, 2);
            $table->decimal('tds_amount', 12, 2);
            $table->decimal('net_received', 12, 2);
            $table->date('deduction_date');
            $table->string('tds_certificate_number')->nullable();

            // Enhanced automation fields
            $table->boolean('is_auto_calculated')->default(false);
            $table->string('calculation_method')->nullable();
            $table->json('automation_metadata')->nullable();
            $table->decimal('suggested_tds_rate', 5, 2)->nullable();
            $table->text('rate_justification')->nullable();
            $table->string('compliance_status')->default('pending');
            $table->json('compliance_checks')->nullable();
            $table->timestamp('compliance_verified_at')->nullable();
            $table->unsignedBigInteger('verified_by')->nullable();
            $table->string('certificate_status')->default('pending');
            $table->timestamp('certificate_requested_at')->nullable();
            $table->timestamp('certificate_received_at')->nullable();
            $table->string('certificate_file_path')->nullable();
            $table->json('ai_insights')->nullable();
            $table->decimal('confidence_score', 5, 2)->nullable();

            // Performance indexes
            $table->index('financial_year');
            $table->index('deduction_date');
            $table->index(['user_id', 'financial_year']);
            $table->index(['client_id', 'financial_year']);
            $table->index('is_auto_calculated');
            $table->index('compliance_status');
            $table->index('certificate_status');
            $table->index('calculation_method');
            $table->index('compliance_verified_at');

            $table->timestamps();
        });

        // Create TDS rate configurations table
        Schema::create('tds_rate_configurations', function (Blueprint $table) {
            $table->id();
            $table->string('service_type'); // 'professional_services', 'technical_services', etc.
            $table->string('client_type'); // 'individual', 'company', 'government'
            $table->decimal('tds_rate', 5, 2);
            $table->decimal('threshold_amount', 12, 2)->nullable(); // Minimum amount for TDS applicability
            $table->string('section_code'); // '194J', '194C', etc.
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->string('financial_year');
            $table->json('conditions')->nullable(); // Additional conditions for rate applicability
            $table->timestamps();

            $table->index(['service_type', 'client_type', 'financial_year'], 'tds_rates_service_client_year_idx');
            $table->index('is_active');
            $table->index('financial_year');
        });

        // Create TDS automation rules table
        Schema::create('tds_automation_rules', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('rule_name');
            $table->json('conditions'); // Conditions for rule application
            $table->decimal('tds_rate', 5, 2);
            $table->string('section_code');
            $table->boolean('auto_apply')->default(true);
            $table->boolean('require_confirmation')->default(false);
            $table->integer('priority')->default(1); // Higher number = higher priority
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->index(['user_id', 'is_active']);
            $table->index('priority');
        });

        // Create TDS compliance alerts table
        Schema::create('tds_compliance_alerts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('alert_type'); // 'quarterly_filing', 'certificate_missing', 'rate_mismatch'
            $table->string('severity'); // 'low', 'medium', 'high', 'critical'
            $table->string('title');
            $table->text('description');
            $table->json('metadata')->nullable(); // Additional alert data
            $table->string('status')->default('active'); // 'active', 'resolved', 'dismissed'
            $table->timestamp('due_date')->nullable();
            $table->timestamp('resolved_at')->nullable();
            $table->text('resolution_notes')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index('alert_type');
            $table->index('severity');
            $table->index('due_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tds_compliance_alerts');
        Schema::dropIfExists('tds_automation_rules');
        Schema::dropIfExists('tds_rate_configurations');
        Schema::dropIfExists('tds_records');
    }
};
