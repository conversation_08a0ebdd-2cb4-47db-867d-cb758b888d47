<x-app-layout>
    <x-slot name="header">
        <div class="md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Automation Dashboard
                </h1>
                <p class="mt-1 text-sm text-gray-500">
                    Monitor and manage your automation processes
                </p>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4 space-x-3">
                <select id="period-selector" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <option value="7_days" {{ $period === '7_days' ? 'selected' : '' }}>Last 7 Days</option>
                    <option value="30_days" {{ $period === '30_days' ? 'selected' : '' }}>Last 30 Days</option>
                    <option value="90_days" {{ $period === '90_days' ? 'selected' : '' }}>Last 90 Days</option>
                </select>
                <a href="{{ route('automation.workflows') }}" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    <i class="fas fa-cog mr-2"></i>
                    Manage Workflows
                </a>
            </div>
        </div>
    </x-slot>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Automation Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Recurring Invoices -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-file-invoice text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Recurring Invoices</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $automationStats['recurring_invoices']['active'] }}/{{ $automationStats['recurring_invoices']['total'] }}</dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="text-sm text-gray-600">
                            {{ $automationStats['recurring_invoices']['generated_this_period'] }} generated this period
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Reminders -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-bell text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Payment Reminders</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $automationStats['payment_reminders']['total_sent'] }}</dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="text-sm text-gray-600">
                            {{ $automationStats['payment_reminders']['successful'] }} successful deliveries
                        </div>
                    </div>
                </div>
            </div>

            <!-- TDS Calculations -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-calculator text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">TDS Auto-Calculated</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $automationStats['tds_calculations']['auto_calculated'] }}</dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="text-sm text-gray-600">
                            {{ $automationStats['tds_calculations']['compliance_rate'] }}% compliance rate
                        </div>
                    </div>
                </div>
            </div>

            <!-- Workflow Executions -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Workflow Executions</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $automationStats['workflow_executions']['total'] }}</dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-3">
                        <div class="text-sm text-gray-600">
                            {{ $automationStats['workflow_executions']['successful'] }} successful
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Workflows and Recent Executions -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Active Workflows -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Active Workflows</h3>
                        <a href="{{ route('automation.workflows') }}" class="text-sm text-indigo-600 hover:text-indigo-500">
                            View All
                        </a>
                    </div>
                    <div class="flow-root">
                        <ul class="-my-5 divide-y divide-gray-200">
                            @forelse($activeWorkflows as $workflow)
                            <li class="py-4">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                            <i class="fas fa-play text-white text-xs"></i>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {{ $workflow['name'] }}
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            {{ $workflow['trigger_type'] }} • {{ $workflow['executions_count'] ?? 0 }} executions
                                        </p>
                                    </div>
                                    <div class="flex-shrink-0">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    </div>
                                </div>
                            </li>
                            @empty
                            <li class="py-4 text-center text-gray-500">
                                No active workflows found
                                <div class="mt-2">
                                    <a href="{{ route('automation.workflows') }}" class="text-indigo-600 hover:text-indigo-500">
                                        Create your first workflow
                                    </a>
                                </div>
                            </li>
                            @endforelse
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Recent Executions -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Executions</h3>
                    <div class="flow-root">
                        <ul class="-my-5 divide-y divide-gray-200">
                            @forelse($recentExecutions as $execution)
                            <li class="py-4">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        @if($execution['status'] === 'completed')
                                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-white text-xs"></i>
                                            </div>
                                        @elseif($execution['status'] === 'failed')
                                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                                <i class="fas fa-times text-white text-xs"></i>
                                            </div>
                                        @else
                                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                                <i class="fas fa-clock text-white text-xs"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {{ $execution['workflow']['name'] ?? 'Unknown Workflow' }}
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            {{ \Carbon\Carbon::parse($execution['created_at'])->diffForHumans() }}
                                        </p>
                                    </div>
                                    <div class="flex-shrink-0 text-sm text-gray-500">
                                        {{ $execution['execution_time'] ? round($execution['execution_time'], 2) . 's' : '-' }}
                                    </div>
                                </div>
                            </li>
                            @empty
                            <li class="py-4 text-center text-gray-500">
                                No recent executions found
                            </li>
                            @endforelse
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Automation Settings -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Automation Settings</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">Email Notifications</p>
                            <p class="text-sm text-gray-500">Receive automation alerts via email</p>
                        </div>
                        <div class="ml-4">
                            <input type="checkbox" 
                                   class="automation-setting h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" 
                                   data-setting="email_notifications"
                                   {{ $automationSettings['email_notifications'] ? 'checked' : '' }}>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">Auto Invoice Generation</p>
                            <p class="text-sm text-gray-500">Automatically generate recurring invoices</p>
                        </div>
                        <div class="ml-4">
                            <input type="checkbox" 
                                   class="automation-setting h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" 
                                   data-setting="auto_invoice_generation"
                                   {{ $automationSettings['auto_invoice_generation'] ? 'checked' : '' }}>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">Auto Payment Reminders</p>
                            <p class="text-sm text-gray-500">Send automatic payment reminders</p>
                        </div>
                        <div class="ml-4">
                            <input type="checkbox" 
                                   class="automation-setting h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" 
                                   data-setting="auto_payment_reminders"
                                   {{ $automationSettings['auto_payment_reminders'] ? 'checked' : '' }}>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">Auto TDS Calculation</p>
                            <p class="text-sm text-gray-500">Automatically calculate TDS amounts</p>
                        </div>
                        <div class="ml-4">
                            <input type="checkbox" 
                                   class="automation-setting h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" 
                                   data-setting="auto_tds_calculation"
                                   {{ $automationSettings['auto_tds_calculation'] ? 'checked' : '' }}>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-gray-900">SMS Notifications</p>
                            <p class="text-sm text-gray-500">Receive alerts via SMS</p>
                        </div>
                        <div class="ml-4">
                            <input type="checkbox" 
                                   class="automation-setting h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded" 
                                   data-setting="sms_notifications"
                                   {{ $automationSettings['sms_notifications'] ? 'checked' : '' }}>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</x-app-layout>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Period selector change handler
    document.getElementById('period-selector').addEventListener('change', function() {
        const period = this.value;
        window.location.href = `{{ route('automation.dashboard') }}?period=${period}`;
    });

    // Automation settings change handlers
    document.querySelectorAll('.automation-setting').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const setting = this.dataset.setting;
            const enabled = this.checked;
            
            updateAutomationSetting(setting, enabled);
        });
    });
});

function updateAutomationSetting(setting, enabled) {
    fetch('{{ route("automation.settings.update") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            setting: setting,
            enabled: enabled
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Setting updated successfully', 'success');
        } else {
            showNotification('Failed to update setting', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endpush
