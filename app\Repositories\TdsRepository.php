<?php

namespace App\Repositories;

use App\Models\TdsRecord;
use App\Traits\HasDateRanges;
use App\Traits\HasFiltering;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class TdsRepository extends BaseRepository
{
    use HasDateRanges, HasFiltering;

    protected function getModel(): Model
    {
        return new TdsRecord();
    }

    /**
     * Get TDS records for a specific user with filters
     */
    public function getForUser(int $userId, Request $request = null): LengthAwarePaginator
    {
        $query = $this->model->newQuery()
            ->with(['client', 'invoice'])
            ->where('user_id', $userId);

        if ($request) {
            $query = $this->applyFilters($query, $request, [
                'search_fields' => ['client.name', 'client.company_name', 'invoice.invoice_number'],
                'date_field' => 'deduction_date',
                'sort_by' => 'deduction_date',
                'sort_direction' => 'desc',
            ]);

            // Apply financial year filter
            if ($request->filled('financial_year')) {
                $query->forFinancialYear($request->financial_year);
            }

            // Apply client filter
            if ($request->filled('client_id')) {
                $query->where('client_id', $request->client_id);
            }

            return $this->applyPagination($query, $request);
        }

        return $query->latest('deduction_date')->paginate(15);
    }

    /**
     * Get TDS records for specific financial year
     */
    public function getForFinancialYear(int $userId, string $financialYear): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->forFinancialYear($financialYear)
            ->with(['client', 'invoice'])
            ->orderBy('deduction_date')
            ->get();
    }

    /**
     * Get TDS records for specific client
     */
    public function getForClient(int $clientId, string $financialYear = null): Collection
    {
        $query = $this->model->newQuery()
            ->where('client_id', $clientId)
            ->with(['invoice']);

        if ($financialYear) {
            $query->forFinancialYear($financialYear);
        }

        return $query->orderBy('deduction_date')->get();
    }

    /**
     * Get TDS summary for user by financial year
     */
    public function getSummaryForUser(int $userId, string $financialYear): array
    {
        $records = $this->getForFinancialYear($userId, $financialYear);

        return [
            'total_records' => $records->count(),
            'total_invoice_amount' => $records->sum('invoice_amount'),
            'total_tds_amount' => $records->sum('tds_amount'),
            'total_net_received' => $records->sum('net_received'),
            'avg_tds_percentage' => $records->avg('tds_percentage'),
            'client_wise_summary' => $this->getClientWiseSummary($records),
            'monthly_breakdown' => $this->getMonthlyBreakdown($records),
        ];
    }

    /**
     * Get TDS records by date range
     */
    public function getByDateRange(int $userId, string $startDate, string $endDate): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->whereBetween('deduction_date', [$startDate, $endDate])
            ->with(['client', 'invoice'])
            ->orderBy('deduction_date')
            ->get();
    }

    /**
     * Get quarterly TDS summary
     */
    public function getQuarterlySummary(int $userId, string $financialYear): array
    {
        $records = $this->getForFinancialYear($userId, $financialYear);
        
        $quarters = [
            'Q1' => ['04', '05', '06'], // Apr-Jun
            'Q2' => ['07', '08', '09'], // Jul-Sep
            'Q3' => ['10', '11', '12'], // Oct-Dec
            'Q4' => ['01', '02', '03'], // Jan-Mar
        ];

        $quarterlySummary = [];
        
        foreach ($quarters as $quarter => $months) {
            $quarterRecords = $records->filter(function ($record) use ($months) {
                return in_array($record->deduction_date->format('m'), $months);
            });

            $quarterlySummary[$quarter] = [
                'total_records' => $quarterRecords->count(),
                'total_invoice_amount' => $quarterRecords->sum('invoice_amount'),
                'total_tds_amount' => $quarterRecords->sum('tds_amount'),
                'total_net_received' => $quarterRecords->sum('net_received'),
            ];
        }

        return $quarterlySummary;
    }

    /**
     * Get TDS statistics for user
     */
    public function getStatsForUser(int $userId): array
    {
        $currentFinancialYear = $this->getCurrentFinancialYear();
        $baseQuery = $this->model->newQuery()->where('user_id', $userId);

        return [
            'total_records' => $baseQuery->count(),
            'current_year_records' => (clone $baseQuery)->forFinancialYear($currentFinancialYear)->count(),
            'total_tds_amount' => (clone $baseQuery)->sum('tds_amount'),
            'current_year_tds' => (clone $baseQuery)->forFinancialYear($currentFinancialYear)->sum('tds_amount'),
            'total_invoice_amount' => (clone $baseQuery)->sum('invoice_amount'),
            'current_year_invoice_amount' => (clone $baseQuery)->forFinancialYear($currentFinancialYear)->sum('invoice_amount'),
            'avg_tds_percentage' => (clone $baseQuery)->avg('tds_percentage'),
            'unique_clients' => (clone $baseQuery)->distinct('client_id')->count(),
        ];
    }

    /**
     * Search TDS records with advanced filters
     */
    public function searchTdsRecords(int $userId, array $filters): Collection
    {
        $query = $this->model->newQuery()
            ->where('user_id', $userId)
            ->with(['client', 'invoice']);

        if (!empty($filters['financial_year'])) {
            $query->forFinancialYear($filters['financial_year']);
        }

        if (!empty($filters['client_id'])) {
            $query->where('client_id', $filters['client_id']);
        }

        if (!empty($filters['start_date'])) {
            $query->whereDate('deduction_date', '>=', $filters['start_date']);
        }

        if (!empty($filters['end_date'])) {
            $query->whereDate('deduction_date', '<=', $filters['end_date']);
        }

        if (!empty($filters['min_amount'])) {
            $query->where('tds_amount', '>=', $filters['min_amount']);
        }

        if (!empty($filters['max_amount'])) {
            $query->where('tds_amount', '<=', $filters['max_amount']);
        }

        if (!empty($filters['tds_percentage'])) {
            $query->where('tds_percentage', $filters['tds_percentage']);
        }

        return $query->orderBy('deduction_date', 'desc')->get();
    }

    /**
     * Get client-wise TDS summary
     */
    private function getClientWiseSummary(Collection $records): array
    {
        return $records->groupBy('client_id')->map(function ($clientRecords) {
            $client = $clientRecords->first()->client;
            
            return [
                'client_name' => $client->name,
                'company_name' => $client->company_name,
                'total_records' => $clientRecords->count(),
                'total_invoice_amount' => $clientRecords->sum('invoice_amount'),
                'total_tds_amount' => $clientRecords->sum('tds_amount'),
                'total_net_received' => $clientRecords->sum('net_received'),
                'avg_tds_percentage' => $clientRecords->avg('tds_percentage'),
            ];
        })->values()->toArray();
    }

    /**
     * Get monthly breakdown of TDS records
     */
    private function getMonthlyBreakdown(Collection $records): array
    {
        return $records->groupBy(function ($record) {
            return $record->deduction_date->format('Y-m');
        })->map(function ($monthRecords, $month) {
            return [
                'month' => $month,
                'total_records' => $monthRecords->count(),
                'total_invoice_amount' => $monthRecords->sum('invoice_amount'),
                'total_tds_amount' => $monthRecords->sum('tds_amount'),
                'total_net_received' => $monthRecords->sum('net_received'),
            ];
        })->values()->toArray();
    }

    /**
     * Get TDS records for export
     */
    public function getForExport(int $userId, string $financialYear, int $clientId = null): Collection
    {
        $query = $this->model->newQuery()
            ->where('user_id', $userId)
            ->forFinancialYear($financialYear)
            ->with(['client', 'invoice']);

        if ($clientId) {
            $query->where('client_id', $clientId);
        }

        return $query->orderBy('deduction_date')->get();
    }
}
