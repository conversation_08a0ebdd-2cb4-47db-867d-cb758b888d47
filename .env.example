APP_NAME=Freeligo
APP_ENV=production
APP_KEY=
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Brand Configuration
BRAND_NAME=Freeligo
BRAND_COLOR=#10B981
BRAND_GRADIENT_FROM=#059669
BRAND_GRADIENT_TO=#0d9488

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database

# Security Configuration
FORCE_HTTPS=true
SECURE_COOKIES=true
SAME_SITE_COOKIES=strict

# Performance Configuration
PHP_CLI_SERVER_WORKERS=4
BCRYPT_ROUNDS=12

# Logging Configuration
LOG_CHANNEL=stack
LOG_STACK=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

# Database Configuration (Production)
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=freeligo_production
DB_USERNAME=freeligo_user
DB_PASSWORD=your_secure_database_password
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci
DB_SSL_MODE=REQUIRED
DB_SSL_VERIFY_SERVER_CERT=true
DB_TIMEOUT=30

# Database SSL Certificates
MYSQL_ATTR_SSL_CA=/path/to/ca-cert.pem
MYSQL_ATTR_SSL_CERT=/path/to/client-cert.pem
MYSQL_ATTR_SSL_KEY=/path/to/client-key.pem
MYSQL_ATTR_SSL_CIPHER=

# Session Configuration (Production)
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=.yourdomain.com
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=strict

# Storage & Queue Configuration
BROADCAST_CONNECTION=log
FILESYSTEM_DISK=s3
QUEUE_CONNECTION=redis

# Cache Configuration (Production)
CACHE_STORE=redis
CACHE_PREFIX=freeligo_cache

# Redis Configuration (Production)
REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=your_secure_redis_password
REDIS_PORT=6379
REDIS_DB=0
REDIS_CACHE_DB=1
REDIS_SESSION_DB=2

# Memcached Configuration
MEMCACHED_HOST=127.0.0.1

# Mail Configuration (Production)
MAIL_MAILER=smtp
MAIL_HOST=smtp.mailgun.org
MAIL_PORT=587
MAIL_USERNAME=your_mailgun_username
MAIL_PASSWORD=your_mailgun_password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# AWS S3 Configuration (Production)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=freeligo-production-bucket
AWS_USE_PATH_STYLE_ENDPOINT=false

# Frontend Configuration
VITE_APP_NAME="${APP_NAME}"

# Currency Configuration
APP_CURRENCY=USD
APP_CURRENCY_SYMBOL=$

# PayPal Configuration (Production)
PAYPAL_CLIENT_ID=your_production_paypal_client_id
PAYPAL_CLIENT_SECRET=your_production_paypal_client_secret
PAYPAL_MODE=live
PAYPAL_WEBHOOK_ID=your_paypal_webhook_id
PAYPAL_WEBHOOK_SECRET=your_paypal_webhook_secret

# Razorpay Configuration (Hidden for International)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_KEY_SECRET=your_razorpay_key_secret
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret

# OpenAI Configuration (AI Features)
OPENAI_API_KEY=your_openai_api_key
OPENAI_ORGANIZATION=your_openai_organization_id
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7
OPENAI_TIMEOUT=30

# Groq Configuration (Fast AI Alternative)
GROQ_API_KEY=your_groq_api_key
GROQ_MODEL=meta-llama/llama-4-scout-17b-16e-instruct
GROQ_MAX_TOKENS=1000
GROQ_TEMPERATURE=0.7
GROQ_TIMEOUT=30

# AI Provider Selection (openai or groq)
AI_PROVIDER=openai

# Security & Monitoring
SENTRY_LARAVEL_DSN=your_sentry_dsn
TELESCOPE_ENABLED=false

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
API_RATE_LIMIT_PER_MINUTE=100
RATE_LIMIT_LOGIN=5
RATE_LIMIT_CONTACT=5
RATE_LIMIT_PAYMENT=10

# Monitoring & Alerting Configuration
MONITORING_ENABLED=true
MONITORING_WEBHOOK_URL=
SLACK_WEBHOOK_URL=
HEALTH_CHECK_TOKEN=

# Performance Monitoring
PERFORMANCE_MONITORING_ENABLED=true
SLOW_QUERY_THRESHOLD=1000
MEMORY_ALERT_THRESHOLD=512
DISK_ALERT_THRESHOLD=90

# Log Retention (days)
LOG_RETENTION_DAYS=30
SECURITY_LOG_RETENTION_DAYS=90
PERFORMANCE_LOG_RETENTION_DAYS=7

# CORS Configuration
CORS_ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com

# Content Security Policy
CSP_ENABLED=true
CSP_REPORT_ONLY=false
