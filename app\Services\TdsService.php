<?php

namespace App\Services;

use App\Models\TdsRecord;
use App\Models\Invoice;
use App\Models\Client;
use App\Models\User;
use App\Models\TdsRateConfiguration;
use App\Models\TdsAutomationRule;
use App\Models\TdsComplianceAlert;
use App\Repositories\TdsRepository;
use App\Services\AIServiceFactory;
use App\Helpers\PlanChecker;
use App\Traits\HasCalculations;
use App\Traits\HasDateRanges;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class TdsService
{
    use HasCalculations, HasDateRanges;

    protected TdsRepository $tdsRepository;
    protected $aiService;

    public function __construct(TdsRepository $tdsRepository)
    {
        $this->tdsRepository = $tdsRepository;
        $this->aiService = AIServiceFactory::create();
    }

    /**
     * Create a new TDS record
     */
    public function createTdsRecord(array $data): TdsRecord
    {
        $data['user_id'] = Auth::id();
        $data['financial_year'] = $data['financial_year'] ?? $this->getCurrentFinancialYear();
        
        // Calculate TDS amount if not provided
        if (empty($data['tds_amount']) && !empty($data['invoice_amount']) && !empty($data['tds_percentage'])) {
            $data['tds_amount'] = $this->calculateTdsAmount($data['invoice_amount'], $data['tds_percentage']);
        }

        // Calculate net received if not provided
        if (empty($data['net_received']) && !empty($data['invoice_amount']) && !empty($data['tds_amount'])) {
            $data['net_received'] = $data['invoice_amount'] - $data['tds_amount'];
        }

        return $this->tdsRepository->create($data);
    }

    /**
     * Update an existing TDS record
     */
    public function updateTdsRecord(TdsRecord $tdsRecord, array $data): bool
    {
        // Recalculate TDS amount if invoice amount or percentage changed
        if (isset($data['invoice_amount']) || isset($data['tds_percentage'])) {
            $invoiceAmount = $data['invoice_amount'] ?? $tdsRecord->invoice_amount;
            $tdsPercentage = $data['tds_percentage'] ?? $tdsRecord->tds_percentage;
            $data['tds_amount'] = $this->calculateTdsAmount($invoiceAmount, $tdsPercentage);
            $data['net_received'] = $invoiceAmount - $data['tds_amount'];
        }

        return $this->tdsRepository->update($tdsRecord, $data);
    }

    /**
     * Delete a TDS record
     */
    public function deleteTdsRecord(TdsRecord $tdsRecord): bool
    {
        return $this->tdsRepository->delete($tdsRecord);
    }

    /**
     * Get TDS records for user
     */
    public function getTdsRecordsForUser(int $userId, $request = null)
    {
        return $this->tdsRepository->getForUser($userId, $request);
    }

    /**
     * Get TDS summary for financial year
     */
    public function getTdsSummary(int $userId, string $financialYear): array
    {
        return $this->tdsRepository->getSummaryForUser($userId, $financialYear);
    }

    /**
     * Get quarterly TDS summary
     */
    public function getQuarterlySummary(int $userId, string $financialYear): array
    {
        return $this->tdsRepository->getQuarterlySummary($userId, $financialYear);
    }

    /**
     * Get TDS statistics
     */
    public function getTdsStats(int $userId): array
    {
        return $this->tdsRepository->getStatsForUser($userId);
    }

    /**
     * Generate TDS certificate data
     */
    public function generateTdsCertificate(int $userId, string $financialYear, int $clientId = null): array
    {
        $records = $clientId 
            ? $this->tdsRepository->getForClient($clientId, $financialYear)
            : $this->tdsRepository->getForFinancialYear($userId, $financialYear);

        if ($records->isEmpty()) {
            return [];
        }

        $groupedByClient = $records->groupBy('client_id');

        return $groupedByClient->map(function ($clientRecords) use ($financialYear) {
            $client = $clientRecords->first()->client;
            
            return [
                'client' => $client,
                'financial_year' => $financialYear,
                'total_invoice_amount' => $clientRecords->sum('invoice_amount'),
                'total_tds_amount' => $clientRecords->sum('tds_amount'),
                'total_net_received' => $clientRecords->sum('net_received'),
                'records_count' => $clientRecords->count(),
                'records' => $clientRecords->map(function ($record) {
                    return [
                        'invoice_number' => $record->invoice->invoice_number,
                        'invoice_date' => $record->invoice->invoice_date,
                        'invoice_amount' => $record->invoice_amount,
                        'tds_percentage' => $record->tds_percentage,
                        'tds_amount' => $record->tds_amount,
                        'net_received' => $record->net_received,
                        'deduction_date' => $record->deduction_date,
                    ];
                })->toArray(),
            ];
        })->values()->toArray();
    }

    /**
     * Get TDS compliance report
     */
    public function getComplianceReport(int $userId, string $financialYear): array
    {
        $summary = $this->getTdsSummary($userId, $financialYear);
        $quarterlySummary = $this->getQuarterlySummary($userId, $financialYear);

        return [
            'financial_year' => $financialYear,
            'summary' => $summary,
            'quarterly_breakdown' => $quarterlySummary,
            'compliance_status' => $this->checkComplianceStatus($summary),
            'recommendations' => $this->getComplianceRecommendations($summary),
        ];
    }

    /**
     * Calculate TDS liability for a period
     */
    public function calculateTdsLiability(int $userId, string $startDate, string $endDate): array
    {
        $records = $this->tdsRepository->getByDateRange($userId, $startDate, $endDate);

        return [
            'period' => ['start' => $startDate, 'end' => $endDate],
            'total_records' => $records->count(),
            'total_invoice_amount' => $records->sum('invoice_amount'),
            'total_tds_deducted' => $records->sum('tds_amount'),
            'total_net_received' => $records->sum('net_received'),
            'client_wise_breakdown' => $this->getClientWiseBreakdown($records),
            'tds_rate_wise_breakdown' => $this->getTdsRateWiseBreakdown($records),
        ];
    }

    /**
     * Get TDS trends analysis
     */
    public function getTdsTrends(int $userId, int $months = 12): array
    {
        $endDate = now();
        $startDate = now()->subMonths($months);

        $records = $this->tdsRepository->getByDateRange(
            $userId, 
            $startDate->format('Y-m-d'), 
            $endDate->format('Y-m-d')
        );

        $monthlyData = $records->groupBy(function ($record) {
            return $record->deduction_date->format('Y-m');
        })->map(function ($monthRecords, $month) {
            return [
                'month' => $month,
                'records_count' => $monthRecords->count(),
                'total_invoice_amount' => $monthRecords->sum('invoice_amount'),
                'total_tds_amount' => $monthRecords->sum('tds_amount'),
                'avg_tds_percentage' => $monthRecords->avg('tds_percentage'),
            ];
        })->values();

        return [
            'period' => ['start' => $startDate->format('Y-m-d'), 'end' => $endDate->format('Y-m-d')],
            'monthly_trends' => $monthlyData->toArray(),
            'growth_analysis' => $this->calculateTdsGrowth($monthlyData),
        ];
    }

    /**
     * Validate TDS percentage for client
     */
    public function validateTdsPercentage(Client $client, float $tdsPercentage): array
    {
        $validRates = [0, 1, 2, 5, 10, 20]; // Common TDS rates
        $isValid = in_array($tdsPercentage, $validRates);

        return [
            'is_valid' => $isValid,
            'provided_rate' => $tdsPercentage,
            'client_default_rate' => $client->default_tds_percentage,
            'suggested_rate' => $client->default_tds_percentage ?? 0,
            'valid_rates' => $validRates,
            'warnings' => $isValid ? [] : ['Unusual TDS rate provided. Please verify.'],
        ];
    }

    /**
     * Get client-wise breakdown
     */
    private function getClientWiseBreakdown(Collection $records): array
    {
        return $records->groupBy('client_id')->map(function ($clientRecords) {
            $client = $clientRecords->first()->client;
            
            return [
                'client_name' => $client->name,
                'company_name' => $client->company_name,
                'total_invoice_amount' => $clientRecords->sum('invoice_amount'),
                'total_tds_amount' => $clientRecords->sum('tds_amount'),
                'records_count' => $clientRecords->count(),
            ];
        })->values()->toArray();
    }

    /**
     * Get TDS rate-wise breakdown
     */
    private function getTdsRateWiseBreakdown(Collection $records): array
    {
        return $records->groupBy('tds_percentage')->map(function ($rateRecords, $rate) {
            return [
                'tds_rate' => $rate,
                'records_count' => $rateRecords->count(),
                'total_invoice_amount' => $rateRecords->sum('invoice_amount'),
                'total_tds_amount' => $rateRecords->sum('tds_amount'),
            ];
        })->values()->toArray();
    }

    /**
     * Check compliance status
     */
    private function checkComplianceStatus(array $summary): array
    {
        $totalTds = $summary['total_tds_amount'];
        $isCompliant = $totalTds > 0; // Basic check

        return [
            'is_compliant' => $isCompliant,
            'total_tds_liability' => $totalTds,
            'status' => $isCompliant ? 'Compliant' : 'Needs Review',
        ];
    }

    /**
     * Get compliance recommendations
     */
    private function getComplianceRecommendations(array $summary): array
    {
        $recommendations = [];

        if ($summary['total_tds_amount'] == 0) {
            $recommendations[] = 'No TDS records found. Ensure TDS is being deducted where applicable.';
        }

        if ($summary['avg_tds_percentage'] > 10) {
            $recommendations[] = 'High average TDS percentage detected. Please verify rates.';
        }

        return $recommendations;
    }

    /**
     * Calculate TDS growth
     */
    private function calculateTdsGrowth(Collection $monthlyData): array
    {
        if ($monthlyData->count() < 2) {
            return ['growth_rate' => 0, 'trend' => 'insufficient_data'];
        }

        $latest = $monthlyData->last();
        $previous = $monthlyData->slice(-2, 1)->first();

        $growthRate = $this->calculateGrowthPercentage(
            $latest['total_tds_amount'], 
            $previous['total_tds_amount']
        );

        return [
            'growth_rate' => $growthRate,
            'trend' => $growthRate > 0 ? 'increasing' : ($growthRate < 0 ? 'decreasing' : 'stable'),
            'latest_month' => $latest,
            'previous_month' => $previous,
        ];
    }

    /**
     * Auto-calculate TDS for invoice with enhanced automation.
     */
    public function autoCalculateTdsForInvoice(Invoice $invoice): array
    {
        $client = $invoice->client;
        $user = $invoice->user;

        // Get TDS calculation using automation rules
        $calculation = $this->calculateTdsWithAutomation($invoice);

        if (!$calculation['applicable']) {
            return [
                'calculated' => false,
                'reason' => $calculation['reason'] ?? 'No TDS applicable'
            ];
        }

        $tdsPercentage = $calculation['tds_rate'];
        $tdsAmount = $this->calculateTdsAmount($invoice->subtotal, $tdsPercentage);
        $netAmount = $invoice->subtotal - $tdsAmount;

        // Update invoice with TDS calculations
        $invoice->update([
            'tds_percentage' => $tdsPercentage,
            'tds_amount' => $tdsAmount,
            'net_amount' => $netAmount
        ]);

        // Create enhanced TDS record
        $tdsRecord = $this->createTdsRecord([
            'client_id' => $client->id,
            'invoice_id' => $invoice->id,
            'invoice_amount' => $invoice->subtotal,
            'tds_percentage' => $tdsPercentage,
            'tds_amount' => $tdsAmount,
            'net_received' => $netAmount,
            'deduction_date' => now(),
            'description' => "Auto-calculated TDS for Invoice #{$invoice->invoice_number}",
            'is_auto_calculated' => true,
            'calculation_method' => $calculation['method'],
            'automation_metadata' => $calculation['metadata'],
            'suggested_tds_rate' => $calculation['suggested_rate'] ?? $tdsPercentage,
            'rate_justification' => $calculation['justification'] ?? '',
            'compliance_status' => 'pending',
            'certificate_status' => 'pending',
        ]);

        // Add AI insights if available
        if ($calculation['ai_insights']) {
            $tdsRecord->addAIInsights($calculation['ai_insights'], $calculation['confidence_score']);
        }

        // Run compliance checks
        $this->runComplianceChecks($tdsRecord);

        return [
            'calculated' => true,
            'tds_percentage' => $tdsPercentage,
            'tds_amount' => $tdsAmount,
            'net_amount' => $netAmount,
            'method' => $calculation['method'],
            'confidence_score' => $calculation['confidence_score'] ?? null,
            'compliance_status' => $tdsRecord->compliance_status,
        ];
    }

    /**
     * Generate quarterly TDS report.
     */
    public function generateQuarterlyReport(int $userId, string $quarter, string $year): array
    {
        $quarterDates = $this->getQuarterDateRange($quarter, $year);

        $records = $this->tdsRepository->getByDateRange(
            $userId,
            $quarterDates['start'],
            $quarterDates['end']
        );

        $summary = [
            'quarter' => $quarter,
            'year' => $year,
            'period' => $quarterDates,
            'total_records' => $records->count(),
            'total_invoice_amount' => $records->sum('invoice_amount'),
            'total_tds_deducted' => $records->sum('tds_amount'),
            'total_net_received' => $records->sum('net_received'),
            'client_wise_breakdown' => $this->getClientWiseBreakdown($records),
            'tds_rate_wise_breakdown' => $this->getTdsRateWiseBreakdown($records),
            'compliance_status' => $this->checkQuarterlyCompliance($records),
            'generated_at' => now()
        ];

        // Store report for future reference
        $this->storeTdsReport($userId, $summary);

        return $summary;
    }

    /**
     * Get quarter date range.
     */
    protected function getQuarterDateRange(string $quarter, string $year): array
    {
        $quarterMap = [
            'Q1' => ['start' => '04-01', 'end' => '06-30'],
            'Q2' => ['start' => '07-01', 'end' => '09-30'],
            'Q3' => ['start' => '10-01', 'end' => '12-31'],
            'Q4' => ['start' => '01-01', 'end' => '03-31']
        ];

        $dates = $quarterMap[$quarter];

        // Adjust year for Q4 (Jan-Mar of next year)
        $startYear = $quarter === 'Q4' ? $year + 1 : $year;
        $endYear = $quarter === 'Q4' ? $year + 1 : $year;

        return [
            'start' => $startYear . '-' . $dates['start'],
            'end' => $endYear . '-' . $dates['end']
        ];
    }

    /**
     * Check quarterly compliance.
     */
    protected function checkQuarterlyCompliance($records): array
    {
        $totalTds = $records->sum('tds_amount');
        $requiredFilingThreshold = 40000; // ₹40,000 threshold for TDS filing

        $complianceIssues = [];

        if ($totalTds >= $requiredFilingThreshold) {
            $complianceIssues[] = 'TDS filing required - amount exceeds ₹40,000';
        }

        // Check for missing TDS certificates
        $missingCertificates = $records->filter(function ($record) {
            return empty($record->certificate_number);
        });

        if ($missingCertificates->count() > 0) {
            $complianceIssues[] = "{$missingCertificates->count()} TDS certificates missing";
        }

        return [
            'total_tds' => $totalTds,
            'filing_required' => $totalTds >= $requiredFilingThreshold,
            'compliance_issues' => $complianceIssues,
            'status' => empty($complianceIssues) ? 'compliant' : 'issues_found'
        ];
    }

    /**
     * Store TDS report.
     */
    protected function storeTdsReport(int $userId, array $reportData): void
    {
        // This could be stored in a dedicated reports table
        Log::info('TDS quarterly report generated', [
            'user_id' => $userId,
            'quarter' => $reportData['quarter'],
            'year' => $reportData['year'],
            'total_tds' => $reportData['total_tds_deducted']
        ]);
    }

    /**
     * Send compliance alerts.
     */
    public function sendComplianceAlerts(): array
    {
        $results = [
            'alerts_sent' => 0,
            'failed' => 0,
            'errors' => []
        ];

        $users = User::whereHas('tdsRecords')->get();

        foreach ($users as $user) {
            try {
                $currentQuarter = $this->getCurrentQuarter();
                $report = $this->generateQuarterlyReport($user->id, $currentQuarter['quarter'], $currentQuarter['year']);

                if (!empty($report['compliance_status']['compliance_issues'])) {
                    $this->sendComplianceAlert($user, $report);
                    $results['alerts_sent']++;
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Get current quarter.
     */
    protected function getCurrentQuarter(): array
    {
        $month = now()->month;
        $year = now()->year;

        if ($month >= 4 && $month <= 6) {
            return ['quarter' => 'Q1', 'year' => $year];
        } elseif ($month >= 7 && $month <= 9) {
            return ['quarter' => 'Q2', 'year' => $year];
        } elseif ($month >= 10 && $month <= 12) {
            return ['quarter' => 'Q3', 'year' => $year];
        } else {
            return ['quarter' => 'Q4', 'year' => $year - 1];
        }
    }

    /**
     * Send compliance alert to user.
     */
    protected function sendComplianceAlert(User $user, array $report): void
    {
        $notificationService = app(NotificationService::class);

        $alertData = [
            'quarter' => $report['quarter'],
            'year' => $report['year'],
            'total_tds' => $report['total_tds_deducted'],
            'issues' => $report['compliance_status']['compliance_issues']
        ];

        $notificationService->sendAutomationAlert($user, 'tds_compliance_alert', $alertData);
    }

    /**
     * Calculate TDS with automation rules and AI.
     */
    public function calculateTdsWithAutomation(Invoice $invoice): array
    {
        $client = $invoice->client;
        $user = $invoice->user;

        // Prepare context for rule evaluation
        $context = [
            'amount' => $invoice->subtotal,
            'client' => [
                'type' => $client->client_type ?? 'individual',
                'name' => $client->name,
                'pan' => $client->pan_number,
                'state' => $client->state,
            ],
            'service_type' => $invoice->service_type ?? 'professional_services',
            'invoice' => [
                'subtotal' => $invoice->subtotal,
                'total_amount' => $invoice->total_amount,
            ],
            'user' => [
                'state' => $user->state,
            ],
            'financial_year' => TdsRecord::getCurrentFinancialYear(),
        ];

        // Try automation rules first
        $automationRule = TdsAutomationRule::getBestMatch($user->id, $context);

        if ($automationRule) {
            return [
                'applicable' => true,
                'tds_rate' => $automationRule->tds_rate,
                'method' => 'automation_rule',
                'metadata' => [
                    'rule_id' => $automationRule->id,
                    'rule_name' => $automationRule->rule_name,
                    'auto_apply' => $automationRule->auto_apply,
                ],
                'justification' => "Applied automation rule: {$automationRule->rule_name}",
                'ai_insights' => null,
                'confidence_score' => 95,
            ];
        }

        // Try rate configuration
        $rateConfig = TdsRateConfiguration::getApplicableRate(
            $context['service_type'],
            $context['client']['type'],
            $context['amount']
        );

        if ($rateConfig && $rateConfig->conditionsMet($context)) {
            return [
                'applicable' => true,
                'tds_rate' => $rateConfig->tds_rate,
                'method' => 'rate_configuration',
                'metadata' => [
                    'config_id' => $rateConfig->id,
                    'section_code' => $rateConfig->section_code,
                    'service_type' => $rateConfig->service_type,
                ],
                'justification' => "Applied rate configuration for {$rateConfig->service_type} - {$rateConfig->section_code}",
                'ai_insights' => null,
                'confidence_score' => 90,
            ];
        }

        // Try AI-powered calculation if available
        if (PlanChecker::hasFeature($user, 'ai_features')) {
            $aiCalculation = $this->calculateTdsWithAI($context);
            if ($aiCalculation['applicable']) {
                return $aiCalculation;
            }
        }

        // Fallback to client default
        if ($client->default_tds_percentage > 0) {
            return [
                'applicable' => true,
                'tds_rate' => $client->default_tds_percentage,
                'method' => 'client_default',
                'metadata' => [
                    'client_id' => $client->id,
                    'default_rate' => $client->default_tds_percentage,
                ],
                'justification' => 'Applied client default TDS rate',
                'ai_insights' => null,
                'confidence_score' => 70,
            ];
        }

        return [
            'applicable' => false,
            'reason' => 'No applicable TDS rate found',
        ];
    }

    /**
     * Calculate TDS using AI.
     */
    protected function calculateTdsWithAI(array $context): array
    {
        try {
            $prompt = $this->buildAIPromptForTdsCalculation($context);
            $response = $this->aiService->generateText($prompt);

            $aiResult = $this->parseAITdsResponse($response);

            if ($aiResult['applicable']) {
                return [
                    'applicable' => true,
                    'tds_rate' => $aiResult['tds_rate'],
                    'suggested_rate' => $aiResult['suggested_rate'] ?? $aiResult['tds_rate'],
                    'method' => 'ai_calculation',
                    'metadata' => [
                        'ai_model' => $this->aiService->getModel(),
                        'analysis' => $aiResult['analysis'],
                    ],
                    'justification' => $aiResult['justification'],
                    'ai_insights' => $aiResult['insights'],
                    'confidence_score' => $aiResult['confidence_score'],
                ];
            }
        } catch (\Exception $e) {
            Log::warning('AI TDS calculation failed', [
                'error' => $e->getMessage(),
                'context' => $context,
            ]);
        }

        return ['applicable' => false, 'reason' => 'AI calculation not available'];
    }

    /**
     * Build AI prompt for TDS calculation.
     */
    protected function buildAIPromptForTdsCalculation(array $context): string
    {
        return "Analyze the following invoice details and determine the appropriate TDS rate and section:

Invoice Amount: ₹{$context['amount']}
Client Type: {$context['client']['type']}
Client State: {$context['client']['state']}
Service Type: {$context['service_type']}
Financial Year: {$context['financial_year']}

Please provide:
1. Applicable TDS rate (%)
2. TDS section code
3. Justification for the rate
4. Confidence score (0-100)
5. Any compliance considerations

Respond in JSON format:
{
    \"applicable\": true/false,
    \"tds_rate\": number,
    \"section_code\": \"string\",
    \"justification\": \"string\",
    \"confidence_score\": number,
    \"analysis\": \"string\",
    \"insights\": {
        \"compliance_notes\": \"string\",
        \"recommendations\": [\"string\"]
    }
}";
    }

    /**
     * Parse AI TDS response.
     */
    protected function parseAITdsResponse(string $response): array
    {
        try {
            $data = json_decode($response, true);

            if (!$data || !isset($data['applicable'])) {
                throw new \Exception('Invalid AI response format');
            }

            return [
                'applicable' => $data['applicable'] ?? false,
                'tds_rate' => $data['tds_rate'] ?? 0,
                'section_code' => $data['section_code'] ?? '',
                'justification' => $data['justification'] ?? '',
                'confidence_score' => $data['confidence_score'] ?? 0,
                'analysis' => $data['analysis'] ?? '',
                'insights' => $data['insights'] ?? [],
            ];
        } catch (\Exception $e) {
            Log::warning('Failed to parse AI TDS response', [
                'response' => $response,
                'error' => $e->getMessage(),
            ]);

            return ['applicable' => false];
        }
    }

    /**
     * Run compliance checks on TDS record.
     */
    public function runComplianceChecks(TdsRecord $tdsRecord): void
    {
        $checks = [];
        $status = 'compliant';

        // Check TDS rate validity
        if (!$tdsRecord->isRateAcceptable()) {
            $checks[] = [
                'type' => 'rate_validation',
                'status' => 'failed',
                'message' => 'TDS rate is not within acceptable range',
                'severity' => 'medium',
            ];
            $status = 'non_compliant';
        }

        // Check threshold compliance
        $thresholdCheck = $this->checkThresholdCompliance($tdsRecord);
        if (!$thresholdCheck['compliant']) {
            $checks[] = $thresholdCheck;
            $status = 'non_compliant';
        }

        // Check section code validity
        $sectionCheck = $this->validateSectionCode($tdsRecord);
        if (!$sectionCheck['valid']) {
            $checks[] = [
                'type' => 'section_validation',
                'status' => 'failed',
                'message' => $sectionCheck['message'],
                'severity' => 'low',
            ];
        }

        $tdsRecord->updateComplianceStatus($status, $checks);
    }

    /**
     * Check threshold compliance.
     */
    protected function checkThresholdCompliance(TdsRecord $tdsRecord): array
    {
        $client = $tdsRecord->client;
        $thresholds = [
            'individual' => 30000,
            'company' => 0, // No threshold for companies
            'government' => 0,
        ];

        $threshold = $thresholds[$client->client_type ?? 'individual'] ?? 30000;

        if ($threshold > 0 && $tdsRecord->invoice_amount < $threshold) {
            return [
                'type' => 'threshold_check',
                'status' => 'failed',
                'message' => "Invoice amount ₹{$tdsRecord->invoice_amount} is below TDS threshold of ₹{$threshold}",
                'severity' => 'high',
                'compliant' => false,
            ];
        }

        return ['compliant' => true];
    }

    /**
     * Validate TDS section code.
     */
    protected function validateSectionCode(TdsRecord $tdsRecord): array
    {
        $validSections = ['194J', '194C', '194I', '194H', '194G', '194A', '194'];

        // Extract section from automation metadata or use default validation
        $sectionCode = $tdsRecord->automation_metadata['section_code'] ?? '194J';

        if (!in_array($sectionCode, $validSections)) {
            return [
                'valid' => false,
                'message' => "Invalid TDS section code: {$sectionCode}",
            ];
        }

        return ['valid' => true];
    }

    /**
     * Check for rate mismatches.
     */
    protected function checkForRateMismatches(int $userId): void
    {
        $recentRecords = TdsRecord::where('user_id', $userId)
            ->where('created_at', '>=', now()->subDays(30))
            ->with('client')
            ->get();

        foreach ($recentRecords as $record) {
            $suggestedRate = $this->getSuggestedRate($record);

            if ($suggestedRate && abs($record->tds_percentage - $suggestedRate) > 1) {
                TdsComplianceAlert::createRateMismatchAlert($userId, [
                    'client_name' => $record->client->name,
                    'applied_rate' => $record->tds_percentage,
                    'suggested_rate' => $suggestedRate,
                    'invoice_amount' => $record->invoice_amount,
                    'record_id' => $record->id,
                ]);
            }
        }
    }

    /**
     * Get suggested TDS rate for a record.
     */
    protected function getSuggestedRate(TdsRecord $record): ?float
    {
        $context = [
            'amount' => $record->invoice_amount,
            'client' => [
                'type' => $record->client->client_type ?? 'individual',
                'state' => $record->client->state,
            ],
            'service_type' => 'professional_services', // Default
        ];

        $rateConfig = TdsRateConfiguration::getApplicableRate(
            $context['service_type'],
            $context['client']['type'],
            $context['amount']
        );

        return $rateConfig ? $rateConfig->tds_rate : null;
    }

    /**
     * Check compliance deadlines.
     */
    protected function checkComplianceDeadlines(int $userId): void
    {
        $currentQuarter = $this->getCurrentQuarter();
        $quarterlyAmount = $this->getQuarterlyTdsAmount($userId, $currentQuarter);

        if ($quarterlyAmount >= 40000) {
            $dueDate = $this->getQuarterlyFilingDueDate($currentQuarter['quarter'], $currentQuarter['year']);
            $daysLeft = now()->diffInDays($dueDate, false);

            if ($daysLeft <= 30 && $daysLeft > 0) {
                TdsComplianceAlert::createComplianceDeadlineAlert(
                    $userId,
                    "Quarterly TDS Filing - {$currentQuarter['quarter']} {$currentQuarter['year']}",
                    $dueDate
                );
            }
        }
    }

    /**
     * Get quarterly filing due date.
     */
    protected function getQuarterlyFilingDueDate(string $quarter, string $year): \Carbon\Carbon
    {
        $dueDates = [
            'Q1' => "{$year}-07-31", // Q1 (Apr-Jun) due by July 31
            'Q2' => "{$year}-10-31", // Q2 (Jul-Sep) due by Oct 31
            'Q3' => ($year + 1) . "-01-31", // Q3 (Oct-Dec) due by Jan 31
            'Q4' => ($year + 1) . "-05-31", // Q4 (Jan-Mar) due by May 31
        ];

        return \Carbon\Carbon::parse($dueDates[$quarter]);
    }

    /**
     * Process automated TDS calculations for all pending invoices.
     */
    public function processAutomatedCalculations(): array
    {
        $results = [
            'processed' => 0,
            'calculated' => 0,
            'failed' => 0,
            'errors' => [],
        ];

        // Get invoices without TDS calculations
        $pendingInvoices = Invoice::whereNull('tds_amount')
            ->where('status', 'sent')
            ->with(['client', 'user'])
            ->get();

        foreach ($pendingInvoices as $invoice) {
            try {
                $results['processed']++;

                $calculation = $this->autoCalculateTdsForInvoice($invoice);

                if ($calculation['calculated']) {
                    $results['calculated']++;
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'invoice_id' => $invoice->id,
                    'error' => $e->getMessage(),
                ];

                Log::error('Automated TDS calculation failed', [
                    'invoice_id' => $invoice->id,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        return $results;
    }

    /**
     * Generate TDS automation insights.
     */
    public function generateAutomationInsights(int $userId): array
    {
        $cacheKey = "tds_automation_insights_{$userId}";

        return Cache::remember($cacheKey, 3600, function () use ($userId) {
            $records = TdsRecord::where('user_id', $userId)
                ->where('created_at', '>=', now()->subMonths(6))
                ->get();

            $autoCalculated = $records->where('is_auto_calculated', true);
            $manualCalculated = $records->where('is_auto_calculated', false);

            return [
                'total_records' => $records->count(),
                'automation_rate' => $records->count() > 0 ? ($autoCalculated->count() / $records->count()) * 100 : 0,
                'average_confidence_score' => $autoCalculated->avg('confidence_score') ?? 0,
                'compliance_rate' => $records->count() > 0 ? ($records->where('compliance_status', 'compliant')->count() / $records->count()) * 100 : 0,
                'method_breakdown' => [
                    'automation_rule' => $autoCalculated->where('calculation_method', 'automation_rule')->count(),
                    'rate_configuration' => $autoCalculated->where('calculation_method', 'rate_configuration')->count(),
                    'ai_calculation' => $autoCalculated->where('calculation_method', 'ai_calculation')->count(),
                    'client_default' => $autoCalculated->where('calculation_method', 'client_default')->count(),
                    'manual' => $manualCalculated->count(),
                ],
                'certificate_status' => [
                    'received' => $records->where('certificate_status', 'received')->count(),
                    'pending' => $records->where('certificate_status', 'pending')->count(),
                    'missing' => $records->where('certificate_status', 'missing')->count(),
                ],
                'recommendations' => $this->generateAutomationRecommendations($records),
            ];
        });
    }

    /**
     * Generate automation recommendations.
     */
    protected function generateAutomationRecommendations(Collection $records): array
    {
        $recommendations = [];

        // Check automation rate
        $autoRate = $records->count() > 0 ? ($records->where('is_auto_calculated', true)->count() / $records->count()) * 100 : 0;

        if ($autoRate < 70) {
            $recommendations[] = [
                'type' => 'automation_improvement',
                'priority' => 'high',
                'message' => 'Consider setting up more automation rules to increase automation rate',
                'action' => 'Create automation rules for common client types and service categories',
            ];
        }

        // Check compliance rate
        $complianceRate = $records->count() > 0 ? ($records->where('compliance_status', 'compliant')->count() / $records->count()) * 100 : 0;

        if ($complianceRate < 90) {
            $recommendations[] = [
                'type' => 'compliance_improvement',
                'priority' => 'high',
                'message' => 'Review non-compliant TDS records and update automation rules',
                'action' => 'Analyze failed compliance checks and adjust rate configurations',
            ];
        }

        // Check certificate collection
        $missingCertificates = $records->where('certificate_status', 'missing')->count();

        if ($missingCertificates > 0) {
            $recommendations[] = [
                'type' => 'certificate_collection',
                'priority' => 'medium',
                'message' => "Follow up on {$missingCertificates} missing TDS certificates",
                'action' => 'Set up automated reminders for certificate collection',
            ];
        }

        return $recommendations;
    }

    /**
     * Dispatch queue-based TDS calculation processing.
     */
    public function dispatchQueueProcessing(string $calculationType = 'automation', ?int $invoiceId = null, ?int $userId = null, array $options = []): void
    {
        \App\Jobs\ProcessTdsCalculationJob::dispatch($invoiceId, $userId, $calculationType, $options)
            ->onQueue($options['queue'] ?? 'default');
    }

    /**
     * Dispatch queue-based compliance alerts processing.
     */
    public function dispatchComplianceAlertsProcessing(?int $userId = null, array $options = []): void
    {
        \App\Jobs\ProcessTdsComplianceAlertsJob::dispatch($userId, $options)
            ->onQueue($options['queue'] ?? 'high');
    }
}
