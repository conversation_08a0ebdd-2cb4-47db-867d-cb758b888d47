<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Models\TdsRecord;
use App\Services\TdsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessTdsCalculationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 120; // 2 minutes
    public $tries = 3;
    public $maxExceptions = 2;
    public $backoff = [10, 30, 60];

    protected ?int $invoiceId;
    protected ?int $userId;
    protected string $calculationType;
    protected array $options;

    /**
     * Create a new job instance.
     */
    public function __construct(?int $invoiceId = null, ?int $userId = null, string $calculationType = 'invoice', array $options = [])
    {
        $this->invoiceId = $invoiceId;
        $this->userId = $userId;
        $this->calculationType = $calculationType;
        $this->options = $options;
        
        // Set queue based on calculation type
        $queue = match($calculationType) {
            'quarterly' => 'low',
            'compliance' => 'high',
            'bulk' => 'bulk',
            default => 'default'
        };
        
        $this->onQueue($queue);
    }

    /**
     * Execute the job.
     */
    public function handle(TdsService $service): void
    {
        try {
            match($this->calculationType) {
                'invoice' => $this->processInvoiceTdsCalculation($service),
                'quarterly' => $this->processQuarterlyCalculations($service),
                'compliance' => $this->processComplianceChecks($service),
                'bulk' => $this->processBulkCalculations($service),
                'automation' => $this->processAutomatedCalculations($service),
                default => throw new \InvalidArgumentException("Unknown calculation type: {$this->calculationType}")
            };
        } catch (Exception $e) {
            Log::error('TDS calculation job failed', [
                'invoice_id' => $this->invoiceId,
                'user_id' => $this->userId,
                'calculation_type' => $this->calculationType,
                'attempt' => $this->attempts(),
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Process TDS calculation for specific invoice.
     */
    protected function processInvoiceTdsCalculation(TdsService $service): void
    {
        if (!$this->invoiceId) {
            throw new \InvalidArgumentException('Invoice ID is required for invoice TDS calculation');
        }

        $invoice = Invoice::find($this->invoiceId);
        
        if (!$invoice) {
            Log::warning('Invoice not found for TDS calculation', [
                'invoice_id' => $this->invoiceId
            ]);
            return;
        }

        $result = $service->calculateTdsForInvoice($invoice);
        
        if ($result) {
            Log::info('TDS calculation completed for invoice', [
                'invoice_id' => $this->invoiceId,
                'tds_amount' => $result->tds_amount,
                'tds_rate' => $result->tds_rate,
                'calculation_method' => $result->calculation_method
            ]);

            // Dispatch compliance check if needed
            if ($result->tds_amount > 0) {
                $this->dispatchComplianceCheck($invoice, $result);
            }
        }
    }

    /**
     * Process quarterly TDS calculations.
     */
    protected function processQuarterlyCalculations(TdsService $service): void
    {
        $results = $service->processQuarterlyCalculations($this->userId);
        
        Log::info('Quarterly TDS calculations completed', [
            'user_id' => $this->userId,
            'processed' => $results['processed'],
            'total_tds' => $results['total_tds'],
            'compliance_issues' => $results['compliance_issues'] ?? 0
        ]);

        // Dispatch compliance alerts if issues found
        if (($results['compliance_issues'] ?? 0) > 0) {
            ProcessTdsComplianceAlertsJob::dispatch($this->userId, [
                'type' => 'quarterly_issues',
                'issues' => $results['compliance_issues']
            ])->onQueue('high');
        }
    }

    /**
     * Process compliance checks.
     */
    protected function processComplianceChecks(TdsService $service): void
    {
        $results = $service->processComplianceAlerts($this->userId);
        
        Log::info('TDS compliance checks completed', [
            'user_id' => $this->userId,
            'alerts_sent' => $results['alerts_sent'],
            'deadlines_approaching' => $results['deadlines_approaching'],
            'overdue_filings' => $results['overdue_filings']
        ]);

        // Dispatch urgent notifications for overdue filings
        if (($results['overdue_filings'] ?? 0) > 0) {
            ProcessAdminNotificationJob::dispatch([
                'type' => 'tds_overdue_filings',
                'user_id' => $this->userId,
                'overdue_count' => $results['overdue_filings']
            ])->onQueue('notifications');
        }
    }

    /**
     * Process bulk TDS calculations.
     */
    protected function processBulkCalculations(TdsService $service): void
    {
        $invoiceIds = $this->options['invoice_ids'] ?? [];
        
        if (empty($invoiceIds)) {
            Log::warning('No invoice IDs provided for bulk TDS calculation');
            return;
        }

        $results = [
            'processed' => 0,
            'failed' => 0,
            'total_tds' => 0,
            'errors' => []
        ];

        foreach ($invoiceIds as $invoiceId) {
            try {
                $invoice = Invoice::find($invoiceId);
                if (!$invoice) {
                    continue;
                }

                $tdsRecord = $service->calculateTdsForInvoice($invoice);
                if ($tdsRecord) {
                    $results['processed']++;
                    $results['total_tds'] += $tdsRecord->tds_amount;
                }
            } catch (Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'invoice_id' => $invoiceId,
                    'error' => $e->getMessage()
                ];
            }
        }

        Log::info('Bulk TDS calculations completed', $results);
    }

    /**
     * Process automated TDS calculations.
     */
    protected function processAutomatedCalculations(TdsService $service): void
    {
        $results = $service->processAutomatedCalculations($this->userId);
        
        Log::info('Automated TDS calculations completed', [
            'user_id' => $this->userId,
            'processed' => $results['processed'],
            'automated' => $results['automated'],
            'manual_review_required' => $results['manual_review_required']
        ]);

        // Dispatch notifications for manual review items
        if (($results['manual_review_required'] ?? 0) > 0) {
            ProcessUserNotificationJob::dispatch($this->userId, [
                'type' => 'tds_manual_review',
                'count' => $results['manual_review_required'],
                'message' => "You have {$results['manual_review_required']} TDS calculations that require manual review."
            ])->onQueue('notifications');
        }
    }

    /**
     * Dispatch compliance check for TDS record.
     */
    protected function dispatchComplianceCheck(Invoice $invoice, TdsRecord $tdsRecord): void
    {
        ProcessTdsComplianceAlertsJob::dispatch($invoice->user_id, [
            'type' => 'new_tds_record',
            'invoice_id' => $invoice->id,
            'tds_record_id' => $tdsRecord->id,
            'tds_amount' => $tdsRecord->tds_amount
        ])->onQueue('default')->delay(now()->addMinutes(5));
    }

    /**
     * Handle job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('TDS calculation job permanently failed', [
            'invoice_id' => $this->invoiceId,
            'user_id' => $this->userId,
            'calculation_type' => $this->calculationType,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage()
        ]);

        // Dispatch failure notification based on calculation type
        $notificationType = match($this->calculationType) {
            'quarterly' => 'quarterly_tds_failure',
            'compliance' => 'compliance_check_failure',
            'bulk' => 'bulk_tds_failure',
            default => 'tds_calculation_failure'
        };

        ProcessAdminNotificationJob::dispatch([
            'type' => $notificationType,
            'invoice_id' => $this->invoiceId,
            'user_id' => $this->userId,
            'calculation_type' => $this->calculationType,
            'error' => $exception->getMessage()
        ])->onQueue('notifications');
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        $tags = ['tds-calculation', "type:{$this->calculationType}"];
        
        if ($this->invoiceId) {
            $tags[] = "invoice:{$this->invoiceId}";
        }
        
        if ($this->userId) {
            $tags[] = "user:{$this->userId}";
        }
        
        return $tags;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return $this->backoff;
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHours(1);
    }
}
