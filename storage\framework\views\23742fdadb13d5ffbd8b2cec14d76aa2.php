<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="text-center">
            <h1 class="text-2xl font-bold text-gray-900">Choose Your Plan</h1>
            <p class="mt-2 text-gray-600">Select the perfect plan for your freelance business</p>
        </div>
     <?php $__env->endSlot(); ?>
<div class="min-h-screen bg-gray-50 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Plans Grid -->
        <div class="mt-12 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-6 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:mx-0 xl:grid-cols-3">
            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="border border-gray-200 rounded-lg shadow-sm divide-y divide-gray-200 <?php echo e($plan->is_popular ? 'ring-2 ring-indigo-500' : ''); ?>">
                <?php if($plan->is_popular): ?>
                <div class="bg-indigo-500 text-white text-center py-2 rounded-t-lg">
                    <span class="text-sm font-medium">Most Popular</span>
                </div>
                <?php endif; ?>
                
                <div class="p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900"><?php echo e($plan->name); ?></h3>
                    <p class="mt-4 text-sm text-gray-500"><?php echo e($plan->description); ?></p>
                    <p class="mt-8">
                        <span class="text-4xl font-bold text-gray-900">$<?php echo e($plan->price); ?></span>
                        <span class="text-base font-medium text-gray-500">/<?php echo e($plan->billing_cycle); ?></span>
                    </p>
                    
                    <?php if(auth()->user()->current_plan_id == $plan->id): ?>
                    <button class="mt-8 block w-full bg-gray-300 border border-gray-300 rounded-md py-2 text-sm font-semibold text-gray-500 text-center cursor-not-allowed">
                        Current Plan
                    </button>
                    <?php else: ?>
                    <form action="<?php echo e(route('subscriptions.subscribe', $plan)); ?>" method="POST" class="mt-8">
                        <?php echo csrf_field(); ?>
                        <button type="submit" class="block w-full bg-indigo-600 border border-transparent rounded-md py-2 text-sm font-semibold text-white text-center hover:bg-indigo-700">
                            Choose <?php echo e($plan->name); ?>

                        </button>
                    </form>
                    <?php endif; ?>
                </div>
                
                <div class="pt-6 pb-8 px-6">
                    <h4 class="text-sm font-medium text-gray-900 tracking-wide uppercase">What's included</h4>
                    <ul class="mt-6 space-y-4">
                        <?php $__currentLoopData = $plan->planFeatures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li class="flex space-x-3">
                            <i class="fas fa-check text-green-500 text-sm mt-0.5"></i>
                            <span class="text-sm text-gray-500"><?php echo e($feature->feature_name); ?></span>
                        </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <!-- FAQ Section -->
        <div class="mt-16">
            <h3 class="text-lg font-medium text-gray-900 text-center mb-8">Frequently Asked Questions</h3>
            <div class="max-w-3xl mx-auto">
                <div class="space-y-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Can I change my plan anytime?</h4>
                        <p class="mt-2 text-sm text-gray-500">Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">What payment methods do you accept?</h4>
                        <p class="mt-2 text-sm text-gray-500">We accept all major credit cards and PayPal for your convenience.</p>
                    </div>
                    <div>
                        <h4 class="text-sm font-medium text-gray-900">Is there a free trial?</h4>
                        <p class="mt-2 text-sm text-gray-500">Yes, all new users get a 14-day free trial to explore all features.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\freeligo\resources\views/subscriptions/plans.blade.php ENDPATH**/ ?>