<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TdsComplianceAlert extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'alert_type',
        'severity',
        'title',
        'description',
        'metadata',
        'status',
        'due_date',
        'resolved_at',
        'resolution_notes',
    ];

    protected function casts(): array
    {
        return [
            'metadata' => 'array',
            'due_date' => 'datetime',
            'resolved_at' => 'datetime',
        ];
    }

    /**
     * Get the user that owns the alert.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get active alerts.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get resolved alerts.
     */
    public function scopeResolved($query)
    {
        return $query->where('status', 'resolved');
    }

    /**
     * Scope to get alerts by severity.
     */
    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Scope to get critical alerts.
     */
    public function scopeCritical($query)
    {
        return $query->where('severity', 'critical');
    }

    /**
     * Scope to get overdue alerts.
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
                    ->where('status', 'active');
    }

    /**
     * Scope to get alerts for a specific user.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Mark alert as resolved.
     */
    public function resolve(string $notes = null): void
    {
        $this->update([
            'status' => 'resolved',
            'resolved_at' => now(),
            'resolution_notes' => $notes,
        ]);
    }

    /**
     * Mark alert as dismissed.
     */
    public function dismiss(string $notes = null): void
    {
        $this->update([
            'status' => 'dismissed',
            'resolved_at' => now(),
            'resolution_notes' => $notes,
        ]);
    }

    /**
     * Check if alert is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->due_date && $this->due_date->isPast() && $this->status === 'active';
    }

    /**
     * Get severity color for UI.
     */
    public function getSeverityColor(): string
    {
        return match ($this->severity) {
            'critical' => 'red',
            'high' => 'orange',
            'medium' => 'yellow',
            'low' => 'blue',
            default => 'gray',
        };
    }

    /**
     * Get severity icon for UI.
     */
    public function getSeverityIcon(): string
    {
        return match ($this->severity) {
            'critical' => 'exclamation-triangle',
            'high' => 'exclamation-circle',
            'medium' => 'info-circle',
            'low' => 'info',
            default => 'bell',
        };
    }

    /**
     * Create a quarterly filing alert.
     */
    public static function createQuarterlyFilingAlert(int $userId, string $quarter, string $year, float $tdsAmount): self
    {
        return self::create([
            'user_id' => $userId,
            'alert_type' => 'quarterly_filing',
            'severity' => $tdsAmount >= 40000 ? 'critical' : 'high',
            'title' => "TDS Quarterly Filing Required - Q{$quarter} {$year}",
            'description' => "TDS amount of ₹" . number_format($tdsAmount, 2) . " requires quarterly filing for Q{$quarter} {$year}.",
            'metadata' => [
                'quarter' => $quarter,
                'year' => $year,
                'tds_amount' => $tdsAmount,
                'filing_threshold' => 40000,
            ],
            'due_date' => self::getQuarterlyFilingDueDate($quarter, $year),
        ]);
    }

    /**
     * Create a missing certificate alert.
     */
    public static function createMissingCertificateAlert(int $userId, array $missingRecords): self
    {
        $count = count($missingRecords);
        $totalAmount = array_sum(array_column($missingRecords, 'tds_amount'));

        return self::create([
            'user_id' => $userId,
            'alert_type' => 'certificate_missing',
            'severity' => $count > 5 ? 'high' : 'medium',
            'title' => "Missing TDS Certificates ({$count} records)",
            'description' => "You have {$count} TDS records with missing certificates totaling ₹" . number_format($totalAmount, 2) . ".",
            'metadata' => [
                'missing_count' => $count,
                'total_amount' => $totalAmount,
                'record_ids' => array_column($missingRecords, 'id'),
            ],
            'due_date' => now()->addDays(30),
        ]);
    }

    /**
     * Create a rate mismatch alert.
     */
    public static function createRateMismatchAlert(int $userId, array $mismatchData): self
    {
        return self::create([
            'user_id' => $userId,
            'alert_type' => 'rate_mismatch',
            'severity' => 'medium',
            'title' => 'TDS Rate Mismatch Detected',
            'description' => "Potential TDS rate mismatch detected for {$mismatchData['client_name']}. Applied: {$mismatchData['applied_rate']}%, Suggested: {$mismatchData['suggested_rate']}%",
            'metadata' => $mismatchData,
            'due_date' => now()->addDays(7),
        ]);
    }

    /**
     * Create a compliance deadline alert.
     */
    public static function createComplianceDeadlineAlert(int $userId, string $deadlineType, \Carbon\Carbon $dueDate): self
    {
        $daysLeft = now()->diffInDays($dueDate, false);
        
        return self::create([
            'user_id' => $userId,
            'alert_type' => 'compliance_deadline',
            'severity' => $daysLeft <= 7 ? 'critical' : ($daysLeft <= 15 ? 'high' : 'medium'),
            'title' => "TDS Compliance Deadline Approaching",
            'description' => "{$deadlineType} deadline is in {$daysLeft} days ({$dueDate->format('d M Y')}).",
            'metadata' => [
                'deadline_type' => $deadlineType,
                'days_left' => $daysLeft,
            ],
            'due_date' => $dueDate,
        ]);
    }

    /**
     * Get quarterly filing due date.
     */
    protected static function getQuarterlyFilingDueDate(string $quarter, string $year): \Carbon\Carbon
    {
        $dueDates = [
            '1' => "{$year}-07-31", // Q1 (Apr-Jun) due by July 31
            '2' => "{$year}-10-31", // Q2 (Jul-Sep) due by Oct 31
            '3' => ($year + 1) . "-01-31", // Q3 (Oct-Dec) due by Jan 31
            '4' => ($year + 1) . "-05-31", // Q4 (Jan-Mar) due by May 31
        ];

        return \Carbon\Carbon::parse($dueDates[$quarter]);
    }

    /**
     * Get alert types.
     */
    public static function getAlertTypes(): array
    {
        return [
            'quarterly_filing' => 'Quarterly Filing Required',
            'certificate_missing' => 'Missing TDS Certificates',
            'rate_mismatch' => 'TDS Rate Mismatch',
            'compliance_deadline' => 'Compliance Deadline',
            'threshold_exceeded' => 'TDS Threshold Exceeded',
            'annual_filing' => 'Annual Filing Required',
            'penalty_risk' => 'Penalty Risk',
        ];
    }

    /**
     * Get severity levels.
     */
    public static function getSeverityLevels(): array
    {
        return [
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'critical' => 'Critical',
        ];
    }
}
