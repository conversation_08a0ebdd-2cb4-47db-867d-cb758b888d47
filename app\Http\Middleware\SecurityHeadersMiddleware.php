<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class SecurityHeadersMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Skip security headers for static assets
        if ($this->isStaticAsset($request)) {
            return $response;
        }

        // Force HTTPS in production
        if (config('security.force_https') && !$request->secure() && app()->environment('production')) {
            return redirect()->secure($request->getRequestUri(), 301);
        }

        // Add security headers
        $headers = config('security.headers', []);
        foreach ($headers as $header => $value) {
            $response->headers->set($header, $value);
        }

        // Add Content Security Policy
        if (config('security.csp.enabled')) {
            $cspDirectives = config('security.csp.directives', []);
            $cspString = '';
            
            foreach ($cspDirectives as $directive => $value) {
                $cspString .= $directive . ' ' . $value . '; ';
            }
            
            $headerName = config('security.csp.report_only') ? 'Content-Security-Policy-Report-Only' : 'Content-Security-Policy';
            $response->headers->set($headerName, trim($cspString));
        }

        // Set secure cookie settings
        if (config('security.secure_cookies') && app()->environment('production')) {
            $response->headers->setCookie(
                $response->headers->getCookies()[0] ?? null,
                null,
                null,
                null,
                true, // secure
                true, // httpOnly
                false,
                config('security.same_site_cookies', 'strict')
            );
        }

        return $response;
    }

    /**
     * Check if the request is for a static asset.
     */
    private function isStaticAsset(Request $request): bool
    {
        $path = $request->getPathInfo();

        // Common static asset extensions
        $staticExtensions = [
            '.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.svg', '.ico',
            '.woff', '.woff2', '.ttf', '.eot', '.map', '.webp', '.pdf'
        ];

        foreach ($staticExtensions as $extension) {
            if (str_ends_with($path, $extension)) {
                return true;
            }
        }

        // Check for common static asset paths
        $staticPaths = ['/css/', '/js/', '/images/', '/fonts/', '/assets/', '/build/'];

        foreach ($staticPaths as $staticPath) {
            if (str_contains($path, $staticPath)) {
                return true;
            }
        }

        return false;
    }
}
