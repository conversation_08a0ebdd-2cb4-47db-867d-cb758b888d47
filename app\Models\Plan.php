<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Plan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'price',
        'currency',
        'billing_cycle',
        'sort_order',
        'is_popular',
        'is_active',
        'features',
    ];

    /**
     * Get the default currency from configuration
     */
    public static function getDefaultCurrency(): string
    {
        return config('services.currency.code', 'USD');
    }

    /**
     * Get the currency symbol from configuration
     */
    public static function getCurrencySymbol(): string
    {
        return config('services.currency.symbol', '$');
    }

    /**
     * Get formatted price with currency symbol
     */
    public function getFormattedPriceAttribute(): string
    {
        if ($this->price == 0) {
            return self::getCurrencySymbol() . '0';
        }

        return self::getCurrencySymbol() . number_format($this->price, 0);
    }

    protected function casts(): array
    {
        return [
            'price' => 'float',
            'is_popular' => 'boolean',
            'is_active' => 'boolean',
            'features' => 'array',
        ];
    }

    /**
     * Get the plan features.
     */
    public function planFeatures(): HasMany
    {
        return $this->hasMany(PlanFeature::class);
    }

    /**
     * Get the plan subscriptions.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get the user subscriptions for this plan.
     */
    public function userSubscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    /**
     * Get the users currently on this plan.
     */
    public function users(): HasMany
    {
        return $this->hasMany(User::class, 'current_plan_id');
    }

    /**
     * Scope a query to only include active plans.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope a query to order plans by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Check if this is a free plan.
     */
    public function isFree(): bool
    {
        return $this->price == 0;
    }

    /**
     * Get the default free plan.
     */
    public static function getFreePlan(): ?Plan
    {
        return static::where('slug', 'free')->where('is_active', true)->first();
    }

    /**
     * Get a specific feature object for this plan.
     */
    public function getFeature(string $featureKey): ?PlanFeature
    {
        return $this->planFeatures()->where('feature_key', $featureKey)->first();
    }

    /**
     * Get a specific feature value for this plan.
     */
    public function getFeatureValue(string $featureKey): ?string
    {
        $feature = $this->getFeature($featureKey);
        return $feature ? $feature->feature_value : null;
    }

    /**
     * Check if plan has unlimited feature.
     */
    public function hasUnlimitedFeature(string $featureKey): bool
    {
        return $this->getFeatureValue($featureKey) === 'unlimited';
    }

    /**
     * Get numeric limit for a feature.
     */
    public function getFeatureLimit(string $featureKey): ?int
    {
        $value = $this->getFeatureValue($featureKey);
        if ($value === 'unlimited') {
            return null; // null means unlimited
        }
        return is_numeric($value) ? (int) $value : 0;
    }
}
