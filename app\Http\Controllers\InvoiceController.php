<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreInvoiceRequest;
use App\Models\Invoice;
use App\Services\InvoiceService;
use App\Services\ClientService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Barryvdh\DomPDF\Facade\Pdf;

class InvoiceController extends Controller
{
    protected InvoiceService $invoiceService;
    protected ClientService $clientService;

    public function __construct(InvoiceService $invoiceService, ClientService $clientService)
    {
        $this->invoiceService = $invoiceService;
        $this->clientService = $clientService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $invoices = $this->invoiceService->getInvoicesForUser(Auth::id(), $request);
        $clients = $this->clientService->getClientsForUser(Auth::id());

        return view('invoices.index', compact('invoices', 'clients'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        // Check if user can create invoices
        if (!$this->invoiceService->canCreateInvoice(Auth::id())) {
            return redirect()->route('upgrade')
                ->with('error', 'You have reached your monthly invoice limit. Upgrade to Pro for unlimited invoices.');
        }

        $clients = $this->clientService->getClientsForUser(Auth::id());
        $invoiceNumber = Invoice::generateInvoiceNumber();

        return view('invoices.create', compact('clients', 'invoiceNumber'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreInvoiceRequest $request)
    {
        // Check if user can create invoices
        if (!$this->invoiceService->canCreateInvoice(Auth::id())) {
            return redirect()->route('invoices.index')
                ->with('error', 'You have reached your monthly invoice limit. Upgrade to Pro for unlimited invoices.');
        }

        $validated = $request->validated();
        $this->invoiceService->createInvoice($validated);

        return redirect()->route('invoices.index')
                        ->with('success', 'Invoice created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Invoice $invoice)
    {
        $this->authorize('view', $invoice);
        $invoice->load(['client', 'items', 'tdsRecord']);

        return view('invoices.show', compact('invoice'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Invoice $invoice)
    {
        $this->authorize('update', $invoice);

        if ($invoice->status === 'paid') {
            return redirect()->route('invoices.show', $invoice)
                            ->with('error', 'Cannot edit paid invoices.');
        }

        $clients = $this->clientService->getClientsForUser(Auth::id());
        $invoice->load('items');

        return view('invoices.edit', compact('invoice', 'clients'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Invoice $invoice)
    {
        $this->authorize('update', $invoice);

        if ($invoice->status === 'paid') {
            return redirect()->route('invoices.show', $invoice)
                            ->with('error', 'Cannot edit paid invoices.');
        }

        $validated = $request->validate([
            'client_id' => 'required|exists:clients,id',
            'invoice_date' => 'required|date',
            'due_date' => 'required|date|after_or_equal:invoice_date',
            'tax_percentage' => 'nullable|numeric|min:0|max:100',
            'tds_percentage' => 'nullable|numeric|min:0|max:100',
            'notes' => 'nullable|string',
            'items' => 'required|array|min:1',
            'items.*.description' => 'required|string',
            'items.*.quantity' => 'required|numeric|min:1',
            'items.*.rate' => 'required|numeric|min:0',
        ]);

        $this->invoiceService->updateInvoice($invoice, $validated);

        return redirect()->route('invoices.show', $invoice)
                        ->with('success', 'Invoice updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Invoice $invoice)
    {
        $this->authorize('delete', $invoice);

        if ($invoice->status === 'paid') {
            return redirect()->route('invoices.index')
                            ->with('error', 'Cannot delete paid invoices.');
        }

        $invoice->delete();

        return redirect()->route('invoices.index')
                        ->with('success', 'Invoice deleted successfully.');
    }

    /**
     * Download invoice as PDF.
     */
    public function download(Invoice $invoice)
    {
        $this->authorize('view', $invoice);
        $invoice->load(['client', 'items', 'user']);

        try {
            $pdf = Pdf::loadView('invoices.pdf', compact('invoice'));

            return $pdf->download($invoice->invoice_number . '.pdf');
        } catch (\Exception $e) {
            return redirect()->back()
                ->with('error', 'Failed to generate PDF. Please try again or contact support.');
        }
    }

    /**
     * Mark invoice as paid.
     */
    public function markAsPaid(Invoice $invoice)
    {
        $this->authorize('update', $invoice);

        $this->invoiceService->markAsPaid($invoice);

        return redirect()->back()
                        ->with('success', 'Invoice marked as paid.');
    }
}
