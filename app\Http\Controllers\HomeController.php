<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class HomeController extends Controller
{
    /**
     * Display the home page.
     */
    public function index()
    {
        $plans = Plan::with('planFeatures')->active()->orderBy('sort_order')->get();
        return view('frontend.home', compact('plans'));
    }

    /**
     * Display the about page.
     */
    public function about()
    {
        return view('frontend.about');
    }

    /**
     * Display the features page.
     */
    public function features()
    {
        return view('frontend.features');
    }

    /**
     * Display the pricing page.
     */
    public function pricing()
    {
        $plans = Plan::with('planFeatures')->active()->orderBy('sort_order')->get();
        return view('frontend.pricing', compact('plans'));
    }

    /**
     * Display the contact page.
     */
    public function contact()
    {
        return view('frontend.contact');
    }

    /**
     * Handle contact form submission.
     */
    public function submitContact(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => [
                'required',
                'string',
                'min:2',
                'max:255',
                'regex:/^[a-zA-Z\s\-\.\']+$/'
            ],
            'email' => [
                'required',
                'email:rfc,dns',
                'max:255'
            ],
            'subject' => [
                'required',
                'string',
                'min:5',
                'max:255'
            ],
            'message' => [
                'required',
                'string',
                'min:10',
                'max:2000'
            ],
        ], [
            'name.required' => 'Please enter your full name.',
            'name.min' => 'Name must be at least 2 characters long.',
            'name.regex' => 'Name can only contain letters, spaces, hyphens, dots, and apostrophes.',
            'email.required' => 'Please enter your email address.',
            'email.email' => 'Please enter a valid email address.',
            'subject.required' => 'Please enter a subject for your message.',
            'subject.min' => 'Subject must be at least 5 characters long.',
            'message.required' => 'Please enter your message.',
            'message.min' => 'Message must be at least 10 characters long.',
            'message.max' => 'Message cannot exceed 2000 characters.',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // Here you can add email sending logic
        // For now, we'll just redirect with success message

        return back()->with('success', 'Thank you for your message! We\'ll get back to you soon.');
    }

    /**
     * Display the privacy policy page.
     */
    public function privacy()
    {
        return view('frontend.privacy');
    }

    /**
     * Display the terms of service page.
     */
    public function terms()
    {
        return view('frontend.terms');
    }

    /**
     * Display the refund policy page.
     */
    public function refund()
    {
        return view('frontend.refund');
    }
}
