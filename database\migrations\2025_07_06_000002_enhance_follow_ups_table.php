<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('follow_ups', function (Blueprint $table) {
            // Enhanced tracking fields
            $table->json('delivery_channels')->nullable()->after('method');
            $table->decimal('effectiveness_score', 5, 2)->nullable()->after('sent_at');
            $table->json('client_response_data')->nullable()->after('effectiveness_score');
            $table->timestamp('client_viewed_at')->nullable()->after('client_response_data');
            $table->timestamp('client_responded_at')->nullable()->after('client_viewed_at');
            $table->string('response_type')->nullable()->after('client_responded_at');
            $table->json('ai_insights')->nullable()->after('response_type');
            $table->boolean('auto_generated')->default(false)->after('ai_insights');
            $table->string('template_used')->nullable()->after('auto_generated');
            $table->json('personalization_data')->nullable()->after('template_used');
            $table->integer('retry_count')->default(0)->after('personalization_data');
            $table->timestamp('optimal_send_time')->nullable()->after('retry_count');
            
            // Performance indexes
            $table->index('effectiveness_score');
            $table->index('auto_generated');
            $table->index('client_viewed_at');
            $table->index('optimal_send_time');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('follow_ups', function (Blueprint $table) {
            $table->dropIndex(['effectiveness_score']);
            $table->dropIndex(['auto_generated']);
            $table->dropIndex(['client_viewed_at']);
            $table->dropIndex(['optimal_send_time']);
            
            $table->dropColumn([
                'delivery_channels',
                'effectiveness_score',
                'client_response_data',
                'client_viewed_at',
                'client_responded_at',
                'response_type',
                'ai_insights',
                'auto_generated',
                'template_used',
                'personalization_data',
                'retry_count',
                'optimal_send_time'
            ]);
        });
    }
};
