<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

trait HasUserScope
{
    /**
     * Scope query to current user
     */
    public function scopeForCurrentUser(Builder $query): Builder
    {
        return $query->where('user_id', Auth::id());
    }

    /**
     * Scope query to specific user
     */
    public function scopeForUser(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Check if record belongs to current user
     */
    public function belongsToCurrentUser(): bool
    {
        return $this->user_id === Auth::id();
    }

    /**
     * Check if record belongs to specific user
     */
    public function belongsToUser(int $userId): bool
    {
        return $this->user_id === $userId;
    }

    /**
     * Get user-scoped query builder
     */
    public static function forCurrentUserQuery(): Builder
    {
        return static::query()->forCurrentUser();
    }

    /**
     * Get user-scoped query builder for specific user
     */
    public static function forUserQuery(int $userId): Builder
    {
        return static::query()->forUser($userId);
    }

    /**
     * Boot the trait
     */
    protected static function bootHasUserScope(): void
    {
        // Automatically set user_id when creating records
        static::creating(function ($model) {
            if (empty($model->user_id) && Auth::check()) {
                $model->user_id = Auth::id();
            }
        });
    }
}
