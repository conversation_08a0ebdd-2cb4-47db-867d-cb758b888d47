<?php

namespace Database\Factories;

use App\Models\FollowUp;
use App\Models\User;
use App\Models\Invoice;
use Illuminate\Database\Eloquent\Factories\Factory;

class FollowUpFactory extends Factory
{
    protected $model = FollowUp::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'invoice_id' => Invoice::factory(),
            'type' => fake()->randomElement(['friendly', 'reminder', 'legal_notice']),
            'message' => fake()->paragraph(),
            'method' => fake()->randomElement(['email', 'whatsapp', 'both']),
            'status' => fake()->randomElement(['pending', 'sent', 'failed']),
            'scheduled_at' => fake()->dateTimeBetween('now', '+30 days'),
            'sent_at' => null,
        ];
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'sent_at' => null,
        ]);
    }

    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'sent',
            'sent_at' => fake()->dateTimeBetween('-30 days', 'now'),
        ]);
    }

    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'failed',
            'sent_at' => null,
        ]);
    }

    public function friendly(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'friendly',
            'message' => 'Hi! Just a friendly reminder about your pending invoice. Please let me know if you have any questions.',
        ]);
    }

    public function reminder(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'reminder',
            'message' => 'This is a reminder that your invoice payment is due. Please process the payment at your earliest convenience.',
        ]);
    }

    public function legalNotice(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'legal_notice',
            'message' => 'This is a formal notice regarding your overdue payment. Please settle the outstanding amount immediately to avoid further action.',
        ]);
    }

    public function emailOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'method' => 'email',
        ]);
    }

    public function whatsappOnly(): static
    {
        return $this->state(fn (array $attributes) => [
            'method' => 'whatsapp',
        ]);
    }

    public function bothMethods(): static
    {
        return $this->state(fn (array $attributes) => [
            'method' => 'both',
        ]);
    }

    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'scheduled_at' => fake()->dateTimeBetween('+1 day', '+7 days'),
            'status' => 'pending',
        ]);
    }

    public function overdue(): static
    {
        return $this->state(fn (array $attributes) => [
            'scheduled_at' => fake()->dateTimeBetween('-7 days', '-1 day'),
            'status' => 'pending',
        ]);
    }

    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    public function forInvoice(Invoice $invoice): static
    {
        return $this->state(fn (array $attributes) => [
            'invoice_id' => $invoice->id,
            'user_id' => $invoice->user_id,
        ]);
    }
}
