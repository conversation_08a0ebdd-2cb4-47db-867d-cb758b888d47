<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Task extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'user_id',
        'assigned_to',
        'title',
        'description',
        'status',
        'priority',
        'due_date',
        'completed_date',
        'estimated_hours',
        'actual_hours',
        'is_billable',
        'sort_order',
        'notes',
        'custom_fields',
    ];

    protected function casts(): array
    {
        return [
            'due_date' => 'date',
            'completed_date' => 'date',
            'estimated_hours' => 'decimal:2',
            'actual_hours' => 'decimal:2',
            'is_billable' => 'boolean',
            'sort_order' => 'integer',
            'custom_fields' => 'array',
        ];
    }

    /**
     * Get the project that owns the task.
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * Get the user that created the task.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the user assigned to the task.
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the time entries for the task.
     */
    public function timeEntries(): HasMany
    {
        return $this->hasMany(TimeEntry::class);
    }

    /**
     * Scope a query to only include completed tasks.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include pending tasks.
     */
    public function scopePending($query)
    {
        return $query->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * Scope a query to only include overdue tasks.
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
            ->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * Scope a query to order by sort order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at');
    }

    /**
     * Get total hours tracked for this task.
     */
    public function getTotalHoursAttribute(): float
    {
        return $this->timeEntries()
            ->whereNotNull('end_time')
            ->sum('duration_minutes') / 60;
    }

    /**
     * Get total billable hours for this task.
     */
    public function getBillableHoursAttribute(): float
    {
        return $this->timeEntries()
            ->where('is_billable', true)
            ->whereNotNull('end_time')
            ->sum('duration_minutes') / 60;
    }

    /**
     * Check if task is overdue.
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date &&
               $this->due_date->isPast() &&
               !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Get days until due date.
     */
    public function getDaysUntilDueAttribute(): ?int
    {
        if (!$this->due_date) {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    /**
     * Mark task as completed.
     */
    public function markAsCompleted(): void
    {
        $this->update([
            'status' => 'completed',
            'completed_date' => now(),
            'actual_hours' => $this->total_hours,
        ]);
    }

    /**
     * Get progress percentage based on time tracked vs estimated.
     */
    public function getProgressPercentageAttribute(): float
    {
        if (!$this->estimated_hours || $this->estimated_hours <= 0) {
            return $this->status === 'completed' ? 100 : 0;
        }

        $percentage = ($this->total_hours / $this->estimated_hours) * 100;
        return min(100, round($percentage, 2));
    }
}
