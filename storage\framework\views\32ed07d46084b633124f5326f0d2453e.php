<?php $attributes ??= new \Illuminate\View\ComponentAttributeBag;

$__newAttributes = [];
$__propNames = \Illuminate\View\ComponentAttributeBag::extractPropNames((['user']));

foreach ($attributes->all() as $__key => $__value) {
    if (in_array($__key, $__propNames)) {
        $$__key = $$__key ?? $__value;
    } else {
        $__newAttributes[$__key] = $__value;
    }
}

$attributes = new \Illuminate\View\ComponentAttributeBag($__newAttributes);

unset($__propNames);
unset($__newAttributes);

foreach (array_filter((['user']), 'is_string', ARRAY_FILTER_USE_KEY) as $__key => $__value) {
    $$__key = $$__key ?? $__value;
}

$__defined_vars = get_defined_vars();

foreach ($attributes->all() as $__key => $__value) {
    if (array_key_exists($__key, $__defined_vars)) unset($$__key);
}

unset($__defined_vars); ?>

<div x-data="{
    sidebarOpen: false,
    init() {
        // Show sidebar by default on desktop
        if (window.innerWidth >= 1024) {
            this.sidebarOpen = true;
        }
        // Listen for window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 1024) {
                this.sidebarOpen = true;
            }
        });
    }
}" class="flex h-screen bg-gray-50">
    <!-- Mobile menu button -->
    <div class="lg:hidden fixed top-4 left-4 z-50">
        <button @click="sidebarOpen = !sidebarOpen"
                class="bg-white p-2 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path x-show="!sidebarOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                <path x-show="sidebarOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
        </button>
    </div>

    <!-- Mobile sidebar overlay -->
    <div x-show="sidebarOpen"
         x-transition:enter="transition-opacity ease-linear duration-300"
         x-transition:enter-start="opacity-0"
         x-transition:enter-end="opacity-100"
         x-transition:leave="transition-opacity ease-linear duration-300"
         x-transition:leave-start="opacity-100"
         x-transition:leave-end="opacity-0"
         class="fixed inset-0 z-40 lg:hidden">
        <div class="fixed inset-0 bg-gray-600 bg-opacity-75" @click="sidebarOpen = false"></div>
    </div>

    <!-- Sidebar -->
    <div x-show="sidebarOpen"
         x-transition:enter="transition ease-in-out duration-300 transform"
         x-transition:enter-start="-translate-x-full"
         x-transition:enter-end="translate-x-0"
         x-transition:leave="transition ease-in-out duration-300 transform"
         x-transition:leave-start="translate-x-0"
         x-transition:leave-end="-translate-x-full"
         class="fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg lg:translate-x-0 lg:static lg:inset-0 lg:block">
        
        <!-- Sidebar Header -->
        <div class="flex items-center justify-between h-16 px-6 border-b border-gray-200">
            <div class="flex items-center">
                <?php if (isset($component)) { $__componentOriginalfccce0c9b9d71a9e71c5ef4c4018973f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfccce0c9b9d71a9e71c5ef4c4018973f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.freeligo-logo','data' => ['class' => 'h-8 w-auto']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('freeligo-logo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-8 w-auto']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfccce0c9b9d71a9e71c5ef4c4018973f)): ?>
<?php $attributes = $__attributesOriginalfccce0c9b9d71a9e71c5ef4c4018973f; ?>
<?php unset($__attributesOriginalfccce0c9b9d71a9e71c5ef4c4018973f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfccce0c9b9d71a9e71c5ef4c4018973f)): ?>
<?php $component = $__componentOriginalfccce0c9b9d71a9e71c5ef4c4018973f; ?>
<?php unset($__componentOriginalfccce0c9b9d71a9e71c5ef4c4018973f); ?>
<?php endif; ?>
                <span class="ml-2 text-xl font-bold bg-gradient-to-r from-blue-600 to-blue-800 bg-clip-text text-transparent">Freeligo</span>
            </div>
            <button @click="sidebarOpen = false" class="lg:hidden text-gray-500 hover:text-gray-700">
                <i class="fas fa-times text-lg"></i>
            </button>
        </div>

        <!-- Simple User Info -->
        <?php if(auth()->user()->hasRole('admin')): ?>
            <div class="px-6 py-3 border-b border-gray-200 bg-red-50">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-600">Access Level</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        <i class="fas fa-shield-alt w-3 h-3 mr-1"></i>
                        Admin
                    </span>
                </div>
                <div class="mt-2">
                    <span class="text-xs text-gray-500">Full system access</span>
                </div>
            </div>
        <?php else: ?>
            <div class="px-6 py-3 border-b border-gray-200 bg-gray-50">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-600">Current Plan</span>
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                        <i class="fas fa-gift w-3 h-3 mr-1"></i>
                        Free
                    </span>
                </div>
                <div class="mt-2">
                    <a href="<?php echo e(route('subscriptions.plans')); ?>"
                       class="text-xs text-blue-600 hover:text-blue-800 font-medium">
                        Upgrade for more features →
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- User Profile Section -->
        <div class="p-6 border-b border-gray-200">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                        <?php echo e(substr($user->name, 0, 1)); ?>

                    </div>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-gray-900"><?php echo e($user->name); ?></p>
                    <p class="text-xs text-gray-500"><?php echo e($user->email); ?></p>
                </div>
            </div>
        </div>

        <!-- Navigation Links -->
        <nav class="flex-1 px-4 py-6 space-y-2">
            <!-- Dashboard -->
            <a href="<?php echo e(route('dashboard')); ?>"
               class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 group <?php echo e(request()->routeIs('dashboard') ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'); ?>">
                <i class="fas fa-tachometer-alt w-5 h-5 mr-3 transition-colors duration-200 <?php echo e(request()->routeIs('dashboard') ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'); ?>"></i>
                <span>Dashboard</span>
            </a>

            <!-- Clients -->
            <a href="<?php echo e(route('clients.index')); ?>"
               class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 group <?php echo e(request()->routeIs('clients.*') ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'); ?>">
                <i class="fas fa-users w-5 h-5 mr-3 transition-colors duration-200 <?php echo e(request()->routeIs('clients.*') ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'); ?>"></i>
                <span>Clients</span>
            </a>

            <!-- Invoices -->
            <a href="<?php echo e(route('invoices.index')); ?>"
               class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 group <?php echo e(request()->routeIs('invoices.*') ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'); ?>">
                <i class="fas fa-file-invoice-dollar w-5 h-5 mr-3 transition-colors duration-200 <?php echo e(request()->routeIs('invoices.*') ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'); ?>"></i>
                <span>Invoices</span>
            </a>

            <!-- Contracts -->
            <a href="<?php echo e(route('contracts.index')); ?>"
               class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 group <?php echo e(request()->routeIs('contracts.*') ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'); ?>">
                <i class="fas fa-file-contract w-5 h-5 mr-3 transition-colors duration-200 <?php echo e(request()->routeIs('contracts.*') ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'); ?>"></i>
                <span>Contracts</span>
            </a>

            <!-- Projects -->
            <a href="<?php echo e(route('projects.index')); ?>"
               class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 group <?php echo e(request()->routeIs('projects.*') ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'); ?>">
                <i class="fas fa-project-diagram w-5 h-5 mr-3 transition-colors duration-200 <?php echo e(request()->routeIs('projects.*') ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'); ?>"></i>
                <span>Projects</span>
            </a>

            <!-- Time Tracking -->
            <a href="<?php echo e(route('time-tracking.index')); ?>"
               class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 group <?php echo e(request()->routeIs('time-tracking.*') ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'); ?>">
                <i class="fas fa-clock w-5 h-5 mr-3 transition-colors duration-200 <?php echo e(request()->routeIs('time-tracking.*') ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'); ?>"></i>
                <span>Time Tracking</span>
            </a>

            <!-- TDS -->
            <a href="<?php echo e(route('tds.index')); ?>"
               class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 group <?php echo e(request()->routeIs('tds.*') ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'); ?>">
                <i class="fas fa-receipt w-5 h-5 mr-3 transition-colors duration-200 <?php echo e(request()->routeIs('tds.*') ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'); ?>"></i>
                <span>TDS Records</span>
            </a>

            <!-- Follow-ups -->
            <a href="<?php echo e(route('followups.index')); ?>"
               class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 group <?php echo e(request()->routeIs('followups.*') ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'); ?>">
                <i class="fas fa-clock w-5 h-5 mr-3 transition-colors duration-200 <?php echo e(request()->routeIs('followups.*') ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'); ?>"></i>
                <span>Follow-ups</span>
            </a>

            <!-- File Manager -->
            <a href="<?php echo e(route('files.index')); ?>"
               class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 group <?php echo e(request()->routeIs('files.*') ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'); ?>">
                <i class="fas fa-folder w-5 h-5 mr-3 transition-colors duration-200 <?php echo e(request()->routeIs('files.*') ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'); ?>"></i>
                <span>File Manager</span>
            </a>

            <?php if(auth()->user()->hasRole('admin')): ?>
            <!-- Admin Section -->
            <div class="pt-6 mt-6 border-t border-gray-200">
                <p class="px-4 text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">
                    Administration
                </p>
                
                <a href="<?php echo e(route('admin.dashboard')); ?>"
                   class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 group <?php echo e(request()->routeIs('admin.dashboard') ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'); ?>">
                    <i class="fas fa-cog w-5 h-5 mr-3 transition-colors duration-200 <?php echo e(request()->routeIs('admin.dashboard') ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'); ?>"></i>
                    <span>Admin Panel</span>
                </a>

                <a href="<?php echo e(route('admin.files.index')); ?>"
                   class="flex items-center px-4 py-3 rounded-lg transition-all duration-200 group <?php echo e(request()->routeIs('admin.files.*') ? 'text-blue-600 bg-blue-50 border-r-2 border-blue-600' : 'text-gray-600 hover:text-blue-600 hover:bg-blue-50'); ?>">
                    <i class="fas fa-folder-open w-5 h-5 mr-3 transition-colors duration-200 <?php echo e(request()->routeIs('admin.files.*') ? 'text-blue-600' : 'text-gray-400 group-hover:text-blue-500'); ?>"></i>
                    <span>File Management</span>
                </a>
            </div>
            <?php endif; ?>
        </nav>

        <!-- Sidebar Footer -->
        <div class="p-4 border-t border-gray-200">
            <form method="POST" action="<?php echo e(route('logout')); ?>">
                <?php echo csrf_field(); ?>
                <button type="submit" class="w-full flex items-center px-4 py-3 text-left text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all duration-200 group">
                    <i class="fas fa-sign-out-alt w-5 h-5 mr-3 text-red-500 transition-colors duration-200"></i>
                    <span>Logout</span>
                </button>
            </form>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex-1 flex flex-col overflow-hidden lg:ml-0">
        <!-- Top Header for Mobile -->
        <header class="bg-white shadow-sm border-b border-gray-200 lg:hidden">
            <div class="flex items-center justify-between h-16 px-4 pl-16">
                <?php if (isset($component)) { $__componentOriginalfccce0c9b9d71a9e71c5ef4c4018973f = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalfccce0c9b9d71a9e71c5ef4c4018973f = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.freeligo-logo','data' => ['class' => 'h-8 w-auto mx-auto']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('freeligo-logo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['class' => 'h-8 w-auto mx-auto']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalfccce0c9b9d71a9e71c5ef4c4018973f)): ?>
<?php $attributes = $__attributesOriginalfccce0c9b9d71a9e71c5ef4c4018973f; ?>
<?php unset($__attributesOriginalfccce0c9b9d71a9e71c5ef4c4018973f); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalfccce0c9b9d71a9e71c5ef4c4018973f)): ?>
<?php $component = $__componentOriginalfccce0c9b9d71a9e71c5ef4c4018973f; ?>
<?php unset($__componentOriginalfccce0c9b9d71a9e71c5ef4c4018973f); ?>
<?php endif; ?>
            </div>
        </header>

        <!-- Page Content -->
        <main class="flex-1 overflow-y-auto bg-gray-50">
            <?php echo e($slot); ?>

        </main>
    </div>
</div>
<?php /**PATH C:\laragon\www\freeligo\resources\views/components/sidebar.blade.php ENDPATH**/ ?>