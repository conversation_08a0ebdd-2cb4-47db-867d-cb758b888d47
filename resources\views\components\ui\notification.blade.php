@props([
    'type' => 'info',
    'title' => null,
    'dismissible' => true,
    'icon' => null
])

@php
$typeClasses = [
    'success' => 'bg-green-50 border-green-200 text-green-800',
    'error' => 'bg-red-50 border-red-200 text-red-800',
    'warning' => 'bg-yellow-50 border-yellow-200 text-yellow-800',
    'info' => 'bg-blue-50 border-blue-200 text-blue-800'
];

$iconClasses = [
    'success' => 'text-green-400',
    'error' => 'text-red-400',
    'warning' => 'text-yellow-400',
    'info' => 'text-blue-400'
];

$defaultIcons = [
    'success' => 'fas fa-check-circle',
    'error' => 'fas fa-exclamation-circle',
    'warning' => 'fas fa-exclamation-triangle',
    'info' => 'fas fa-info-circle'
];

$classes = $typeClasses[$type] ?? $typeClasses['info'];
$iconColor = $iconClasses[$type] ?? $iconClasses['info'];
$displayIcon = $icon ?? $defaultIcons[$type];
@endphp

<div x-data="{ show: true }" 
     x-show="show"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform scale-95"
     x-transition:enter-end="opacity-100 transform scale-100"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform scale-100"
     x-transition:leave-end="opacity-0 transform scale-95"
     class="rounded-lg border p-4 {{ $classes }} {{ $attributes->get('class') }}">
    
    <div class="flex items-start">
        @if($displayIcon)
            <div class="flex-shrink-0">
                <i class="{{ $displayIcon }} {{ $iconColor }} text-lg"></i>
            </div>
        @endif
        
        <div class="ml-3 flex-1">
            @if($title)
                <h3 class="text-sm font-medium mb-1">{{ $title }}</h3>
            @endif
            
            <div class="text-sm">
                {{ $slot }}
            </div>
        </div>
        
        @if($dismissible)
            <div class="ml-auto pl-3">
                <button @click="show = false" 
                        class="inline-flex rounded-md p-1.5 hover:bg-black hover:bg-opacity-10 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent focus:ring-gray-500 transition-colors duration-200">
                    <span class="sr-only">Dismiss</span>
                    <i class="fas fa-times text-sm {{ $iconColor }}"></i>
                </button>
            </div>
        @endif
    </div>
</div>
