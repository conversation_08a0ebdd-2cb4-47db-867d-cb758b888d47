<?php

namespace App\Repositories;

use App\Models\Contract;
use App\Traits\HasFiltering;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class ContractRepository extends BaseRepository
{
    use HasFiltering;

    protected function getModel(): Model
    {
        return new Contract();
    }

    /**
     * Get contracts for a specific user with filters
     */
    public function getForUser(int $userId, Request $request = null): LengthAwarePaginator
    {
        $query = $this->model->newQuery()
            ->with(['client', 'contractTemplate'])
            ->where('user_id', $userId);

        if ($request) {
            $query = $this->applyFilters($query, $request, [
                'search_fields' => ['title', 'client.name', 'client.company_name'],
                'status_field' => 'status',
                'date_field' => 'created_at',
                'sort_by' => 'created_at',
                'sort_direction' => 'desc',
            ]);

            return $this->applyPagination($query, $request);
        }

        return $query->latest()->paginate(15);
    }

    /**
     * Get contracts by status for user
     */
    public function getByStatusForUser(int $userId, string $status): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->where('status', $status)
            ->with(['client', 'contractTemplate'])
            ->get();
    }

    /**
     * Get recent contracts for user
     */
    public function getRecentForUser(int $userId, int $limit = 10): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->with(['client', 'contractTemplate'])
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get contract statistics for user
     */
    public function getStatsForUser(int $userId): array
    {
        $baseQuery = $this->model->newQuery()->where('user_id', $userId);

        return [
            'total_count' => $baseQuery->count(),
            'draft_count' => (clone $baseQuery)->where('status', 'draft')->count(),
            'sent_count' => (clone $baseQuery)->where('status', 'sent')->count(),
            'signed_count' => (clone $baseQuery)->where('status', 'signed')->count(),
            'expired_count' => (clone $baseQuery)->where('status', 'expired')->count(),
            'cancelled_count' => (clone $baseQuery)->where('status', 'cancelled')->count(),
        ];
    }

    /**
     * Get contracts for client
     */
    public function getForClient(int $clientId, int $limit = null): Collection
    {
        $query = $this->model->newQuery()
            ->where('client_id', $clientId)
            ->with('contractTemplate')
            ->latest();

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get current month contract count for user
     */
    public function getCurrentMonthCountForUser(int $userId): int
    {
        $currentMonth = now()->format('Y-m');
        
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
            ->count();
    }

    /**
     * Get contracts by template
     */
    public function getByTemplate(int $templateId): Collection
    {
        return $this->model->newQuery()
            ->where('contract_template_id', $templateId)
            ->with(['client', 'user'])
            ->get();
    }

    /**
     * Get contracts expiring soon
     */
    public function getExpiringSoon(int $userId, int $days = 30): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->where('status', 'signed')
            ->whereNotNull('expiry_date')
            ->whereBetween('expiry_date', [now(), now()->addDays($days)])
            ->with('client')
            ->orderBy('expiry_date')
            ->get();
    }

    /**
     * Get expired contracts
     */
    public function getExpired(int $userId): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->where('status', 'signed')
            ->whereNotNull('expiry_date')
            ->where('expiry_date', '<', now())
            ->with('client')
            ->orderBy('expiry_date', 'desc')
            ->get();
    }

    /**
     * Search contracts with advanced filters
     */
    public function searchContracts(int $userId, array $filters): Collection
    {
        $query = $this->model->newQuery()
            ->where('user_id', $userId)
            ->with(['client', 'contractTemplate']);

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['client_id'])) {
            $query->where('client_id', $filters['client_id']);
        }

        if (!empty($filters['template_id'])) {
            $query->where('contract_template_id', $filters['template_id']);
        }

        if (!empty($filters['start_date'])) {
            $query->whereDate('created_at', '>=', $filters['start_date']);
        }

        if (!empty($filters['end_date'])) {
            $query->whereDate('created_at', '<=', $filters['end_date']);
        }

        if (!empty($filters['expiry_start'])) {
            $query->whereDate('expiry_date', '>=', $filters['expiry_start']);
        }

        if (!empty($filters['expiry_end'])) {
            $query->whereDate('expiry_date', '<=', $filters['expiry_end']);
        }

        return $query->latest()->get();
    }

    /**
     * Get contract analytics data
     */
    public function getAnalyticsData(int $userId): array
    {
        $contracts = $this->model->newQuery()
            ->where('user_id', $userId)
            ->with(['client', 'contractTemplate'])
            ->get();

        $statusCounts = $contracts->groupBy('status')->map->count();
        $templateUsage = $contracts->groupBy('contract_template_id')->map->count();
        $clientContracts = $contracts->groupBy('client_id')->map->count();

        return [
            'total_contracts' => $contracts->count(),
            'status_breakdown' => $statusCounts->toArray(),
            'template_usage' => $templateUsage->toArray(),
            'client_distribution' => $clientContracts->toArray(),
            'monthly_creation' => $this->getMonthlyCreationData($userId),
            'avg_time_to_sign' => $this->getAverageTimeToSign($userId),
        ];
    }

    /**
     * Get monthly contract creation data
     */
    public function getMonthlyCreationData(int $userId, int $months = 12): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, COUNT(*) as count')
            ->groupByRaw('YEAR(created_at), MONTH(created_at)')
            ->orderByRaw('YEAR(created_at) DESC, MONTH(created_at) DESC')
            ->limit($months)
            ->get();
    }

    /**
     * Get average time to sign contracts
     */
    private function getAverageTimeToSign(int $userId): float
    {
        $signedContracts = $this->model->newQuery()
            ->where('user_id', $userId)
            ->where('status', 'signed')
            ->whereNotNull('signed_date')
            ->get();

        if ($signedContracts->isEmpty()) {
            return 0;
        }

        $totalDays = $signedContracts->sum(function ($contract) {
            return $contract->created_at->diffInDays($contract->signed_date);
        });

        return $totalDays / $signedContracts->count();
    }
}
