<?php

namespace Tests\Unit\Models;

use App\Models\User;
use App\Models\Client;
use App\Models\Invoice;
use App\Models\Contract;
use App\Models\FollowUp;
use App\Models\Plan;
use App\Models\PlanFeature;
use App\Models\UserSubscription;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class UserTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        $this->artisan('db:seed', ['--class' => 'Database\\Seeders\\RolePermissionSeeder']);
    }

    public function test_user_has_fillable_attributes()
    {
        $user = new User();
        $fillable = [
            'name', 'email', 'password', 'business_name', 'address', 'phone',
            'gst_number', 'logo_path', 'bank_name', 'account_number', 'ifsc_code',
            'pan_number', 'current_plan_id', 'usage_stats', 'trial_ends_at'
        ];

        $this->assertEquals($fillable, $user->getFillable());
    }

    public function test_user_has_hidden_attributes()
    {
        $user = new User();
        $hidden = ['password', 'remember_token'];

        $this->assertEquals($hidden, $user->getHidden());
    }

    public function test_user_casts_attributes_correctly()
    {
        $user = User::factory()->create([
            'usage_stats' => ['invoices' => 5, 'contracts' => 2],
            'trial_ends_at' => now()->addDays(30),
        ]);

        $this->assertIsArray($user->usage_stats);
        $this->assertInstanceOf(\Carbon\Carbon::class, $user->trial_ends_at);
        $this->assertInstanceOf(\Carbon\Carbon::class, $user->email_verified_at);
    }

    public function test_user_has_many_clients()
    {
        $user = User::factory()->create();
        $clients = Client::factory()->count(3)->forUser($user)->create();

        $this->assertCount(3, $user->clients);
        $this->assertInstanceOf(Client::class, $user->clients->first());
    }

    public function test_user_has_many_invoices()
    {
        $user = User::factory()->create();
        $invoices = Invoice::factory()->count(5)->forUser($user)->create();

        $this->assertCount(5, $user->invoices);
        $this->assertInstanceOf(Invoice::class, $user->invoices->first());
    }

    public function test_user_has_many_contracts()
    {
        $user = User::factory()->create();
        $contracts = Contract::factory()->count(2)->forUser($user)->create();

        $this->assertCount(2, $user->contracts);
        $this->assertInstanceOf(Contract::class, $user->contracts->first());
    }

    public function test_user_has_many_follow_ups()
    {
        $user = User::factory()->create();
        $followUps = FollowUp::factory()->count(4)->forUser($user)->create();

        $this->assertCount(4, $user->followUps);
        $this->assertInstanceOf(FollowUp::class, $user->followUps->first());
    }

    public function test_user_belongs_to_current_plan()
    {
        $plan = Plan::factory()->create();
        $user = User::factory()->create(['current_plan_id' => $plan->id]);

        $this->assertInstanceOf(Plan::class, $user->currentPlan);
        $this->assertEquals($plan->id, $user->currentPlan->id);
    }

    public function test_user_has_many_subscriptions()
    {
        $user = User::factory()->create();
        $subscriptions = UserSubscription::factory()->count(2)->create(['user_id' => $user->id]);

        $this->assertCount(2, $user->subscriptions);
        $this->assertInstanceOf(UserSubscription::class, $user->subscriptions->first());
    }

    public function test_user_can_get_active_subscription()
    {
        $user = User::factory()->create();
        
        // Create inactive subscription
        UserSubscription::factory()->create([
            'user_id' => $user->id,
            'status' => 'expired'
        ]);
        
        // Create active subscription
        $activeSubscription = UserSubscription::factory()->active()->create([
            'user_id' => $user->id
        ]);

        $this->assertCount(1, $user->activeSubscription);
        $this->assertInstanceOf(UserSubscription::class, $user->activeSubscription->first());
        $this->assertEquals($activeSubscription->id, $user->activeSubscription->first()->id);
        $this->assertEquals('active', $user->activeSubscription->first()->status);
    }

    public function test_user_can_check_if_has_feature()
    {
        $plan = Plan::factory()->create();

        // Create plan features
        PlanFeature::factory()->forPlan($plan)->create([
            'feature_key' => 'unlimited_invoices',
            'feature_value' => 'true',
            'feature_type' => 'boolean'
        ]);

        PlanFeature::factory()->forPlan($plan)->create([
            'feature_key' => 'no_watermark',
            'feature_value' => 'true',
            'feature_type' => 'boolean'
        ]);

        $user = User::factory()->create(['current_plan_id' => $plan->id]);

        $this->assertTrue($user->hasFeature('unlimited_invoices'));
        $this->assertTrue($user->hasFeature('no_watermark'));
        $this->assertFalse($user->hasFeature('api_access'));
    }

    public function test_user_without_plan_has_no_features()
    {
        $user = User::factory()->create(['current_plan_id' => null]);

        $this->assertFalse($user->hasFeature('unlimited_invoices'));
    }

    public function test_user_can_perform_action_based_on_plan_limits()
    {
        $plan = Plan::factory()->create();
        $user = User::factory()->create(['current_plan_id' => $plan->id]);

        // Create some invoices
        Invoice::factory()->count(3)->forUser($user)->create();

        // This would need to be implemented in the User model
        // For now, just test the relationship works
        $this->assertEquals(3, $user->invoices()->count());
        $this->assertEquals($plan->id, $user->currentPlan->id);
    }

    public function test_user_with_unlimited_plan_can_always_perform_actions()
    {
        $plan = Plan::factory()->create();
        $user = User::factory()->create(['current_plan_id' => $plan->id]);

        // Create many invoices
        Invoice::factory()->count(10)->forUser($user)->create();

        // This would need to be implemented in the User model
        // For now, just test the relationship works
        $this->assertEquals(10, $user->invoices()->count());
        $this->assertEquals($plan->id, $user->currentPlan->id);
    }
}
