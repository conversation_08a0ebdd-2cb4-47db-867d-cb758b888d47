<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class RecurringInvoice extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'client_id',
        'template_data',
        'frequency',
        'interval_count',
        'start_date',
        'end_date',
        'next_generation_date',
        'status',
        'auto_send',
        'last_generated_at',
        'total_generated',
        'max_occurrences',
        'automation_settings',
        'success_rate',
        'consecutive_failures',
        'last_failure_at',
        'client_behavior_data',
        'dynamic_frequency_enabled',
        'original_frequency',
        'frequency_adjustment_history',
        'ai_description_enabled',
        'retry_settings',
    ];

    protected function casts(): array
    {
        return [
            'template_data' => 'array',
            'start_date' => 'date',
            'end_date' => 'date',
            'next_generation_date' => 'date',
            'last_generated_at' => 'datetime',
            'last_failure_at' => 'datetime',
            'auto_send' => 'boolean',
            'dynamic_frequency_enabled' => 'boolean',
            'ai_description_enabled' => 'boolean',
            'total_generated' => 'integer',
            'max_occurrences' => 'integer',
            'interval_count' => 'integer',
            'consecutive_failures' => 'integer',
            'success_rate' => 'decimal:2',
            'automation_settings' => 'array',
            'client_behavior_data' => 'array',
            'frequency_adjustment_history' => 'array',
            'retry_settings' => 'array',
        ];
    }

    /**
     * Get the user that owns the recurring invoice.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the client that owns the recurring invoice.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the generated invoices.
     */
    public function generatedInvoices(): HasMany
    {
        return $this->hasMany(Invoice::class, 'recurring_invoice_id');
    }

    /**
     * Scope a query to only include active recurring invoices.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include recurring invoices ready for generation.
     */
    public function scopeReadyForGeneration($query)
    {
        return $query->where('status', 'active')
                    ->where('next_generation_date', '<=', now())
                    ->where(function ($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                    })
                    ->where(function ($q) {
                        $q->whereNull('max_occurrences')
                          ->orWhereRaw('total_generated < max_occurrences');
                    });
    }

    /**
     * Calculate the next generation date based on frequency.
     */
    public function calculateNextGenerationDate(): \Carbon\Carbon
    {
        $baseDate = $this->last_generated_at ?? $this->start_date;
        
        return match($this->frequency) {
            'daily' => $baseDate->addDays($this->interval_count),
            'weekly' => $baseDate->addWeeks($this->interval_count),
            'monthly' => $baseDate->addMonths($this->interval_count),
            'quarterly' => $baseDate->addMonths($this->interval_count * 3),
            'yearly' => $baseDate->addYears($this->interval_count),
            default => $baseDate->addMonths(1),
        };
    }

    /**
     * Check if the recurring invoice should continue generating.
     */
    public function shouldContinueGenerating(): bool
    {
        if ($this->status !== 'active') {
            return false;
        }

        if ($this->end_date && $this->end_date < now()) {
            return false;
        }

        if ($this->max_occurrences && $this->total_generated >= $this->max_occurrences) {
            return false;
        }

        return true;
    }

    /**
     * Update the next generation date.
     */
    public function updateNextGenerationDate(): void
    {
        if ($this->shouldContinueGenerating()) {
            $this->update([
                'next_generation_date' => $this->calculateNextGenerationDate(),
            ]);
        } else {
            $this->update(['status' => 'completed']);
        }
    }

    /**
     * Record a successful generation.
     */
    public function recordSuccess(): void
    {
        $totalAttempts = $this->total_generated + $this->consecutive_failures;
        $successRate = $totalAttempts > 0 ? (($this->total_generated + 1) / ($totalAttempts + 1)) * 100 : 100;

        $this->update([
            'consecutive_failures' => 0,
            'success_rate' => round($successRate, 2),
            'last_failure_at' => null,
        ]);
    }

    /**
     * Record a failed generation.
     */
    public function recordFailure(string $error): void
    {
        $totalAttempts = $this->total_generated + $this->consecutive_failures + 1;
        $successRate = $totalAttempts > 0 ? ($this->total_generated / $totalAttempts) * 100 : 0;

        $this->update([
            'consecutive_failures' => $this->consecutive_failures + 1,
            'success_rate' => round($successRate, 2),
            'last_failure_at' => now(),
        ]);

        // Auto-pause if too many consecutive failures
        if ($this->consecutive_failures >= 5) {
            $this->update(['status' => 'paused']);
        }
    }

    /**
     * Update client behavior data.
     */
    public function updateClientBehaviorData(array $behaviorData): void
    {
        $this->update(['client_behavior_data' => $behaviorData]);
    }

    /**
     * Adjust frequency based on client behavior.
     */
    public function adjustFrequency(string $newFrequency, string $reason): void
    {
        if (!$this->dynamic_frequency_enabled) {
            return;
        }

        $history = $this->frequency_adjustment_history ?? [];
        $history[] = [
            'from' => $this->frequency,
            'to' => $newFrequency,
            'reason' => $reason,
            'adjusted_at' => now()->toISOString(),
        ];

        $this->update([
            'original_frequency' => $this->original_frequency ?? $this->frequency,
            'frequency' => $newFrequency,
            'frequency_adjustment_history' => $history,
            'next_generation_date' => $this->calculateNextGenerationDate(),
        ]);
    }

    /**
     * Get default automation settings.
     */
    public function getDefaultAutomationSettings(): array
    {
        return [
            'auto_retry' => true,
            'max_retries' => 3,
            'retry_delay_hours' => 2,
            'auto_pause_on_failure' => true,
            'failure_threshold' => 5,
            'smart_frequency_adjustment' => false,
            'ai_enhanced_descriptions' => false,
        ];
    }

    /**
     * Get automation settings with defaults.
     */
    public function getAutomationSettings(): array
    {
        return array_merge(
            $this->getDefaultAutomationSettings(),
            $this->automation_settings ?? []
        );
    }

    /**
     * Check if retry is needed and allowed.
     */
    public function shouldRetry(): bool
    {
        $settings = $this->getAutomationSettings();

        if (!$settings['auto_retry']) {
            return false;
        }

        if ($this->consecutive_failures >= $settings['max_retries']) {
            return false;
        }

        if ($this->last_failure_at) {
            $retryAfter = $this->last_failure_at->addHours($settings['retry_delay_hours']);
            return now()->gte($retryAfter);
        }

        return true;
    }
}
