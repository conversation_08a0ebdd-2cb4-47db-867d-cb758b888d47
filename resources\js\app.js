import './bootstrap';

import Alpine from 'alpinejs';
import Toastify from 'toastify-js';
import Swal from 'sweetalert2';

// Make libraries globally available
window.Alpine = Alpine;
window.Toastify = Toastify;
window.Swal = Swal;

// Global toast notification function
window.showToast = function(message, type = 'success') {
    const colors = {
        success: 'linear-gradient(to right, #22c55e, #16a34a)',
        error: 'linear-gradient(to right, #ef4444, #dc2626)',
        warning: 'linear-gradient(to right, #f59e0b, #d97706)',
        info: 'linear-gradient(to right, #0ea5e9, #0284c7)'
    };

    Toastify({
        text: message,
        duration: 3000,
        gravity: "top",
        position: "right",
        style: {
            background: colors[type] || colors.success,
        },
        stopOnFocus: true,
    }).showToast();
};

// Global confirmation dialog function
window.confirmAction = function(title, text, confirmButtonText = 'Yes, do it!') {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#ef4444',
        cancelButtonColor: '#6b7280',
        confirmButtonText: confirmButtonText,
        cancelButtonText: 'Cancel'
    });
};

// Global success dialog function
window.showSuccess = function(title, text) {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'success',
        confirmButtonColor: '#22c55e'
    });
};

// Global error dialog function
window.showError = function(title, text) {
    return Swal.fire({
        title: title,
        text: text,
        icon: 'error',
        confirmButtonColor: '#ef4444'
    });
};

Alpine.start();
