<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Exception;

class GroqService
{
    private ?string $apiKey;
    private ?string $model;
    private ?int $maxTokens;
    private ?float $temperature;
    private ?int $timeout;
    private string $baseUrl;

    public function __construct()
    {
        $this->apiKey = config('services.groq.api_key');
        $this->model = config('services.groq.model');
        $this->maxTokens = config('services.groq.max_tokens');
        $this->temperature = config('services.groq.temperature');
        $this->timeout = config('services.groq.timeout');
        $this->baseUrl = config('services.groq.base_url');
    }

    /**
     * Generate text completion using Groq API
     */
    public function generateCompletion(string $prompt, array $options = []): array
    {
        if (empty($this->apiKey)) {
            return ['success' => false, 'error' => 'Groq API key is not configured'];
        }

        try {
            $response = Http::timeout($this->timeout)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . $this->apiKey,
                    'Content-Type' => 'application/json',
                ])
                ->post($this->baseUrl . '/chat/completions', [
                    'model' => $options['model'] ?? $this->model,
                    'messages' => [
                        [
                            'role' => 'user',
                            'content' => $prompt
                        ]
                    ],
                    'max_tokens' => $options['max_tokens'] ?? $this->maxTokens,
                    'temperature' => $options['temperature'] ?? $this->temperature,
                    'stream' => false,
                ]);

            if ($response->successful()) {
                $data = $response->json();
                
                if (isset($data['choices'][0]['message']['content'])) {
                    return [
                        'success' => true,
                        'content' => trim($data['choices'][0]['message']['content']),
                        'usage' => $data['usage'] ?? null,
                    ];
                }
                
                return ['success' => false, 'error' => 'Invalid response format'];
            }

            return [
                'success' => false,
                'error' => 'API request failed: ' . $response->body(),
                'status' => $response->status()
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Request failed: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Generate invoice description using Groq
     */
    public function generateInvoiceDescription(array $context): array
    {
        $prompt = $this->buildInvoiceDescriptionPrompt($context);
        
        return $this->generateCompletion($prompt, [
            'max_tokens' => 200,
            'temperature' => 0.7,
        ]);
    }

    /**
     * Generate follow-up message using Groq
     */
    public function generateFollowUpMessage(array $context): array
    {
        $prompt = $this->buildFollowUpPrompt($context);
        
        return $this->generateCompletion($prompt, [
            'max_tokens' => 300,
            'temperature' => 0.8,
        ]);
    }

    /**
     * Generate contract recommendations using Groq
     */
    public function generateContractRecommendations(array $context): array
    {
        $prompt = $this->buildContractPrompt($context);
        
        return $this->generateCompletion($prompt, [
            'max_tokens' => 500,
            'temperature' => 0.6,
        ]);
    }

    /**
     * Generate business insights using Groq
     */
    public function generateBusinessInsights(array $data): array
    {
        $prompt = $this->buildBusinessInsightsPrompt($data);
        
        return $this->generateCompletion($prompt, [
            'max_tokens' => 600,
            'temperature' => 0.7,
        ]);
    }

    /**
     * Build invoice description prompt
     */
    private function buildInvoiceDescriptionPrompt(array $context): string
    {
        return "Generate a professional invoice description for the following context:\n" .
               "Client: {$context['client_name']}\n" .
               "Service Type: {$context['service_type']}\n" .
               "Project Details: {$context['project_details']}\n" .
               "Duration: {$context['duration']}\n\n" .
               "Create a clear, professional description that explains the work completed. " .
               "Keep it concise but informative (2-3 sentences).";
    }

    /**
     * Build follow-up message prompt
     */
    private function buildFollowUpPrompt(array $context): string
    {
        return "Generate a professional follow-up message for an overdue invoice:\n" .
               "Client Name: {$context['client_name']}\n" .
               "Invoice Number: {$context['invoice_number']}\n" .
               "Amount: ₹{$context['amount']}\n" .
               "Days Overdue: {$context['days_overdue']}\n" .
               "Relationship: {$context['relationship']}\n" .
               "Type: {$context['type']}\n\n" .
               "Create a polite but firm message appropriate for the relationship and overdue period. " .
               "Keep it professional and include a clear call to action.";
    }

    /**
     * Build contract recommendations prompt
     */
    private function buildContractPrompt(array $context): string
    {
        return "Generate contract recommendations for a freelance project:\n" .
               "Project Type: {$context['project_type']}\n" .
               "Duration: {$context['duration']}\n" .
               "Budget Range: {$context['budget_range']}\n" .
               "Client Type: {$context['client_type']}\n\n" .
               "Provide key contract clauses and terms that should be included. " .
               "Focus on payment terms, deliverables, and protection clauses. " .
               "Format as bullet points for easy reading.";
    }

    /**
     * Build business insights prompt
     */
    private function buildBusinessInsightsPrompt(array $data): string
    {
        return "Analyze the following business data and provide actionable insights:\n" .
               "Total Revenue: ₹{$data['total_revenue']}\n" .
               "Client Count: {$data['client_count']}\n" .
               "Average Invoice Value: ₹{$data['avg_invoice_value']}\n" .
               "Payment Delays: {$data['avg_payment_delay']} days\n" .
               "Growth Rate: {$data['growth_rate']}%\n\n" .
               "Provide 3-4 specific, actionable business insights and recommendations. " .
               "Focus on revenue optimization, client management, and growth opportunities. " .
               "Keep insights practical and implementable.";
    }

    /**
     * Test API connection
     */
    public function testConnection(): array
    {
        return $this->generateCompletion("Say 'Hello from Groq!' to test the connection.", [
            'max_tokens' => 50,
            'temperature' => 0.1,
        ]);
    }
}
