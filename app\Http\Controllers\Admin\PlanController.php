<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Plan;
use App\Models\PlanFeature;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PlanController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $plans = Plan::with('planFeatures')->ordered()->get();
        return view('admin.plans.index', compact('plans'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.plans.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'currency' => 'required|string|max:3',
            'billing_cycle' => 'required|in:monthly,yearly',
            'sort_order' => 'required|integer|min:0',
            'is_popular' => 'boolean',
            'is_active' => 'boolean',
            'features' => 'nullable|array',
            'plan_features' => 'nullable|array',
            'plan_features.*.feature_key' => 'required|string',
            'plan_features.*.feature_value' => 'required|string',
            'plan_features.*.feature_type' => 'required|in:limit,boolean,text',
        ]);

        DB::transaction(function () use ($validated) {
            $validated['slug'] = Str::slug($validated['name']);
            $validated['is_popular'] = $validated['is_popular'] ?? false;
            $validated['is_active'] = $validated['is_active'] ?? true;

            $plan = Plan::create($validated);

            // Create plan features
            if (!empty($validated['plan_features'])) {
                foreach ($validated['plan_features'] as $feature) {
                    $plan->planFeatures()->create($feature);
                }
            }
        });

        return redirect()->route('admin.plans.index')
                        ->with('success', 'Plan created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Plan $plan)
    {
        $plan->load('planFeatures', 'userSubscriptions.user');
        return view('admin.plans.show', compact('plan'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Plan $plan)
    {
        $plan->load('planFeatures');
        return view('admin.plans.edit', compact('plan'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Plan $plan)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'price' => 'required|numeric|min:0',
            'currency' => 'required|string|max:3',
            'billing_cycle' => 'required|in:monthly,yearly',
            'sort_order' => 'required|integer|min:0',
            'is_popular' => 'boolean',
            'is_active' => 'boolean',
            'features' => 'nullable|array',
            'plan_features' => 'nullable|array',
            'plan_features.*.feature_key' => 'required|string',
            'plan_features.*.feature_value' => 'required|string',
            'plan_features.*.feature_type' => 'required|in:limit,boolean,text',
        ]);

        DB::transaction(function () use ($validated, $plan) {
            $validated['slug'] = Str::slug($validated['name']);
            $validated['is_popular'] = $validated['is_popular'] ?? false;
            $validated['is_active'] = $validated['is_active'] ?? true;

            $plan->update($validated);

            // Delete existing features and recreate
            $plan->planFeatures()->delete();

            if (!empty($validated['plan_features'])) {
                foreach ($validated['plan_features'] as $feature) {
                    $plan->planFeatures()->create($feature);
                }
            }
        });

        return redirect()->route('admin.plans.index')
                        ->with('success', 'Plan updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Plan $plan)
    {
        if ($plan->userSubscriptions()->exists()) {
            return redirect()->back()
                           ->with('error', 'Cannot delete plan with active subscriptions.');
        }

        $plan->delete();

        return redirect()->route('admin.plans.index')
                        ->with('success', 'Plan deleted successfully.');
    }

    /**
     * Toggle plan status.
     */
    public function toggleStatus(Plan $plan)
    {
        $plan->update(['is_active' => !$plan->is_active]);

        $status = $plan->is_active ? 'activated' : 'deactivated';
        return redirect()->back()
                        ->with('success', "Plan {$status} successfully.");
    }
}
