<?php if (isset($component)) { $__componentOriginal49728f76f6574eefb81a3aaa880242ed = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal49728f76f6574eefb81a3aaa880242ed = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.frontend-layout','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('frontend-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('title', null, []); ?> Freeligo - Run Your Freelance Business Like a Pro <?php $__env->endSlot(); ?>
     <?php $__env->slot('description', null, []); ?> Professional freelance management platform with invoicing, contracts, client management, and AI-powered document generation. Streamline your freelance business today. <?php $__env->endSlot(); ?>
<!-- Hero Section -->
<section class="bg-gradient-to-br from-slate-50 via-emerald-50 to-teal-50 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
                <h1 class="text-4xl md:text-5xl lg:text-6xl font-heading font-bold text-gray-800 leading-tight mb-6">
                    Run Your Freelance Business
                    <span class="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600">Like a Pro</span>
                </h1>
                <p class="text-xl text-gray-600 mb-8 leading-relaxed">
                    Professional invoicing, contract management, and client relationships made simple.
                    Powered by AI to help you create smarter documents and make better business decisions.
                </p>
                <div class="flex flex-col sm:flex-row gap-4">
                    <a href="<?php echo e(route('register')); ?>" class="bg-gradient-to-r from-emerald-600 to-teal-600 text-white px-8 py-4 rounded-xl font-semibold hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 transform hover:scale-105 text-center shadow-lg">
                        Create Free Account
                    </a>
                    <a href="<?php echo e(route('pricing')); ?>" class="border-2 border-emerald-300 text-emerald-700 px-8 py-4 rounded-xl font-semibold hover:bg-emerald-50 transition-all duration-300 text-center">
                        View Pricing
                    </a>
                </div>
                <p class="text-sm text-gray-500 mt-4">No credit card required • Free 14-day trial</p>
            </div>
            <div class="relative">
                <div class="bg-white rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-300 border border-emerald-100">
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <h3 class="font-semibold text-gray-800">Invoice #INV-001</h3>
                            <span class="bg-emerald-100 text-emerald-700 px-3 py-1 rounded-full text-sm font-medium">Paid</span>
                        </div>
                        <div class="space-y-2">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Web Development</span>
                                <span class="font-semibold">$2,500</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">UI/UX Design</span>
                                <span class="font-semibold">$1,200</span>
                            </div>
                        </div>
                        <div class="border-t border-emerald-100 pt-2">
                            <div class="flex justify-between font-bold text-lg">
                                <span>Total</span>
                                <span class="text-emerald-600">$3,700</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="absolute -top-4 -right-4 w-24 h-24 bg-emerald-100 rounded-full opacity-30"></div>
                <div class="absolute -bottom-6 -left-6 w-32 h-32 bg-teal-100 rounded-full opacity-30"></div>
            </div>
        </div>
    </div>
</section>

<!-- Benefits Section -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-heading font-bold text-gray-800 mb-4">
                Everything You Need to Run Your Business
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Professional tools designed specifically for freelancers to manage clients, finances, and grow their business.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-12">
            <!-- Branded Invoices -->
            <div class="flex flex-col lg:flex-row items-center gap-8">
                <div class="lg:w-1/2">
                    <div class="bg-gradient-to-br from-emerald-50 to-teal-50 p-8 rounded-2xl">
                        <div class="w-16 h-16 bg-gradient-to-br from-emerald-500 to-teal-500 rounded-2xl flex items-center justify-center mb-6">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-heading font-bold text-gray-800 mb-4">Branded Invoices</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Create stunning, professional invoices with your brand colors, logo, and custom templates.
                            Automated calculations, tax handling, and multiple currency support included.
                        </p>
                        <ul class="space-y-3 text-gray-600">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Custom branding & templates
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Automated tax calculations
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Payment status tracking
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="lg:w-1/2">
                    <div class="bg-white rounded-2xl shadow-2xl p-6 border border-emerald-100 transform rotate-2 hover:rotate-0 transition-transform duration-300">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center">
                                    <span class="text-white font-bold text-sm">FL</span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-800">Freeligo</h4>
                                    <p class="text-xs text-gray-500">Professional Services</p>
                                </div>
                            </div>
                            <span class="bg-emerald-100 text-emerald-700 px-3 py-1 rounded-full text-sm font-medium">Paid</span>
                        </div>
                        <div class="border-t border-gray-100 pt-4">
                            <h5 class="font-semibold text-gray-800 mb-3">Invoice #INV-2024-001</h5>
                            <div class="space-y-2">
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">Web Development</span>
                                    <span class="font-medium">$2,500.00</span>
                                </div>
                                <div class="flex justify-between text-sm">
                                    <span class="text-gray-600">UI/UX Design</span>
                                    <span class="font-medium">$1,200.00</span>
                                </div>
                                <div class="border-t border-gray-100 pt-2 mt-3">
                                    <div class="flex justify-between font-bold text-lg">
                                        <span>Total</span>
                                        <span class="text-emerald-600">$3,700.00</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Auto Contracts -->
            <div class="flex flex-col lg:flex-row-reverse items-center gap-8">
                <div class="lg:w-1/2">
                    <div class="bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-2xl">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-500 rounded-2xl flex items-center justify-center mb-6">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-heading font-bold text-gray-800 mb-4">Auto Contracts</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Generate professional contracts automatically with smart templates, digital signatures,
                            and automated reminders for both parties.
                        </p>
                        <ul class="space-y-3 text-gray-600">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Smart contract templates
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Digital signatures
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Automated reminders
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="lg:w-1/2">
                    <div class="bg-white rounded-2xl shadow-2xl p-6 border border-blue-100 transform -rotate-2 hover:rotate-0 transition-transform duration-300">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="font-bold text-gray-800">Service Agreement</h4>
                            <span class="bg-green-100 text-green-700 px-3 py-1 rounded-full text-sm font-medium">Signed</span>
                        </div>
                        <div class="space-y-3 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Project:</span>
                                <span class="font-medium">E-commerce Website</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Duration:</span>
                                <span class="font-medium">6 weeks</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Value:</span>
                                <span class="font-medium">$5,000</span>
                            </div>
                            <div class="border-t border-gray-100 pt-3 mt-4">
                                <div class="flex items-center space-x-2">
                                    <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <span class="text-gray-600 text-xs">Digitally signed by both parties</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TDS Tracker -->
            <div class="flex flex-col lg:flex-row items-center gap-8">
                <div class="lg:w-1/2">
                    <div class="bg-gradient-to-br from-purple-50 to-pink-50 p-8 rounded-2xl">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center mb-6">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-heading font-bold text-gray-800 mb-4">TDS Tracker</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Automatically track Tax Deducted at Source (TDS) for all your clients. Generate TDS certificates,
                            monitor quarterly filings, and stay compliant with tax regulations.
                        </p>
                        <ul class="space-y-3 text-gray-600">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-purple-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Automatic TDS calculations
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-purple-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                TDS certificate generation
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-purple-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Quarterly filing reminders
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="lg:w-1/2">
                    <div class="bg-white rounded-2xl shadow-2xl p-6 border border-purple-100 transform rotate-1 hover:rotate-0 transition-transform duration-300">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="font-bold text-gray-800">TDS Summary</h4>
                            <span class="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-medium">Q4 2024</span>
                        </div>
                        <div class="space-y-3">
                            <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                                <div>
                                    <p class="font-medium text-gray-800">Total TDS Deducted</p>
                                    <p class="text-sm text-gray-600">Across all clients</p>
                                </div>
                                <span class="text-xl font-bold text-purple-600">₹45,000</span>
                            </div>
                            <div class="grid grid-cols-2 gap-3">
                                <div class="p-3 bg-green-50 rounded-lg text-center">
                                    <p class="text-sm text-gray-600">Certificates</p>
                                    <p class="text-lg font-bold text-green-600">12</p>
                                </div>
                                <div class="p-3 bg-orange-50 rounded-lg text-center">
                                    <p class="text-sm text-gray-600">Pending</p>
                                    <p class="text-lg font-bold text-orange-600">2</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Follow-ups -->
            <div class="flex flex-col lg:flex-row-reverse items-center gap-8">
                <div class="lg:w-1/2">
                    <div class="bg-gradient-to-br from-orange-50 to-red-50 p-8 rounded-2xl">
                        <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl flex items-center justify-center mb-6">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-2xl font-heading font-bold text-gray-800 mb-4">Payment Follow-ups</h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            Never chase payments manually again. Automated follow-up sequences with customizable
                            templates ensure you get paid on time, every time.
                        </p>
                        <ul class="space-y-3 text-gray-600">
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-orange-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Automated reminder sequences
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-orange-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Customizable templates
                            </li>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-orange-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Payment status tracking
                            </li>
                        </ul>
                    </div>
                </div>
                <div class="lg:w-1/2">
                    <div class="bg-white rounded-2xl shadow-2xl p-6 border border-orange-100 transform -rotate-1 hover:rotate-0 transition-transform duration-300">
                        <div class="flex items-center justify-between mb-4">
                            <h4 class="font-bold text-gray-800">Follow-up Schedule</h4>
                            <span class="bg-orange-100 text-orange-700 px-3 py-1 rounded-full text-sm font-medium">Active</span>
                        </div>
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3 p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                                <div class="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-xs">1</span>
                                </div>
                                <div class="flex-1">
                                    <p class="font-medium text-gray-800">Gentle Reminder</p>
                                    <p class="text-sm text-gray-600">3 days after due date</p>
                                </div>
                                <span class="text-xs text-yellow-600 font-medium">Sent</span>
                            </div>
                            <div class="flex items-center space-x-3 p-3 bg-orange-50 rounded-lg border-l-4 border-orange-400">
                                <div class="w-8 h-8 bg-orange-400 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-xs">2</span>
                                </div>
                                <div class="flex-1">
                                    <p class="font-medium text-gray-800">Firm Reminder</p>
                                    <p class="text-sm text-gray-600">7 days after due date</p>
                                </div>
                                <span class="text-xs text-orange-600 font-medium">Pending</span>
                            </div>
                            <div class="flex items-center space-x-3 p-3 bg-red-50 rounded-lg border-l-4 border-red-400">
                                <div class="w-8 h-8 bg-red-400 rounded-full flex items-center justify-center">
                                    <span class="text-white font-bold text-xs">3</span>
                                </div>
                                <div class="flex-1">
                                    <p class="font-medium text-gray-800">Final Notice</p>
                                    <p class="text-sm text-gray-600">14 days after due date</p>
                                </div>
                                <span class="text-xs text-gray-400 font-medium">Scheduled</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Testimonials Section -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-heading font-bold text-gray-800 mb-4">
                Trusted by <span class="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600">Thousands</span> of Freelancers
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                See how Freeligo has transformed businesses and helped freelancers achieve their goals.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Testimonial 1 -->
            <div class="bg-white p-8 rounded-2xl border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-gray-600 mb-6 leading-relaxed">
                    "Freeligo completely transformed how I manage my freelance business. The automated invoicing and payment follow-ups have saved me hours every week, and I never miss a payment anymore."
                </p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-emerald-400 to-teal-400 rounded-full flex items-center justify-center mr-4">
                        <span class="text-white font-bold">SP</span>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-800">Sarah Parker</h4>
                        <p class="text-gray-600 text-sm">Web Developer</p>
                    </div>
                </div>
            </div>

            <!-- Testimonial 2 -->
            <div class="bg-white p-8 rounded-2xl border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-gray-600 mb-6 leading-relaxed">
                    "The TDS tracker is a game-changer! As a freelancer in India, managing tax compliance was always a headache. Now everything is automated and I never miss a filing deadline."
                </p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-indigo-400 rounded-full flex items-center justify-center mr-4">
                        <span class="text-white font-bold">RK</span>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-800">Raj Kumar</h4>
                        <p class="text-gray-600 text-sm">Digital Marketer</p>
                    </div>
                </div>
            </div>

            <!-- Testimonial 3 -->
            <div class="bg-white p-8 rounded-2xl border border-gray-100 shadow-lg hover:shadow-xl transition-all duration-300">
                <div class="flex items-center mb-4">
                    <div class="flex text-yellow-400">
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                    </div>
                </div>
                <p class="text-gray-600 mb-6 leading-relaxed">
                    "The contract automation feature is incredible. I can generate professional contracts in minutes, and the digital signature process makes everything so smooth for my clients."
                </p>
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-pink-400 rounded-full flex items-center justify-center mr-4">
                        <span class="text-white font-bold">MJ</span>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-800">Maria Johnson</h4>
                        <p class="text-gray-600 text-sm">Graphic Designer</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="text-center mt-12">
            <p class="text-gray-600 mb-6">Join thousands of satisfied freelancers who trust Freeligo</p>
            <div class="flex justify-center items-center space-x-8 text-gray-400">
                <div class="flex items-center">
                    <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="font-medium">5,000+ Active Users</span>
                </div>
                <div class="flex items-center">
                    <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="font-medium">₹50M+ Invoiced</span>
                </div>
                <div class="flex items-center">
                    <svg class="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="font-medium">4.9/5 Rating</span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Pricing Section -->
<section class="py-20 bg-gradient-to-br from-slate-50 via-emerald-50 to-teal-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-heading font-bold text-gray-800 mb-4">
                Simple, Transparent <span class="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600">Pricing</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose the plan that fits your business needs. Start with our free trial and upgrade as you grow.
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            <?php $__currentLoopData = $plans; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $plan): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-2xl border <?php echo e($plan->is_popular ? 'border-2 border-emerald-500' : 'border border-gray-200'); ?> p-8 hover:shadow-xl transition-all duration-300 relative <?php echo e($plan->is_popular ? 'transform scale-105' : ''); ?>">
                <?php if($plan->is_popular): ?>
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span class="bg-emerald-500 text-white px-4 py-2 rounded-full text-sm font-medium">Most Popular</span>
                </div>
                <?php endif; ?>

                <div class="text-center">
                    <h3 class="text-2xl font-heading font-bold text-gray-900 mb-2"><?php echo e($plan->name); ?></h3>
                    <p class="text-gray-600 mb-6"><?php echo e($plan->description); ?></p>
                    <div class="mb-6">
                        <span class="text-4xl font-bold <?php echo e($plan->is_popular ? 'text-emerald-600' : ($plan->price == 0 ? 'text-emerald-600' : 'text-gray-900')); ?>">
                            <?php echo e($plan->formatted_price); ?>

                        </span>
                        <span class="text-gray-600">/<?php echo e($plan->billing_cycle); ?></span>
                    </div>

                    <?php if($plan->slug === 'free'): ?>
                        <a href="<?php echo e(route('register')); ?>" class="w-full bg-emerald-600 text-white py-3 px-6 rounded-xl font-semibold hover:bg-emerald-700 transition-colors block text-center mb-6">
                            Get Started Free
                        </a>
                    <?php elseif($plan->slug === 'pro'): ?>
                        <a href="<?php echo e(route('register')); ?>" class="w-full bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 block text-center mb-6">
                            Start Free Trial
                        </a>
                    <?php else: ?>
                        <a href="<?php echo e(route('contact')); ?>" class="w-full bg-gray-900 text-white py-3 px-6 rounded-xl font-semibold hover:bg-gray-800 transition-colors block text-center mb-6">
                            Contact Sales
                        </a>
                    <?php endif; ?>
                </div>

                <ul class="space-y-4">
                    <?php $__currentLoopData = $plan->planFeatures; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php if($feature->feature_type === 'boolean' && $feature->getBooleanValue()): ?>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-600"><?php echo e($feature->feature_name); ?></span>
                            </li>
                        <?php elseif($feature->feature_type === 'limit'): ?>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-600">
                                    <?php echo e($feature->feature_name); ?>:
                                    <?php if($feature->feature_value === 'unlimited'): ?>
                                        Unlimited
                                    <?php else: ?>
                                        <?php echo e($feature->feature_value); ?>

                                    <?php endif; ?>
                                </span>
                            </li>
                        <?php elseif($feature->feature_type === 'text'): ?>
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-600"><?php echo e($feature->feature_value); ?></span>
                            </li>
                        <?php endif; ?>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </ul>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="text-center mt-12">
            <p class="text-gray-600 mb-4">Start free forever • Upgrade anytime • No credit card required</p>
            <a href="<?php echo e(route('pricing')); ?>" class="text-emerald-600 hover:text-emerald-700 font-medium">
                View detailed pricing comparison →
            </a>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="bg-gradient-to-r from-emerald-600 via-teal-600 to-slate-700 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-heading font-bold text-white mb-4">
            Ready to Transform Your Freelance Business?
        </h2>
        <p class="text-xl text-emerald-100 mb-8 max-w-2xl mx-auto">
            Join thousands of freelancers who have streamlined their business operations with Freeligo.
        </p>
        <a href="<?php echo e(route('register')); ?>" class="bg-white text-emerald-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 inline-block shadow-lg">
            Create Free Account
        </a>
        <p class="text-emerald-100 text-sm mt-4">No credit card required • 14-day free trial</p>
    </div>
</section>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal49728f76f6574eefb81a3aaa880242ed)): ?>
<?php $attributes = $__attributesOriginal49728f76f6574eefb81a3aaa880242ed; ?>
<?php unset($__attributesOriginal49728f76f6574eefb81a3aaa880242ed); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal49728f76f6574eefb81a3aaa880242ed)): ?>
<?php $component = $__componentOriginal49728f76f6574eefb81a3aaa880242ed; ?>
<?php unset($__componentOriginal49728f76f6574eefb81a3aaa880242ed); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\freeligo\resources\views/frontend/home.blade.php ENDPATH**/ ?>