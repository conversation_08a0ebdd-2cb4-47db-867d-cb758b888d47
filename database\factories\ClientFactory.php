<?php

namespace Database\Factories;

use App\Models\Client;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ClientFactory extends Factory
{
    protected $model = Client::class;

    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'phone' => fake()->phoneNumber(),
            'company_name' => fake()->company(),
            'address' => fake()->address(),
            'gst_number' => fake()->regexify('[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}'),
            'contact_person' => fake()->name(),
            'default_tds_percentage' => fake()->randomFloat(2, 0, 10),
        ];
    }

    public function withoutGst(): static
    {
        return $this->state(fn (array $attributes) => [
            'gst_number' => null,
        ]);
    }

    public function withoutTds(): static
    {
        return $this->state(fn (array $attributes) => [
            'default_tds_percentage' => 0,
        ]);
    }

    public function forUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }
}
