<?php

namespace App\Services;

use App\Models\RecurringInvoice;
use App\Models\Invoice;
use App\Models\FollowUp;
use App\Services\AIServiceFactory;
use App\Services\InvoiceService;
use App\Services\NotificationService;
use App\Helpers\PlanChecker;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class RecurringInvoiceService
{
    protected $aiService;
    protected $invoiceService;
    protected $notificationService;

    public function __construct(InvoiceService $invoiceService, NotificationService $notificationService)
    {
        $this->aiService = AIServiceFactory::create();
        $this->invoiceService = $invoiceService;
        $this->notificationService = $notificationService;
    }

    /**
     * Generate invoices for all ready recurring invoices with enhanced automation.
     */
    public function generateScheduledInvoices(): array
    {
        $results = [
            'generated' => 0,
            'failed' => 0,
            'auto_sent' => 0,
            'retried' => 0,
            'paused' => 0,
            'errors' => []
        ];

        // Get ready invoices including those that need retry
        $recurringInvoices = $this->getInvoicesReadyForProcessing();

        foreach ($recurringInvoices as $recurringInvoice) {
            try {
                // Check if this is a retry attempt
                $isRetry = $recurringInvoice->consecutive_failures > 0;

                $invoice = $this->generateInvoiceFromRecurring($recurringInvoice);
                $recurringInvoice->recordSuccess();
                $results['generated']++;

                if ($isRetry) {
                    $results['retried']++;
                }

                // Auto-send if enabled (dispatch job)
                if ($recurringInvoice->auto_send && $invoice) {
                    \App\Jobs\ProcessInvoiceAutoSendJob::dispatch($invoice->id)
                        ->onQueue('high')
                        ->delay(now()->addMinutes(2));
                    $results['auto_sent']++;
                }

                // Schedule smart reminders (dispatch job)
                $this->scheduleAutomaticReminders($invoice);

                // Update client behavior analysis (dispatch job)
                \App\Jobs\ProcessClientAnalyticsJob::dispatch($recurringInvoice->client_id, [
                    'event' => 'recurring_invoice_generated',
                    'invoice_id' => $invoice->id,
                    'recurring_invoice_id' => $recurringInvoice->id
                ])->onQueue('analytics')->delay(now()->addMinutes(3));

                // Adjust frequency based on client behavior
                $this->adjustFrequencyBasedOnBehavior($recurringInvoice);

            } catch (\Exception $e) {
                $recurringInvoice->recordFailure($e->getMessage());
                $results['failed']++;

                if ($recurringInvoice->status === 'paused') {
                    $results['paused']++;
                }

                $results['errors'][] = [
                    'recurring_id' => $recurringInvoice->id,
                    'error' => $e->getMessage(),
                    'consecutive_failures' => $recurringInvoice->consecutive_failures,
                    'will_retry' => $recurringInvoice->shouldRetry()
                ];

                Log::error('Failed to generate recurring invoice', [
                    'recurring_id' => $recurringInvoice->id,
                    'error' => $e->getMessage(),
                    'consecutive_failures' => $recurringInvoice->consecutive_failures
                ]);
            }
        }

        return $results;
    }

    /**
     * Generate a single invoice from a recurring invoice.
     */
    public function generateInvoiceFromRecurring(RecurringInvoice $recurringInvoice): Invoice
    {
        return DB::transaction(function () use ($recurringInvoice) {
            $templateData = $recurringInvoice->template_data;
            
            // Generate AI-enhanced description if enabled
            if (PlanChecker::canUseAiAssistant()) {
                $templateData['notes'] = $this->generateAIDescription($recurringInvoice);
            }

            // Create the invoice
            $invoice = Invoice::create([
                'user_id' => $recurringInvoice->user_id,
                'client_id' => $recurringInvoice->client_id,
                'recurring_invoice_id' => $recurringInvoice->id,
                'invoice_number' => $this->generateInvoiceNumber($recurringInvoice->user_id),
                'issue_date' => now(),
                'due_date' => now()->addDays($templateData['payment_terms'] ?? 30),
                'subtotal' => $templateData['subtotal'],
                'tax_rate' => $templateData['tax_rate'] ?? 0,
                'tax_amount' => $templateData['tax_amount'] ?? 0,
                'tds_rate' => $templateData['tds_rate'] ?? 0,
                'tds_amount' => $templateData['tds_amount'] ?? 0,
                'total_amount' => $templateData['total_amount'],
                'net_amount' => $templateData['net_amount'],
                'currency' => $templateData['currency'] ?? 'INR',
                'notes' => $templateData['notes'] ?? '',
                'status' => $recurringInvoice->auto_send ? 'sent' : 'draft',
            ]);

            // Create invoice items
            if (isset($templateData['items'])) {
                foreach ($templateData['items'] as $item) {
                    $invoice->items()->create($item);
                }
            }

            // Update recurring invoice
            $recurringInvoice->increment('total_generated');
            $recurringInvoice->update(['last_generated_at' => now()]);
            $recurringInvoice->updateNextGenerationDate();

            return $invoice;
        });
    }



    /**
     * Generate unique invoice number.
     */
    protected function generateInvoiceNumber(int $userId): string
    {
        $prefix = 'INV-' . date('Y') . '-';
        $lastInvoice = Invoice::where('user_id', $userId)
            ->where('invoice_number', 'like', $prefix . '%')
            ->orderBy('invoice_number', 'desc')
            ->first();

        if ($lastInvoice) {
            $lastNumber = (int) substr($lastInvoice->invoice_number, strlen($prefix));
            $newNumber = $lastNumber + 1;
        } else {
            $newNumber = 1;
        }

        return $prefix . str_pad($newNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Create a new recurring invoice.
     */
    public function createRecurringInvoice(array $data): RecurringInvoice
    {
        $data['next_generation_date'] = $data['start_date'];
        $data['status'] = 'active';
        $data['total_generated'] = 0;

        return RecurringInvoice::create($data);
    }

    /**
     * Update recurring invoice template.
     */
    public function updateRecurringInvoice(RecurringInvoice $recurringInvoice, array $data): bool
    {
        return $recurringInvoice->update($data);
    }

    /**
     * Pause a recurring invoice.
     */
    public function pauseRecurringInvoice(RecurringInvoice $recurringInvoice): bool
    {
        return $recurringInvoice->update(['status' => 'paused']);
    }

    /**
     * Resume a recurring invoice.
     */
    public function resumeRecurringInvoice(RecurringInvoice $recurringInvoice): bool
    {
        $recurringInvoice->update([
            'status' => 'active',
            'next_generation_date' => $recurringInvoice->calculateNextGenerationDate()
        ]);

        return true;
    }

    /**
     * Cancel a recurring invoice.
     */
    public function cancelRecurringInvoice(RecurringInvoice $recurringInvoice): bool
    {
        return $recurringInvoice->update(['status' => 'cancelled']);
    }

    /**
     * Auto-send invoice via email.
     */
    protected function autoSendInvoice(Invoice $invoice): void
    {
        try {
            // Generate PDF if not exists
            if (!$invoice->pdf_path) {
                $this->invoiceService->generatePdf($invoice);
            }

            // Send email notification
            $this->notificationService->sendInvoiceEmail($invoice);

            // Update invoice status
            $invoice->update(['status' => 'sent']);

        } catch (\Exception $e) {
            Log::error('Failed to auto-send invoice', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Schedule automatic reminders for invoice.
     */
    protected function scheduleAutomaticReminders(Invoice $invoice): void
    {
        // Dispatch job to create reminder workflow
        \App\Jobs\ProcessSmartRemindersJob::dispatch($invoice->id)
            ->onQueue('default')
            ->delay(now()->addMinutes(5));
    }

    /**
     * Dispatch queue-based processing for all recurring invoices.
     */
    public function dispatchQueueProcessing(array $options = []): void
    {
        \App\Jobs\ProcessRecurringInvoicesJob::dispatch(null, $options)
            ->onQueue($options['queue'] ?? 'default');
    }

    /**
     * Dispatch queue-based processing for specific recurring invoice.
     */
    public function dispatchSingleProcessing(int $recurringInvoiceId, array $options = []): void
    {
        \App\Jobs\ProcessRecurringInvoicesJob::dispatch($recurringInvoiceId, $options)
            ->onQueue($options['queue'] ?? 'high');
    }

    /**
     * Analyze client payment behavior.
     */
    protected function analyzeClientPaymentBehavior($client): array
    {
        $paidInvoices = $client->invoices()
            ->where('status', 'paid')
            ->whereNotNull('paid_date')
            ->get();

        if ($paidInvoices->isEmpty()) {
            return ['avg_delay' => 0, 'reliability_score' => 50];
        }

        $delays = $paidInvoices->map(function ($invoice) {
            return Carbon::parse($invoice->paid_date)->diffInDays($invoice->due_date);
        });

        return [
            'avg_delay' => $delays->avg(),
            'reliability_score' => $this->calculateReliabilityScore($delays),
            'total_invoices' => $paidInvoices->count()
        ];
    }

    /**
     * Calculate reliability score based on payment delays.
     */
    protected function calculateReliabilityScore($delays): int
    {
        $onTimePayments = $delays->filter(fn($delay) => $delay <= 3)->count();
        $totalPayments = $delays->count();

        return $totalPayments > 0 ? (int) (($onTimePayments / $totalPayments) * 100) : 50;
    }

    /**
     * Create frequency adjustment suggestion.
     */
    protected function createFrequencyAdjustmentSuggestion(RecurringInvoice $recurringInvoice, array $paymentHistory): void
    {
        // This could be stored in a suggestions table or sent as notification
        Log::info('Frequency adjustment suggested', [
            'recurring_id' => $recurringInvoice->id,
            'current_frequency' => $recurringInvoice->frequency,
            'suggested_frequency' => 'quarterly',
            'reason' => 'Client payment delay average: ' . $paymentHistory['avg_delay'] . ' days'
        ]);
    }

    /**
     * Generate reminder message for scheduled follow-ups.
     */
    protected function generateReminderMessage(Invoice $invoice, string $type): string
    {
        $templates = [
            'gentle' => "Hi {client_name}, just a gentle reminder that invoice #{invoice_number} for ₹{amount} is due on {due_date}. Thank you!",
            'reminder' => "Dear {client_name}, invoice #{invoice_number} for ₹{amount} was due on {due_date}. Please process payment at your earliest convenience.",
            'urgent' => "Dear {client_name}, invoice #{invoice_number} for ₹{amount} is now overdue. Please arrange payment immediately to avoid any service disruption.",
            'final_notice' => "Dear {client_name}, this is a final notice for overdue invoice #{invoice_number} for ₹{amount}. Please contact us immediately to resolve this matter."
        ];

        $template = $templates[$type] ?? $templates['reminder'];

        return str_replace(
            ['{client_name}', '{invoice_number}', '{amount}', '{due_date}'],
            [$invoice->client->name, $invoice->invoice_number, $invoice->total_amount, $invoice->due_date->format('d M Y')],
            $template
        );
    }

    /**
     * Get invoices ready for processing including retries.
     */
    protected function getInvoicesReadyForProcessing()
    {
        return RecurringInvoice::where(function ($query) {
            // Regular ready invoices
            $query->where('status', 'active')
                  ->where('next_generation_date', '<=', now())
                  ->where(function ($q) {
                      $q->whereNull('end_date')
                        ->orWhere('end_date', '>=', now());
                  })
                  ->where(function ($q) {
                      $q->whereNull('max_occurrences')
                        ->orWhereRaw('total_generated < max_occurrences');
                  });
        })->orWhere(function ($query) {
            // Failed invoices ready for retry
            $query->where('status', 'active')
                  ->where('consecutive_failures', '>', 0)
                  ->where('consecutive_failures', '<', 5)
                  ->whereNotNull('last_failure_at');
        })->get()->filter(function ($recurringInvoice) {
            // Additional check for retry timing
            if ($recurringInvoice->consecutive_failures > 0) {
                return $recurringInvoice->shouldRetry();
            }
            return true;
        });
    }

    /**
     * Update client behavior analysis.
     */
    protected function updateClientBehaviorAnalysis(RecurringInvoice $recurringInvoice): void
    {
        $client = $recurringInvoice->client;
        $behaviorData = $this->analyzeClientPaymentBehavior($client);

        // Add additional metrics
        $behaviorData['last_analysis_date'] = now()->toISOString();
        $behaviorData['invoice_frequency'] = $recurringInvoice->frequency;
        $behaviorData['total_recurring_invoices'] = $client->recurringInvoices()->count();

        $recurringInvoice->updateClientBehaviorData($behaviorData);
    }

    /**
     * Enhanced frequency adjustment with AI insights.
     */
    protected function adjustFrequencyBasedOnBehavior(RecurringInvoice $recurringInvoice): void
    {
        if (!$recurringInvoice->dynamic_frequency_enabled) {
            return;
        }

        $client = $recurringInvoice->client;
        $behaviorData = $recurringInvoice->client_behavior_data ?? [];

        // Get AI recommendation if available
        if (PlanChecker::canUseAiAssistant() && !empty($behaviorData)) {
            $recommendation = $this->getAIFrequencyRecommendation($recurringInvoice, $behaviorData);

            if ($recommendation && $recommendation['frequency'] !== $recurringInvoice->frequency) {
                $recurringInvoice->adjustFrequency(
                    $recommendation['frequency'],
                    $recommendation['reason']
                );

                Log::info('AI-based frequency adjustment applied', [
                    'recurring_id' => $recurringInvoice->id,
                    'old_frequency' => $recurringInvoice->frequency,
                    'new_frequency' => $recommendation['frequency'],
                    'reason' => $recommendation['reason']
                ]);
            }
        } else {
            // Fallback to rule-based adjustment
            $this->applyRuleBasedFrequencyAdjustment($recurringInvoice, $behaviorData);
        }
    }

    /**
     * Get AI-powered frequency recommendation.
     */
    protected function getAIFrequencyRecommendation(RecurringInvoice $recurringInvoice, array $behaviorData): ?array
    {
        try {
            $context = [
                'current_frequency' => $recurringInvoice->frequency,
                'client_reliability_score' => $behaviorData['reliability_score'] ?? 50,
                'average_payment_delay' => $behaviorData['avg_delay'] ?? 0,
                'total_invoices' => $behaviorData['total_invoices'] ?? 0,
                'invoice_amount' => $recurringInvoice->template_data['total_amount'] ?? 0,
                'client_relationship_months' => $behaviorData['relationship_months'] ?? 0,
            ];

            $result = $this->aiService->generateFrequencyRecommendation($context);

            if ($result['success']) {
                return $result['recommendation'];
            }
        } catch (\Exception $e) {
            Log::warning('AI frequency recommendation failed', [
                'recurring_id' => $recurringInvoice->id,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }

    /**
     * Apply rule-based frequency adjustment.
     */
    protected function applyRuleBasedFrequencyAdjustment(RecurringInvoice $recurringInvoice, array $behaviorData): void
    {
        $avgDelay = $behaviorData['avg_delay'] ?? 0;
        $reliabilityScore = $behaviorData['reliability_score'] ?? 50;

        // If client consistently pays late and has low reliability
        if ($avgDelay > 15 && $reliabilityScore < 60 && $recurringInvoice->frequency === 'monthly') {
            $recurringInvoice->adjustFrequency(
                'quarterly',
                "Client payment delay average: {$avgDelay} days, reliability score: {$reliabilityScore}%"
            );
        }
        // If client is very reliable, consider more frequent billing
        elseif ($avgDelay < 3 && $reliabilityScore > 90 && $recurringInvoice->frequency === 'quarterly') {
            $recurringInvoice->adjustFrequency(
                'monthly',
                "Excellent payment history: {$avgDelay} days average delay, {$reliabilityScore}% reliability"
            );
        }
    }

    /**
     * Enhanced AI description generation with context.
     */
    protected function generateAIDescription(RecurringInvoice $recurringInvoice): string
    {
        if (!$recurringInvoice->ai_description_enabled) {
            return $recurringInvoice->template_data['notes'] ?? '';
        }

        try {
            $context = [
                'client_name' => $recurringInvoice->client->name,
                'frequency' => $recurringInvoice->frequency,
                'occurrence_number' => $recurringInvoice->total_generated + 1,
                'service_type' => $recurringInvoice->template_data['service_type'] ?? 'services',
                'previous_notes' => $recurringInvoice->template_data['notes'] ?? '',
                'client_behavior' => $recurringInvoice->client_behavior_data ?? [],
                'invoice_amount' => $recurringInvoice->template_data['total_amount'] ?? 0,
                'relationship_duration' => now()->diffInMonths($recurringInvoice->client->created_at),
            ];

            $result = $this->aiService->generateInvoiceDescription($context);

            if ($result['success']) {
                return $result['content'];
            }
        } catch (\Exception $e) {
            Log::warning('Failed to generate AI description for recurring invoice', [
                'recurring_id' => $recurringInvoice->id,
                'error' => $e->getMessage()
            ]);
        }

        return $recurringInvoice->template_data['notes'] ?? '';
    }
}
