<x-admin-layout>
    <x-slot name="header">
        <div class="md:flex md:items-center md:justify-between">
            <div class="flex-1 min-w-0">
                <h1 class="text-2xl font-bold leading-7 text-gray-900 sm:text-3xl sm:truncate">
                    Automation Monitoring
                </h1>
                <p class="mt-1 text-sm text-gray-500">
                    Monitor and manage all automation processes across the platform
                </p>
            </div>
            <div class="mt-4 flex md:mt-0 md:ml-4">
                <select id="period-selector" class="rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    <option value="24_hours" {{ $period === '24_hours' ? 'selected' : '' }}>Last 24 Hours</option>
                    <option value="7_days" {{ $period === '7_days' ? 'selected' : '' }}>Last 7 Days</option>
                    <option value="30_days" {{ $period === '30_days' ? 'selected' : '' }}>Last 30 Days</option>
                    <option value="90_days" {{ $period === '90_days' ? 'selected' : '' }}>Last 90 Days</option>
                </select>
            </div>
        </div>
    </x-slot>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Queue Health Status -->
        <div class="bg-white overflow-hidden shadow rounded-lg mb-8">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Queue Health Status</h3>
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="bg-green-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-green-800">Queue Status</p>
                                <p class="text-lg font-semibold text-green-900">
                                    {{ $queueHealth['status'] === 'healthy' ? 'Healthy' : 'Issues Detected' }}
                                </p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-clock text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-blue-800">Pending Jobs</p>
                                <p class="text-lg font-semibold text-blue-900">{{ number_format($queueStats['pending_jobs']) }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-yellow-800">Failed Jobs</p>
                                <p class="text-lg font-semibold text-yellow-900">{{ number_format($queueStats['failed_jobs']) }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-purple-50 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-tachometer-alt text-white text-sm"></i>
                                </div>
                            </div>
                            <div class="ml-3">
                                <p class="text-sm font-medium text-purple-800">Avg Processing Time</p>
                                <p class="text-lg font-semibold text-purple-900">{{ $queuePerformance['avg_processing_time'] }}s</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Automation Statistics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Recurring Invoices -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recurring Invoices</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Total Processed</span>
                            <span class="text-sm font-semibold">{{ number_format($automationStats['recurring_invoices']['total_processed']) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Successful</span>
                            <span class="text-sm font-semibold text-green-600">{{ number_format($automationStats['recurring_invoices']['successful']) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Failed</span>
                            <span class="text-sm font-semibold text-red-600">{{ number_format($automationStats['recurring_invoices']['failed']) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Reminders -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Payment Reminders</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Total Sent</span>
                            <span class="text-sm font-semibold">{{ number_format($automationStats['payment_reminders']['total_sent']) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Successful</span>
                            <span class="text-sm font-semibold text-green-600">{{ number_format($automationStats['payment_reminders']['successful']) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Pending</span>
                            <span class="text-sm font-semibold text-yellow-600">{{ number_format($automationStats['payment_reminders']['pending']) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- TDS Calculations -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">TDS Calculations</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Total Processed</span>
                            <span class="text-sm font-semibold">{{ number_format($automationStats['tds_calculations']['total_processed']) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Compliant</span>
                            <span class="text-sm font-semibold text-green-600">{{ number_format($automationStats['tds_calculations']['successful']) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Pending Review</span>
                            <span class="text-sm font-semibold text-yellow-600">{{ number_format($automationStats['tds_calculations']['pending_review']) }}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Workflow Executions -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Workflow Executions</h3>
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Total Executed</span>
                            <span class="text-sm font-semibold">{{ number_format($automationStats['workflow_executions']['total_executed']) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Successful</span>
                            <span class="text-sm font-semibold text-green-600">{{ number_format($automationStats['workflow_executions']['successful']) }}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-sm text-gray-600">Failed</span>
                            <span class="text-sm font-semibold text-red-600">{{ number_format($automationStats['workflow_executions']['failed']) }}</span>
                        </div>
                        <div class="mt-4 pt-3 border-t border-gray-200">
                            <div class="flex justify-between">
                                <span class="text-sm text-gray-600">Success Rate</span>
                                <span class="text-sm font-semibold text-blue-600">{{ $executionMetrics['success_rate'] }}%</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Section -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Execution Trends Chart -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Execution Trends</h3>
                    <div class="h-64">
                        <canvas id="executionTrendsChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Error Analysis Chart -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Error Types</h3>
                    <div class="h-64">
                        <canvas id="errorTypesChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Executions and Failed Jobs -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Recent Executions -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Recent Executions</h3>
                    <div class="flow-root">
                        <ul class="-my-5 divide-y divide-gray-200">
                            @forelse($recentExecutions as $execution)
                            <li class="py-4">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        @if($execution['status'] === 'completed')
                                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                                <i class="fas fa-check text-white text-xs"></i>
                                            </div>
                                        @elseif($execution['status'] === 'failed')
                                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                                <i class="fas fa-times text-white text-xs"></i>
                                            </div>
                                        @else
                                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                                <i class="fas fa-clock text-white text-xs"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {{ $execution['workflow']['name'] ?? 'Unknown Workflow' }}
                                        </p>
                                        <p class="text-sm text-gray-500">
                                            {{ $execution['user']['name'] ?? 'System' }} • 
                                            {{ \Carbon\Carbon::parse($execution['created_at'])->diffForHumans() }}
                                        </p>
                                    </div>
                                    <div class="flex-shrink-0 text-sm text-gray-500">
                                        {{ $execution['execution_time'] ? round($execution['execution_time'], 2) . 's' : '-' }}
                                    </div>
                                </div>
                            </li>
                            @empty
                            <li class="py-4 text-center text-gray-500">
                                No recent executions found
                            </li>
                            @endforelse
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Failed Jobs -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Failed Jobs</h3>
                        @if(count($failedJobs) > 0)
                        <div class="space-x-2">
                            <button id="retry-all-btn" class="text-sm bg-blue-600 text-white px-3 py-1 rounded hover:bg-blue-700">
                                Retry All
                            </button>
                            <button id="clear-all-btn" class="text-sm bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700">
                                Clear All
                            </button>
                        </div>
                        @endif
                    </div>
                    <div class="flow-root">
                        <ul class="-my-5 divide-y divide-gray-200">
                            @forelse($failedJobs as $job)
                            <li class="py-4">
                                <div class="flex items-center space-x-4">
                                    <div class="flex-shrink-0">
                                        <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                            <i class="fas fa-exclamation text-white text-xs"></i>
                                        </div>
                                    </div>
                                    <div class="flex-1 min-w-0">
                                        <p class="text-sm font-medium text-gray-900 truncate">
                                            {{ class_basename($job['payload']['displayName'] ?? 'Unknown Job') }}
                                        </p>
                                        <p class="text-sm text-gray-500 truncate">
                                            {{ Str::limit($job['exception'] ?? 'No error message', 60) }}
                                        </p>
                                        <p class="text-xs text-gray-400">
                                            Failed {{ \Carbon\Carbon::parse($job['failed_at'])->diffForHumans() }}
                                        </p>
                                    </div>
                                    <div class="flex-shrink-0 space-x-2">
                                        <button class="retry-job-btn text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700" 
                                                data-job-id="{{ $job['id'] }}">
                                            Retry
                                        </button>
                                        <button class="clear-job-btn text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700" 
                                                data-job-id="{{ $job['id'] }}">
                                            Clear
                                        </button>
                                    </div>
                                </div>
                            </li>
                            @empty
                            <li class="py-4 text-center text-gray-500">
                                No failed jobs found
                            </li>
                            @endforelse
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</x-admin-layout>

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Period selector change handler
    document.getElementById('period-selector').addEventListener('change', function() {
        const period = this.value;
        window.location.href = `{{ route('admin.automation.dashboard') }}?period=${period}`;
    });

    // Initialize charts
    initializeCharts();
    
    // Failed job management
    initializeFailedJobManagement();
    
    // Auto-refresh every 30 seconds
    setInterval(refreshDashboard, 30000);
});

function initializeCharts() {
    // Execution Trends Chart
    const executionData = @json($executionMetrics['daily_executions']);
    const executionCtx = document.getElementById('executionTrendsChart').getContext('2d');
    
    new Chart(executionCtx, {
        type: 'line',
        data: {
            labels: executionData.map(item => item.date),
            datasets: [{
                label: 'Successful',
                data: executionData.map(item => item.successful),
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                tension: 0.1
            }, {
                label: 'Failed',
                data: executionData.map(item => item.failed),
                borderColor: 'rgb(239, 68, 68)',
                backgroundColor: 'rgba(239, 68, 68, 0.1)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Error Types Chart
    const errorData = @json($errorAnalysis['error_types']);
    const errorCtx = document.getElementById('errorTypesChart').getContext('2d');
    
    new Chart(errorCtx, {
        type: 'doughnut',
        data: {
            labels: errorData.map(item => item.error_type || 'Unknown'),
            datasets: [{
                data: errorData.map(item => item.count),
                backgroundColor: [
                    '#ef4444',
                    '#f97316',
                    '#eab308',
                    '#22c55e',
                    '#3b82f6',
                    '#8b5cf6',
                    '#ec4899'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function initializeFailedJobManagement() {
    // Retry individual job
    document.querySelectorAll('.retry-job-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const jobId = this.dataset.jobId;
            retryJobs([jobId]);
        });
    });

    // Clear individual job
    document.querySelectorAll('.clear-job-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const jobId = this.dataset.jobId;
            clearJobs([jobId]);
        });
    });

    // Retry all jobs
    const retryAllBtn = document.getElementById('retry-all-btn');
    if (retryAllBtn) {
        retryAllBtn.addEventListener('click', function() {
            const jobIds = Array.from(document.querySelectorAll('.retry-job-btn')).map(btn => btn.dataset.jobId);
            retryJobs(jobIds);
        });
    }

    // Clear all jobs
    const clearAllBtn = document.getElementById('clear-all-btn');
    if (clearAllBtn) {
        clearAllBtn.addEventListener('click', function() {
            if (confirm('Are you sure you want to clear all failed jobs? This action cannot be undone.')) {
                const jobIds = Array.from(document.querySelectorAll('.clear-job-btn')).map(btn => btn.dataset.jobId);
                clearJobs(jobIds);
            }
        });
    }
}

function retryJobs(jobIds) {
    fetch('{{ route("admin.automation.retry-failed-jobs") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ job_ids: jobIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showNotification('Failed to retry jobs', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

function clearJobs(jobIds) {
    fetch('{{ route("admin.automation.clear-failed-jobs") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ job_ids: jobIds })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification(data.message, 'success');
            setTimeout(() => window.location.reload(), 1000);
        } else {
            showNotification('Failed to clear jobs', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred', 'error');
    });
}

function refreshDashboard() {
    // Refresh queue statistics
    fetch('{{ route("admin.automation.queue-data") }}?type=statistics')
        .then(response => response.json())
        .then(data => {
            // Update queue statistics in the UI
            updateQueueStatistics(data);
        })
        .catch(error => console.error('Error refreshing dashboard:', error));
}

function updateQueueStatistics(data) {
    // Update pending jobs count
    const pendingElement = document.querySelector('.bg-blue-50 .text-lg');
    if (pendingElement) {
        pendingElement.textContent = new Intl.NumberFormat().format(data.pending_jobs);
    }

    // Update failed jobs count
    const failedElement = document.querySelector('.bg-yellow-50 .text-lg');
    if (failedElement) {
        failedElement.textContent = new Intl.NumberFormat().format(data.failed_jobs);
    }
}

function showNotification(message, type) {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
        type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
    }`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    // Remove after 3 seconds
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
</script>
@endpush
