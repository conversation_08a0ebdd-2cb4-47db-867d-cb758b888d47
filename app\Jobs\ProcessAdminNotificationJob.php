<?php

namespace App\Jobs;

use App\Models\User;
use App\Notifications\AdminNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;
use Exception;

class ProcessAdminNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 60;
    public $tries = 2;
    public $backoff = [15, 30];

    protected array $notificationData;

    public function __construct(array $notificationData)
    {
        $this->notificationData = $notificationData;
        $this->onQueue('notifications');
    }

    public function handle(): void
    {
        $admins = User::role('admin')->get();
        
        if ($admins->isEmpty()) {
            Log::warning('No admin users found for notification', $this->notificationData);
            return;
        }

        foreach ($admins as $admin) {
            try {
                $admin->notify(new AdminNotification($this->notificationData));
            } catch (Exception $e) {
                Log::error('Failed to send admin notification', [
                    'admin_id' => $admin->id,
                    'notification_data' => $this->notificationData,
                    'error' => $e->getMessage()
                ]);
            }
        }

        Log::info('Admin notifications sent', [
            'type' => $this->notificationData['type'] ?? 'unknown',
            'admins_notified' => $admins->count()
        ]);
    }

    public function failed(Exception $exception): void
    {
        Log::error('Admin notification job failed', [
            'notification_data' => $this->notificationData,
            'error' => $exception->getMessage()
        ]);
    }

    public function tags(): array
    {
        return ['admin-notifications', 'type:' . ($this->notificationData['type'] ?? 'unknown')];
    }
}
