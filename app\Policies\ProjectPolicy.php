<?php

namespace App\Policies;

use App\Models\Project;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class ProjectPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('view projects');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Project $project): bool
    {
        // User can view if they have permission and can access the project
        if (!$user->hasPermissionTo('view projects')) {
            return false;
        }

        return $user->canAccessProject($project);
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo('create projects');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Project $project): bool
    {
        // User can update if they have permission and own the project
        if (!$user->hasPermissionTo('edit projects')) {
            return false;
        }

        return $project->user_id === $user->id;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Project $project): bool
    {
        // User can delete if they have permission and own the project
        if (!$user->hasPermissionTo('delete projects')) {
            return false;
        }

        return $project->user_id === $user->id;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Project $project): bool
    {
        // User can restore if they have permission and own the project
        if (!$user->hasPermissionTo('delete projects')) {
            return false;
        }

        return $project->user_id === $user->id;
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Project $project): bool
    {
        // User can force delete if they have permission and own the project
        if (!$user->hasPermissionTo('delete projects')) {
            return false;
        }

        return $project->user_id === $user->id;
    }

    /**
     * Determine whether the user can manage project members.
     */
    public function manageMembers(User $user, Project $project): bool
    {
        // User can manage members if they have permission and own the project
        if (!$user->hasPermissionTo('edit projects')) {
            return false;
        }

        return $project->user_id === $user->id;
    }

    /**
     * Determine whether the user can view project dashboard.
     */
    public function viewDashboard(User $user, Project $project): bool
    {
        // User can view dashboard if they have permission and can access the project
        if (!$user->hasPermissionTo('view projects')) {
            return false;
        }

        return $user->canAccessProject($project);
    }

    /**
     * Determine whether the user can update project status.
     */
    public function updateStatus(User $user, Project $project): bool
    {
        // User can update status if they have permission and own the project or are a project manager
        if (!$user->hasPermissionTo('edit projects')) {
            return false;
        }

        // Owner can always update status
        if ($project->user_id === $user->id) {
            return true;
        }

        // Project managers can also update status
        return $project->projectMembers()
            ->where('user_id', $user->id)
            ->where('role', 'manager')
            ->exists();
    }

    /**
     * Determine whether the user can duplicate the project.
     */
    public function duplicate(User $user, Project $project): bool
    {
        // User can duplicate if they have create permission and can view the project
        if (!$user->hasPermissionTo('create projects')) {
            return false;
        }

        return $user->canAccessProject($project);
    }

    /**
     * Determine whether the user can export project data.
     */
    public function export(User $user, Project $project): bool
    {
        // User can export if they have permission and can access the project
        if (!$user->hasPermissionTo('view projects')) {
            return false;
        }

        return $user->canAccessProject($project);
    }

    /**
     * Determine whether the user can view project reports.
     */
    public function viewReports(User $user, Project $project): bool
    {
        // User can view reports if they have permission and can access the project
        if (!$user->hasPermissionTo('view projects')) {
            return false;
        }

        return $user->canAccessProject($project);
    }

    /**
     * Determine whether the user can manage project tasks.
     */
    public function manageTasks(User $user, Project $project): bool
    {
        // User can manage tasks if they have permission and can access the project
        if (!$user->hasPermissionTo('edit projects')) {
            return false;
        }

        return $user->canAccessProject($project);
    }

    /**
     * Determine whether the user can track time on the project.
     */
    public function trackTime(User $user, Project $project): bool
    {
        // User can track time if they have permission and can access the project
        if (!$user->hasPermissionTo('view projects')) {
            return false;
        }

        return $user->canAccessProject($project);
    }
}
