<?php

namespace Tests\Unit\Models;

use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\User;
use App\Models\Client;
use App\Models\TdsRecord;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class InvoiceTest extends TestCase
{
    use RefreshDatabase;

    public function test_invoice_has_fillable_attributes()
    {
        $invoice = new Invoice();
        $fillable = [
            'user_id', 'client_id', 'invoice_number', 'invoice_date', 'due_date',
            'subtotal', 'tax_percentage', 'tax_amount', 'tds_percentage', 'tds_amount',
            'total_amount', 'net_amount', 'status', 'paid_date', 'notes', 'pdf_path'
        ];

        $this->assertEquals($fillable, $invoice->getFillable());
    }

    public function test_invoice_casts_attributes_correctly()
    {
        $invoice = Invoice::factory()->create([
            'invoice_date' => '2024-01-15',
            'due_date' => '2024-02-15',
            'paid_date' => '2024-01-20',
            'subtotal' => 1000.50,
            'tax_amount' => 180.09,
            'total_amount' => 1180.59,
        ]);

        $this->assertInstanceOf(Carbon::class, $invoice->invoice_date);
        $this->assertInstanceOf(Carbon::class, $invoice->due_date);
        $this->assertInstanceOf(Carbon::class, $invoice->paid_date);
        $this->assertIsFloat($invoice->subtotal);
        $this->assertIsFloat($invoice->tax_amount);
        $this->assertIsFloat($invoice->total_amount);
    }

    public function test_invoice_belongs_to_user()
    {
        $user = User::factory()->create();
        $invoice = Invoice::factory()->forUser($user)->create();

        $this->assertInstanceOf(User::class, $invoice->user);
        $this->assertEquals($user->id, $invoice->user->id);
    }

    public function test_invoice_belongs_to_client()
    {
        $client = Client::factory()->create();
        $invoice = Invoice::factory()->forClient($client)->create();

        $this->assertInstanceOf(Client::class, $invoice->client);
        $this->assertEquals($client->id, $invoice->client->id);
    }

    public function test_invoice_has_many_items()
    {
        $invoice = Invoice::factory()->create();
        $items = InvoiceItem::factory()->count(3)->forInvoice($invoice)->create();

        $this->assertCount(3, $invoice->items);
        $this->assertInstanceOf(InvoiceItem::class, $invoice->items->first());
    }

    public function test_invoice_has_one_tds_record()
    {
        $invoice = Invoice::factory()->create();
        $tdsRecord = TdsRecord::factory()->forInvoice($invoice)->create();

        $this->assertInstanceOf(TdsRecord::class, $invoice->tdsRecord);
        $this->assertEquals($tdsRecord->id, $invoice->tdsRecord->id);
    }

    public function test_invoice_calculates_amounts_correctly()
    {
        $subtotal = 1000.00;
        $taxPercentage = 18.00;
        $tdsPercentage = 2.00;
        
        $taxAmount = ($subtotal * $taxPercentage) / 100; // 180.00
        $totalAmount = $subtotal + $taxAmount; // 1180.00
        $tdsAmount = ($subtotal * $tdsPercentage) / 100; // 20.00
        $netAmount = $totalAmount - $tdsAmount; // 1160.00

        $invoice = Invoice::factory()->create([
            'subtotal' => $subtotal,
            'tax_percentage' => $taxPercentage,
            'tax_amount' => $taxAmount,
            'tds_percentage' => $tdsPercentage,
            'tds_amount' => $tdsAmount,
            'total_amount' => $totalAmount,
            'net_amount' => $netAmount,
        ]);

        $this->assertEquals(1000.00, $invoice->subtotal);
        $this->assertEquals(180.00, $invoice->tax_amount);
        $this->assertEquals(1180.00, $invoice->total_amount);
        $this->assertEquals(20.00, $invoice->tds_amount);
        $this->assertEquals(1160.00, $invoice->net_amount);
    }

    public function test_invoice_without_tax()
    {
        $invoice = Invoice::factory()->withoutTax()->create();

        $this->assertEquals(0, $invoice->tax_percentage);
        $this->assertEquals(0, $invoice->tax_amount);
        $this->assertEquals($invoice->subtotal, $invoice->total_amount);
    }

    public function test_invoice_without_tds()
    {
        $invoice = Invoice::factory()->withoutTds()->create();

        $this->assertEquals(0, $invoice->tds_percentage);
        $this->assertEquals(0, $invoice->tds_amount);
        $this->assertEquals($invoice->total_amount, $invoice->net_amount);
    }

    public function test_invoice_status_transitions()
    {
        $invoice = Invoice::factory()->draft()->create();
        $this->assertEquals('draft', $invoice->status);
        $this->assertNull($invoice->paid_date);

        $invoice = Invoice::factory()->sent()->create();
        $this->assertEquals('sent', $invoice->status);
        $this->assertNull($invoice->paid_date);

        $invoice = Invoice::factory()->paid()->create();
        $this->assertEquals('paid', $invoice->status);
        $this->assertNotNull($invoice->paid_date);

        $invoice = Invoice::factory()->overdue()->create();
        $this->assertEquals('overdue', $invoice->status);
        $this->assertNull($invoice->paid_date);
        $this->assertTrue($invoice->due_date->isPast());
    }

    public function test_invoice_is_overdue()
    {
        $overdueInvoice = Invoice::factory()->create([
            'due_date' => Carbon::yesterday(),
            'status' => 'sent'
        ]);

        $currentInvoice = Invoice::factory()->create([
            'due_date' => Carbon::tomorrow(),
            'status' => 'sent'
        ]);

        $paidInvoice = Invoice::factory()->create([
            'due_date' => Carbon::yesterday(),
            'status' => 'paid'
        ]);

        // Test overdue logic (would need to be implemented in model)
        $this->assertTrue($overdueInvoice->due_date->isPast() && $overdueInvoice->status !== 'paid');
        $this->assertFalse($currentInvoice->due_date->isPast());
        $this->assertFalse($paidInvoice->due_date->isPast() && $paidInvoice->status !== 'paid');
    }

    public function test_invoice_number_is_unique()
    {
        $invoiceNumber = 'INV-2024-001';
        
        $invoice1 = Invoice::factory()->create(['invoice_number' => $invoiceNumber]);
        
        $this->assertEquals($invoiceNumber, $invoice1->invoice_number);
        
        // Second invoice should have different number
        $invoice2 = Invoice::factory()->create();
        $this->assertNotEquals($invoiceNumber, $invoice2->invoice_number);
    }

    public function test_invoice_belongs_to_same_user_as_client()
    {
        $user = User::factory()->create();
        $client = Client::factory()->forUser($user)->create();
        $invoice = Invoice::factory()->forClient($client)->create();

        $this->assertEquals($user->id, $invoice->user_id);
        $this->assertEquals($user->id, $invoice->client->user_id);
        $this->assertEquals($client->id, $invoice->client_id);
    }

    public function test_invoice_items_sum_equals_subtotal()
    {
        $invoice = Invoice::factory()->create(['subtotal' => 0]);
        
        InvoiceItem::factory()->forInvoice($invoice)->create(['amount' => 500.00]);
        InvoiceItem::factory()->forInvoice($invoice)->create(['amount' => 300.00]);
        InvoiceItem::factory()->forInvoice($invoice)->create(['amount' => 200.00]);

        $itemsTotal = $invoice->items->sum('amount');
        $this->assertEquals(1000.00, $itemsTotal);
    }
}
