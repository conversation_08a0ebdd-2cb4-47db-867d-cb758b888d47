<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SubscriptionException extends Exception
{
    protected $subscriptionId;
    protected $planId;
    protected $errorType;

    public function __construct(
        string $message = 'Subscription operation failed',
        string $subscriptionId = null,
        string $planId = null,
        string $errorType = 'general',
        int $code = 0,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->subscriptionId = $subscriptionId;
        $this->planId = $planId;
        $this->errorType = $errorType;
    }

    /**
     * Render the exception as an HTTP response.
     */
    public function render(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Subscription operation failed',
                'message' => $this->getMessage(),
                'subscription_id' => $this->subscriptionId,
                'plan_id' => $this->planId,
                'error_type' => $this->errorType,
            ], 422);
        }

        return response()->view('errors.subscription', [
            'message' => $this->getMessage(),
            'subscriptionId' => $this->subscriptionId,
            'planId' => $this->planId,
            'errorType' => $this->errorType,
        ], 422);
    }

    /**
     * Get the subscription ID that caused the error.
     */
    public function getSubscriptionId(): ?string
    {
        return $this->subscriptionId;
    }

    /**
     * Get the plan ID that caused the error.
     */
    public function getPlanId(): ?string
    {
        return $this->planId;
    }

    /**
     * Get the error type.
     */
    public function getErrorType(): string
    {
        return $this->errorType;
    }
}
