<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreInvoiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'client_id' => ['required', 'exists:clients,id', function ($attribute, $value, $fail) {
                $client = \App\Models\Client::find($value);
                if ($client && $client->user_id !== auth()->id()) {
                    $fail('The selected client is invalid.');
                }
            }],
            'invoice_date' => ['required', 'date', 'before_or_equal:today'],
            'due_date' => ['required', 'date', 'after_or_equal:invoice_date'],
            'tax_percentage' => ['nullable', 'numeric', 'min:0', 'max:100'],
            'tds_percentage' => ['nullable', 'numeric', 'min:0', 'max:100'],
            'notes' => ['nullable', 'string', 'max:2000'],
            'items' => ['required', 'array', 'min:1', 'max:50'],
            'items.*.description' => ['required', 'string', 'max:500'],
            'items.*.quantity' => ['required', 'numeric', 'min:0.01', 'max:999999'],
            'items.*.rate' => ['required', 'numeric', 'min:0', 'max:*********'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'client_id.exists' => 'The selected client is invalid.',
            'invoice_date.before_or_equal' => 'The invoice date cannot be in the future.',
            'due_date.after_or_equal' => 'The due date must be on or after the invoice date.',
            'items.min' => 'At least one item is required.',
            'items.max' => 'Maximum 50 items are allowed per invoice.',
            'items.*.quantity.min' => 'Quantity must be greater than 0.',
            'items.*.rate.min' => 'Rate cannot be negative.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'user_id' => auth()->id(),
        ]);
    }
}
