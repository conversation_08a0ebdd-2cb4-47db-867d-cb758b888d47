<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\UserSubscription;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class PayPalService
{
    private $clientId;
    private $clientSecret;
    private $baseUrl;

    public function __construct()
    {
        $this->clientId = config('services.paypal.client_id');
        $this->clientSecret = config('services.paypal.client_secret');
        $this->baseUrl = config('services.paypal.mode') === 'live'
            ? 'https://api-m.paypal.com'
            : 'https://api-m.sandbox.paypal.com';
    }

    /**
     * Get PayPal access token.
     */
    private function getAccessToken()
    {
        $response = Http::withBasicAuth($this->clientId, $this->clientSecret)
            ->asForm()
            ->post($this->baseUrl . '/v1/oauth2/token', [
                'grant_type' => 'client_credentials'
            ]);

        if ($response->successful()) {
            return $response->json()['access_token'];
        }

        throw new \Exception('Failed to get PayPal access token: ' . $response->body());
    }

    /**
     * Create PayPal subscription.
     */
    public function createSubscription(Payment $payment)
    {
        $accessToken = $this->getAccessToken();
        $subscription = $payment->userSubscription;
        $plan = $subscription->plan;

        $subscriptionData = [
            'plan_id' => $this->getOrCreatePlan($plan),
            'subscriber' => [
                'name' => [
                    'given_name' => $payment->user->name,
                ],
                'email_address' => $payment->user->email,
            ],
            'application_context' => [
                'brand_name' => 'Freeligo',
                'locale' => 'en-IN',
                'shipping_preference' => 'NO_SHIPPING',
                'user_action' => 'SUBSCRIBE_NOW',
                'payment_method' => [
                    'payer_selected' => 'PAYPAL',
                    'payee_preferred' => 'IMMEDIATE_PAYMENT_REQUIRED',
                ],
                'return_url' => route('payment.success', $payment),
                'cancel_url' => route('payment.failure', $payment),
            ],
        ];

        $response = Http::withToken($accessToken)
            ->post($this->baseUrl . '/v1/billing/subscriptions', $subscriptionData);

        if ($response->successful()) {
            $responseData = $response->json();

            // Update payment with PayPal subscription ID
            $payment->update([
                'gateway_payment_id' => $responseData['id'],
                'gateway_response' => $responseData,
            ]);

            // Update subscription with PayPal data
            $subscription->update([
                'gateway_subscription_id' => $responseData['id'],
                'gateway_data' => $responseData,
            ]);

            // Return approval URL
            foreach ($responseData['links'] as $link) {
                if ($link['rel'] === 'approve') {
                    return $link['href'];
                }
            }
        }

        throw new \Exception('Failed to create PayPal subscription: ' . $response->body());
    }

    /**
     * Get or create PayPal billing plan.
     */
    private function getOrCreatePlan($plan)
    {
        // In a real implementation, you would store PayPal plan IDs in the database
        // For now, we'll create a plan on-the-fly
        $accessToken = $this->getAccessToken();

        $planData = [
            'product_id' => $this->getOrCreateProduct($plan),
            'name' => $plan->name . ' Plan',
            'description' => $plan->description,
            'status' => 'ACTIVE',
            'billing_cycles' => [
                [
                    'frequency' => [
                        'interval_unit' => 'MONTH',
                        'interval_count' => 1,
                    ],
                    'tenure_type' => 'REGULAR',
                    'sequence' => 1,
                    'total_cycles' => 0, // Infinite
                    'pricing_scheme' => [
                        'fixed_price' => [
                            'value' => number_format($plan->price, 2, '.', ''),
                            'currency_code' => $plan->currency,
                        ],
                    ],
                ],
            ],
            'payment_preferences' => [
                'auto_bill_outstanding' => true,
                'setup_fee_failure_action' => 'CONTINUE',
                'payment_failure_threshold' => 3,
            ],
        ];

        $response = Http::withToken($accessToken)
            ->post($this->baseUrl . '/v1/billing/plans', $planData);

        if ($response->successful()) {
            return $response->json()['id'];
        }

        throw new \Exception('Failed to create PayPal plan: ' . $response->body());
    }

    /**
     * Get or create PayPal product.
     */
    private function getOrCreateProduct($plan)
    {
        $accessToken = $this->getAccessToken();

        $productData = [
            'name' => 'Freeligo ' . $plan->name . ' Plan',
            'description' => $plan->description,
            'type' => 'SERVICE',
            'category' => 'SOFTWARE',
        ];

        $response = Http::withToken($accessToken)
            ->post($this->baseUrl . '/v1/catalogs/products', $productData);

        if ($response->successful()) {
            return $response->json()['id'];
        }

        throw new \Exception('Failed to create PayPal product: ' . $response->body());
    }

    /**
     * Handle PayPal webhook.
     */
    public function handleWebhook($payload, $headers)
    {
        // Verify webhook signature
        if (!$this->verifyWebhookSignature($payload, $headers)) {
            throw new \Exception('Invalid PayPal webhook signature');
        }

        $event = json_decode($payload, true);

        switch ($event['event_type']) {
            case 'BILLING.SUBSCRIPTION.ACTIVATED':
                $this->handleSubscriptionActivated($event);
                break;
            case 'BILLING.SUBSCRIPTION.CANCELLED':
                $this->handleSubscriptionCancelled($event);
                break;
            case 'PAYMENT.SALE.COMPLETED':
                $this->handlePaymentCompleted($event);
                break;
            default:
                Log::info('Unhandled PayPal webhook event: ' . $event['event_type']);
        }
    }

    /**
     * Verify PayPal webhook signature.
     */
    private function verifyWebhookSignature($payload, $headers)
    {
        try {
            $webhookId = config('services.paypal.webhook_id');
            $authAlgo = $headers['paypal-auth-algo'][0] ?? '';
            $transmission_id = $headers['paypal-transmission-id'][0] ?? '';
            $cert_id = $headers['paypal-cert-id'][0] ?? '';
            $transmission_sig = $headers['paypal-transmission-sig'][0] ?? '';
            $transmission_time = $headers['paypal-transmission-time'][0] ?? '';

            if (empty($authAlgo) || empty($transmission_id) || empty($cert_id) || empty($transmission_sig) || empty($transmission_time)) {
                Log::warning('PayPal webhook missing required headers');
                return false;
            }

            $accessToken = $this->getAccessToken();

            $verificationData = [
                'auth_algo' => $authAlgo,
                'cert_id' => $cert_id,
                'transmission_id' => $transmission_id,
                'transmission_sig' => $transmission_sig,
                'transmission_time' => $transmission_time,
                'webhook_id' => $webhookId,
                'webhook_event' => json_decode($payload, true)
            ];

            $response = Http::withToken($accessToken)
                ->post($this->baseUrl . '/v1/notifications/verify-webhook-signature', $verificationData);

            if ($response->successful()) {
                $result = $response->json();
                return $result['verification_status'] === 'SUCCESS';
            }

            Log::error('PayPal webhook verification failed', [
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return false;
        } catch (\Exception $e) {
            Log::error('PayPal webhook verification error', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }

    /**
     * Handle subscription activated event.
     */
    private function handleSubscriptionActivated($event)
    {
        $subscriptionId = $event['resource']['id'];

        $subscription = UserSubscription::where('gateway_subscription_id', $subscriptionId)->first();
        if ($subscription) {
            $subscription->update(['status' => 'active']);

            $payment = $subscription->payments()->where('status', 'pending')->first();
            if ($payment) {
                $payment->markAsCompleted($event);
            }
        }
    }

    /**
     * Handle subscription cancelled event.
     */
    private function handleSubscriptionCancelled($event)
    {
        $subscriptionId = $event['resource']['id'];

        $subscription = UserSubscription::where('gateway_subscription_id', $subscriptionId)->first();
        if ($subscription) {
            $subscription->cancel();
        }
    }

    /**
     * Handle payment completed event.
     */
    private function handlePaymentCompleted($event)
    {
        $paymentId = $event['resource']['billing_agreement_id'] ?? null;

        if ($paymentId) {
            $subscription = UserSubscription::where('gateway_subscription_id', $paymentId)->first();
            if ($subscription) {
                // Create new payment record for recurring payment
                Payment::create([
                    'user_id' => $subscription->user_id,
                    'user_subscription_id' => $subscription->id,
                    'gateway_payment_id' => $event['resource']['id'],
                    'gateway' => 'paypal',
                    'amount' => $event['resource']['amount']['total'],
                    'currency' => $event['resource']['amount']['currency'],
                    'status' => 'completed',
                    'type' => 'renewal',
                    'gateway_response' => $event,
                    'paid_at' => now(),
                ]);
            }
        }
    }

    /**
     * Cancel PayPal subscription.
     */
    public function cancelSubscription($subscriptionId, $reason = 'User requested cancellation')
    {
        $accessToken = $this->getAccessToken();

        $response = Http::withToken($accessToken)
            ->post($this->baseUrl . "/v1/billing/subscriptions/{$subscriptionId}/cancel", [
                'reason' => $reason,
            ]);

        return $response->successful();
    }
}
