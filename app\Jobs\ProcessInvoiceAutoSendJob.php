<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Services\InvoiceService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessInvoiceAutoSendJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 120;
    public $tries = 3;
    public $backoff = [10, 30, 60];

    protected int $invoiceId;
    protected array $options;

    public function __construct(int $invoiceId, array $options = [])
    {
        $this->invoiceId = $invoiceId;
        $this->options = $options;
        $this->onQueue('high');
    }

    public function handle(InvoiceService $service): void
    {
        $invoice = Invoice::find($this->invoiceId);
        
        if (!$invoice) {
            Log::warning('Invoice not found for auto-send', ['invoice_id' => $this->invoiceId]);
            return;
        }

        if ($invoice->status !== 'draft') {
            Log::info('Invoice not in draft status for auto-send', [
                'invoice_id' => $this->invoiceId,
                'status' => $invoice->status
            ]);
            return;
        }

        $result = $service->sendInvoice($invoice);
        
        if ($result['success']) {
            Log::info('Invoice auto-sent successfully', [
                'invoice_id' => $this->invoiceId,
                'client_email' => $invoice->client->email
            ]);
        }
    }

    public function failed(Exception $exception): void
    {
        Log::error('Invoice auto-send job failed', [
            'invoice_id' => $this->invoiceId,
            'error' => $exception->getMessage()
        ]);
    }

    public function tags(): array
    {
        return ['invoice-auto-send', "invoice:{$this->invoiceId}"];
    }
}
