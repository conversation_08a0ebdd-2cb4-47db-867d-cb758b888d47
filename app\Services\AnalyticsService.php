<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\Client;
use App\Models\Contract;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Collection;

class AnalyticsService
{
    protected $user;

    public function __construct(User $user = null)
    {
        $this->user = $user;
    }

    /**
     * Get revenue comparison data for specified periods
     */
    public function getRevenueComparison(string $period = 'monthly', int $periods = 12): array
    {
        $query = $this->user ? $this->user->invoices() : Invoice::query();
        
        $data = $query->where('status', 'paid')
            ->select(
                DB::raw($this->getPeriodSelectRaw($period)),
                DB::raw('SUM(total_amount) as revenue'),
                DB::raw('COUNT(*) as invoice_count'),
                DB::raw('AVG(total_amount) as avg_invoice_value')
            )
            ->groupBy($this->getPeriodGroupBy($period))
            ->orderBy($this->getPeriodOrderBy($period), 'desc')
            ->take($periods)
            ->get();

        return $this->formatRevenueComparisonData($data, $period);
    }

    /**
     * Get client performance analytics
     */
    public function getClientPerformanceAnalytics(): array
    {
        $query = $this->user ? $this->user->clients() : Client::query();
        
        $clients = $query->with(['invoices', 'contracts'])
            ->get()
            ->map(function ($client) {
                $paidInvoices = $client->invoices->where('status', 'paid');
                $totalRevenue = $paidInvoices->sum('total_amount');
                $invoiceCount = $client->invoices->count();
                
                return [
                    'id' => $client->id,
                    'name' => $client->name,
                    'company_name' => $client->company_name,
                    'total_revenue' => $totalRevenue,
                    'invoice_count' => $invoiceCount,
                    'avg_invoice_value' => $invoiceCount > 0 ? $totalRevenue / $invoiceCount : 0,
                    'pending_amount' => $client->invoices->where('status', 'pending')->sum('total_amount'),
                    'overdue_amount' => $client->invoices
                        ->where('status', '!=', 'paid')
                        ->where('due_date', '<', now())
                        ->sum('total_amount'),
                    'contracts_count' => $client->contracts->count(),
                    'last_invoice_date' => $client->invoices->max('created_at'),
                    'avg_payment_time' => $this->calculateAveragePaymentTime($client->invoices),
                    'client_lifetime_value' => $this->calculateClientLifetimeValue($client),
                    'payment_reliability' => $this->calculatePaymentReliability($client->invoices),
                ];
            })
            ->sortByDesc('total_revenue');

        return [
            'clients' => $clients->values()->all(),
            'summary' => [
                'total_clients' => $clients->count(),
                'active_clients' => $clients->where('invoice_count', '>', 0)->count(),
                'top_client_revenue' => $clients->first()['total_revenue'] ?? 0,
                'avg_client_value' => $clients->avg('total_revenue'),
            ]
        ];
    }

    /**
     * Get invoice payment pattern analysis
     */
    public function getPaymentPatternAnalysis(): array
    {
        $query = $this->user ? $this->user->invoices() : Invoice::query();
        
        $invoices = $query->where('status', 'paid')
            ->whereNotNull('paid_date')
            ->get();

        $paymentTimes = $invoices->map(function ($invoice) {
            return Carbon::parse($invoice->due_date)->diffInDays(Carbon::parse($invoice->paid_date), false);
        });

        $monthlyPayments = $invoices->groupBy(function ($invoice) {
            return Carbon::parse($invoice->paid_date)->format('Y-m');
        })->map(function ($group) {
            return [
                'count' => $group->count(),
                'total_amount' => $group->sum('total_amount'),
                'avg_payment_time' => $group->avg(function ($invoice) {
                    return Carbon::parse($invoice->due_date)->diffInDays(Carbon::parse($invoice->paid_date), false);
                })
            ];
        });

        return [
            'average_payment_time' => $paymentTimes->avg(),
            'median_payment_time' => $this->calculateMedian($paymentTimes->toArray()),
            'early_payments' => $paymentTimes->filter(fn($days) => $days < 0)->count(),
            'on_time_payments' => $paymentTimes->filter(fn($days) => $days >= 0 && $days <= 3)->count(),
            'late_payments' => $paymentTimes->filter(fn($days) => $days > 3)->count(),
            'monthly_patterns' => $monthlyPayments,
            'payment_time_distribution' => $this->getPaymentTimeDistribution($paymentTimes),
        ];
    }

    /**
     * Get business growth metrics
     */
    public function getBusinessGrowthMetrics(): array
    {
        $currentYear = now()->year;
        $previousYear = $currentYear - 1;
        
        $currentYearData = $this->getYearlyMetrics($currentYear);
        $previousYearData = $this->getYearlyMetrics($previousYear);
        
        return [
            'revenue_growth' => $this->calculateGrowthPercentage(
                $currentYearData['revenue'], 
                $previousYearData['revenue']
            ),
            'client_growth' => $this->calculateGrowthPercentage(
                $currentYearData['new_clients'], 
                $previousYearData['new_clients']
            ),
            'invoice_volume_growth' => $this->calculateGrowthPercentage(
                $currentYearData['invoice_count'], 
                $previousYearData['invoice_count']
            ),
            'avg_invoice_value_growth' => $this->calculateGrowthPercentage(
                $currentYearData['avg_invoice_value'], 
                $previousYearData['avg_invoice_value']
            ),
            'monthly_growth_trend' => $this->getMonthlyGrowthTrend(),
            'quarterly_performance' => $this->getQuarterlyPerformance(),
        ];
    }

    /**
     * Get profit margin analysis
     */
    public function getProfitMarginAnalysis(): array
    {
        $query = $this->user ? $this->user->invoices() : Invoice::query();
        
        $invoices = $query->where('status', 'paid')->get();
        
        $totalRevenue = $invoices->sum('total_amount');
        $totalTaxAmount = $invoices->sum('tax_amount');
        $totalTdsAmount = $invoices->sum('tds_amount');
        $netRevenue = $invoices->sum('net_amount');
        
        $monthlyProfitability = $invoices->groupBy(function ($invoice) {
            return Carbon::parse($invoice->paid_date ?? $invoice->created_at)->format('Y-m');
        })->map(function ($group) {
            return [
                'revenue' => $group->sum('total_amount'),
                'tax_amount' => $group->sum('tax_amount'),
                'tds_amount' => $group->sum('tds_amount'),
                'net_revenue' => $group->sum('net_amount'),
                'profit_margin' => $group->sum('total_amount') > 0 
                    ? ($group->sum('net_amount') / $group->sum('total_amount')) * 100 
                    : 0,
            ];
        });

        return [
            'total_revenue' => $totalRevenue,
            'total_tax_amount' => $totalTaxAmount,
            'total_tds_amount' => $totalTdsAmount,
            'net_revenue' => $netRevenue,
            'overall_profit_margin' => $totalRevenue > 0 ? ($netRevenue / $totalRevenue) * 100 : 0,
            'tax_percentage' => $totalRevenue > 0 ? ($totalTaxAmount / $totalRevenue) * 100 : 0,
            'tds_percentage' => $totalRevenue > 0 ? ($totalTdsAmount / $totalRevenue) * 100 : 0,
            'monthly_profitability' => $monthlyProfitability,
            'client_profitability' => $this->getClientProfitability(),
        ];
    }

    /**
     * Calculate average payment time for invoices
     */
    private function calculateAveragePaymentTime(Collection $invoices): float
    {
        $paidInvoices = $invoices->where('status', 'paid')->whereNotNull('paid_date');
        
        if ($paidInvoices->isEmpty()) {
            return 0;
        }

        $totalDays = $paidInvoices->sum(function ($invoice) {
            return Carbon::parse($invoice->due_date)->diffInDays(Carbon::parse($invoice->paid_date), false);
        });

        return $totalDays / $paidInvoices->count();
    }

    /**
     * Calculate client lifetime value
     */
    private function calculateClientLifetimeValue(Client $client): float
    {
        $firstInvoice = $client->invoices->min('created_at');
        if (!$firstInvoice) {
            return 0;
        }

        $monthsActive = Carbon::parse($firstInvoice)->diffInMonths(now()) + 1;
        $totalRevenue = $client->invoices->where('status', 'paid')->sum('total_amount');
        
        return $monthsActive > 0 ? $totalRevenue / $monthsActive : 0;
    }

    /**
     * Calculate payment reliability score
     */
    private function calculatePaymentReliability(Collection $invoices): float
    {
        $totalInvoices = $invoices->count();
        if ($totalInvoices === 0) {
            return 0;
        }

        $paidOnTime = $invoices->filter(function ($invoice) {
            if ($invoice->status !== 'paid' || !$invoice->paid_date) {
                return false;
            }
            return Carbon::parse($invoice->paid_date)->lte(Carbon::parse($invoice->due_date));
        })->count();

        return ($paidOnTime / $totalInvoices) * 100;
    }

    /**
     * Get period select raw SQL
     */
    private function getPeriodSelectRaw(string $period): string
    {
        switch ($period) {
            case 'yearly':
                return 'YEAR(created_at) as period';
            case 'quarterly':
                return 'CONCAT(YEAR(created_at), "-Q", QUARTER(created_at)) as period';
            default: // monthly
                return 'DATE_FORMAT(created_at, "%Y-%m") as period';
        }
    }

    /**
     * Get period group by clause
     */
    private function getPeriodGroupBy(string $period): string
    {
        switch ($period) {
            case 'yearly':
                return 'YEAR(created_at)';
            case 'quarterly':
                return 'YEAR(created_at), QUARTER(created_at)';
            default: // monthly
                return 'YEAR(created_at), MONTH(created_at)';
        }
    }

    /**
     * Get period order by clause
     */
    private function getPeriodOrderBy(string $period): string
    {
        switch ($period) {
            case 'yearly':
                return 'YEAR(created_at)';
            case 'quarterly':
                return 'YEAR(created_at), QUARTER(created_at)';
            default: // monthly
                return 'YEAR(created_at), MONTH(created_at)';
        }
    }

    /**
     * Format revenue comparison data
     */
    private function formatRevenueComparisonData(Collection $data, string $period): array
    {
        $formatted = $data->map(function ($item) {
            return [
                'period' => $item->period,
                'revenue' => $item->revenue,
                'invoice_count' => $item->invoice_count,
                'avg_invoice_value' => $item->avg_invoice_value,
            ];
        })->reverse()->values();

        // Calculate growth percentages
        $withGrowth = $formatted->map(function ($item, $index) use ($formatted) {
            $previousItem = $formatted->get($index - 1);
            $item['revenue_growth'] = $previousItem 
                ? $this->calculateGrowthPercentage($item['revenue'], $previousItem['revenue'])
                : 0;
            return $item;
        });

        return $withGrowth->toArray();
    }

    /**
     * Calculate growth percentage
     */
    private function calculateGrowthPercentage(float $current, float $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }
        return (($current - $previous) / $previous) * 100;
    }

    /**
     * Calculate median value
     */
    private function calculateMedian(array $values): float
    {
        if (empty($values)) {
            return 0;
        }

        sort($values);
        $count = count($values);
        $middle = floor($count / 2);

        if ($count % 2 === 0) {
            return ($values[$middle - 1] + $values[$middle]) / 2;
        }

        return $values[$middle];
    }

    /**
     * Get payment time distribution
     */
    private function getPaymentTimeDistribution(Collection $paymentTimes): array
    {
        return [
            'early' => $paymentTimes->filter(fn($days) => $days < 0)->count(),
            'on_time' => $paymentTimes->filter(fn($days) => $days >= 0 && $days <= 3)->count(),
            'late_1_week' => $paymentTimes->filter(fn($days) => $days > 3 && $days <= 7)->count(),
            'late_2_weeks' => $paymentTimes->filter(fn($days) => $days > 7 && $days <= 14)->count(),
            'late_1_month' => $paymentTimes->filter(fn($days) => $days > 14 && $days <= 30)->count(),
            'very_late' => $paymentTimes->filter(fn($days) => $days > 30)->count(),
        ];
    }

    /**
     * Get yearly metrics
     */
    private function getYearlyMetrics(int $year): array
    {
        $query = $this->user ? $this->user->invoices() : Invoice::query();
        
        $invoices = $query->whereYear('created_at', $year)->get();
        $paidInvoices = $invoices->where('status', 'paid');
        
        $clientQuery = $this->user ? $this->user->clients() : Client::query();
        $newClients = $clientQuery->whereYear('created_at', $year)->count();

        return [
            'revenue' => $paidInvoices->sum('total_amount'),
            'invoice_count' => $invoices->count(),
            'avg_invoice_value' => $invoices->count() > 0 ? $paidInvoices->sum('total_amount') / $invoices->count() : 0,
            'new_clients' => $newClients,
        ];
    }

    /**
     * Get monthly growth trend
     */
    private function getMonthlyGrowthTrend(): array
    {
        $monthlyData = $this->getRevenueComparison('monthly', 12);
        
        return collect($monthlyData)->map(function ($month, $index) use ($monthlyData) {
            $previousMonth = $index > 0 ? $monthlyData[$index - 1] : null;
            return [
                'period' => $month['period'],
                'revenue' => $month['revenue'],
                'growth' => $previousMonth 
                    ? $this->calculateGrowthPercentage($month['revenue'], $previousMonth['revenue'])
                    : 0,
            ];
        })->toArray();
    }

    /**
     * Get quarterly performance
     */
    private function getQuarterlyPerformance(): array
    {
        return $this->getRevenueComparison('quarterly', 8);
    }

    /**
     * Get client profitability analysis
     */
    private function getClientProfitability(): array
    {
        $query = $this->user ? $this->user->clients() : Client::query();
        
        return $query->with('invoices')
            ->get()
            ->map(function ($client) {
                $paidInvoices = $client->invoices->where('status', 'paid');
                $totalRevenue = $paidInvoices->sum('total_amount');
                $netRevenue = $paidInvoices->sum('net_amount');
                
                return [
                    'client_name' => $client->name,
                    'total_revenue' => $totalRevenue,
                    'net_revenue' => $netRevenue,
                    'profit_margin' => $totalRevenue > 0 ? (($netRevenue / $totalRevenue) * 100) : 0,
                    'tax_amount' => $paidInvoices->sum('tax_amount'),
                    'tds_amount' => $paidInvoices->sum('tds_amount'),
                ];
            })
            ->sortByDesc('profit_margin')
            ->values()
            ->toArray();
    }
}
