<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Auth;

class PlanChecker
{
    /**
     * Check if user can create invoices
     */
    public static function canCreateInvoice(User $user = null): bool
    {
        $user = $user ?? Auth::user();
        
        if (!$user || $user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        $isFreePlan = !$currentPlan || $currentPlan->slug === 'free';
        
        if (!$isFreePlan) {
            return true; // Pro/Business users can create unlimited
        }

        // Check monthly limit for free users
        $currentMonth = now()->format('Y-m');
        $monthlyCount = $user->invoices()
            ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
            ->count();
            
        return $monthlyCount < 3;
    }

    /**
     * Check if user can access TDS reports
     */
    public static function canAccessTdsReports(User $user = null): bool
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return false;
        }

        if ($user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        return $currentPlan && in_array($currentPlan->slug, ['pro', 'business']);
    }

    /**
     * Check if user can create contracts
     */
    public static function canCreateContract(User $user = null): bool
    {
        $user = $user ?? Auth::user();
        
        if (!$user || $user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        $isFreePlan = !$currentPlan || $currentPlan->slug === 'free';
        
        if (!$isFreePlan) {
            return true; // Pro/Business users can create unlimited
        }

        // Free users can create limited contracts
        return $user->contracts()->count() < 2;
    }

    /**
     * Check if user can use AI assistant
     */
    public static function canUseAiAssistant(User $user = null): bool
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return false;
        }

        if ($user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        return $currentPlan && $currentPlan->slug === 'business';
    }

    /**
     * Check if user can use multi-client dashboard
     */
    public static function canUseMultiClientDashboard(User $user = null): bool
    {
        $user = $user ?? Auth::user();
        
        if (!$user) {
            return false;
        }

        if ($user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        return $currentPlan && $currentPlan->slug === 'business';
    }

    /**
     * Check if user can use portfolio generator
     */
    public static function canUsePortfolioGenerator(User $user = null): bool
    {
        $user = $user ?? Auth::user();

        if (!$user) {
            return false;
        }

        if ($user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        return $currentPlan && $currentPlan->slug === 'business';
    }

    /**
     * Check if user can use custom branding
     */
    public static function canUseCustomBranding(User $user = null): bool
    {
        $user = $user ?? Auth::user();

        if (!$user) {
            return false;
        }

        if ($user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        return $currentPlan && in_array($currentPlan->slug, ['pro', 'business']);
    }

    /**
     * Check if user can use WhatsApp shortcuts
     */
    public static function canUseWhatsAppShortcuts(User $user = null): bool
    {
        $user = $user ?? Auth::user();

        if (!$user) {
            return false;
        }

        if ($user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        return $currentPlan && in_array($currentPlan->slug, ['pro', 'business']);
    }

    /**
     * Check if user can access multi-client dashboard
     */
    public static function canAccessMultiClientDashboard(User $user = null): bool
    {
        $user = $user ?? Auth::user();

        if (!$user) {
            return false;
        }

        if ($user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        return $currentPlan && $currentPlan->slug === 'business';
    }



    /**
     * Get monthly invoice usage for user
     */
    public static function getMonthlyInvoiceUsage(User $user = null): array
    {
        $user = $user ?? Auth::user();

        if (!$user) {
            return ['used' => 0, 'limit' => 0, 'percentage' => 0];
        }

        if ($user->hasRole('admin')) {
            return ['used' => 0, 'limit' => 'unlimited', 'percentage' => 0];
        }

        $currentPlan = $user->currentPlan;
        $isFreePlan = !$currentPlan || $currentPlan->slug === 'free';

        if (!$isFreePlan) {
            return ['used' => 0, 'limit' => 'unlimited', 'percentage' => 0];
        }

        $currentMonth = now()->format('Y-m');
        $used = $user->invoices()
            ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
            ->count();

        return [
            'used' => $used,
            'limit' => 3,
            'percentage' => min(($used / 3) * 100, 100)
        ];
    }

    /**
     * Get contract usage for user
     */
    public static function getContractUsage(User $user = null): array
    {
        $user = $user ?? Auth::user();

        if (!$user) {
            return ['used' => 0, 'limit' => 0, 'percentage' => 0];
        }

        if ($user->hasRole('admin')) {
            return ['used' => 0, 'limit' => 'unlimited', 'percentage' => 0];
        }

        $currentPlan = $user->currentPlan;
        $isFreePlan = !$currentPlan || $currentPlan->slug === 'free';

        if (!$isFreePlan) {
            return ['used' => 0, 'limit' => 'unlimited', 'percentage' => 0];
        }

        $used = $user->contracts()->count();

        return [
            'used' => $used,
            'limit' => 2,
            'percentage' => min(($used / 2) * 100, 100)
        ];
    }

    /**
     * Redirect to upgrade page for specific feature
     */
    public static function redirectToUpgrade(string $feature, string $title = null, string $description = null, string $plan = 'Pro')
    {
        return redirect()->route('upgrade.required', [
            'feature' => $feature,
            'title' => $title,
            'description' => $description,
            'plan' => $plan
        ]);
    }

    /**
     * Get plan summary for current user
     */
    public static function getPlanSummary(User $user = null): array
    {
        $user = $user ?? Auth::user();

        if (!$user) {
            return [
                'plan_name' => 'Free',
                'plan_slug' => 'free'
            ];
        }

        $currentPlan = $user->currentPlan;
        $planName = $currentPlan ? ucfirst($currentPlan->slug) : 'Free';
        $planSlug = $currentPlan ? $currentPlan->slug : 'free';

        return [
            'plan_name' => $planName,
            'plan_slug' => $planSlug
        ];
    }

    /**
     * Check if user can create projects
     */
    public static function canCreateProject(User $user = null): bool
    {
        $user = $user ?? Auth::user();

        if (!$user || $user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        $isFreePlan = !$currentPlan || $currentPlan->slug === 'free';

        if (!$isFreePlan) {
            return true; // Pro/Business users can create unlimited
        }

        // Check limit for free users (10 projects max)
        $projectCount = $user->projects()->count();
        return $projectCount < 10;
    }

    /**
     * Get remaining project count for user
     */
    public static function getRemainingProjectCount(User $user = null): int
    {
        $user = $user ?? Auth::user();

        if (!$user || $user->hasRole('admin')) {
            return -1; // Unlimited
        }

        $currentPlan = $user->currentPlan;
        $isFreePlan = !$currentPlan || $currentPlan->slug === 'free';

        if (!$isFreePlan) {
            return -1; // Unlimited
        }

        $projectCount = $user->projects()->count();
        return max(0, 10 - $projectCount);
    }

    /**
     * Check if user can access project analytics
     */
    public static function canAccessProjectAnalytics(User $user = null): bool
    {
        $user = $user ?? Auth::user();

        if (!$user) {
            return false;
        }

        if ($user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        return $currentPlan && in_array($currentPlan->slug, ['pro', 'business']);
    }

    /**
     * Check if user can use advanced project features
     */
    public static function canUseAdvancedProjectFeatures(User $user = null): bool
    {
        $user = $user ?? Auth::user();

        if (!$user) {
            return false;
        }

        if ($user->hasRole('admin')) {
            return true;
        }

        $currentPlan = $user->currentPlan;
        return $currentPlan && $currentPlan->slug === 'business';
    }
}
