@props([
    'type' => 'text',
    'lines' => 3,
    'height' => null,
    'width' => null,
    'rounded' => 'md'
])

@php
$roundedClasses = [
    'none' => 'rounded-none',
    'sm' => 'rounded-sm',
    'md' => 'rounded-md',
    'lg' => 'rounded-lg',
    'xl' => 'rounded-xl',
    'full' => 'rounded-full'
];

$roundedClass = $roundedClasses[$rounded] ?? $roundedClasses['md'];
@endphp

<div class="animate-pulse {{ $attributes->get('class') }}">
    @if($type === 'text')
        @for($i = 0; $i < $lines; $i++)
            <div class="h-4 bg-gray-200 {{ $roundedClass }} mb-2 {{ $i === $lines - 1 ? 'w-3/4' : 'w-full' }}"></div>
        @endfor
    @elseif($type === 'title')
        <div class="h-6 bg-gray-200 {{ $roundedClass }} w-1/2 mb-4"></div>
    @elseif($type === 'avatar')
        <div class="w-10 h-10 bg-gray-200 rounded-full"></div>
    @elseif($type === 'button')
        <div class="h-10 bg-gray-200 {{ $roundedClass }} w-24"></div>
    @elseif($type === 'card')
        <div class="bg-white border border-gray-200 {{ $roundedClass }} p-6">
            <div class="h-4 bg-gray-200 {{ $roundedClass }} w-1/4 mb-4"></div>
            <div class="h-6 bg-gray-200 {{ $roundedClass }} w-1/2 mb-4"></div>
            <div class="space-y-2">
                <div class="h-4 bg-gray-200 {{ $roundedClass }} w-full"></div>
                <div class="h-4 bg-gray-200 {{ $roundedClass }} w-full"></div>
                <div class="h-4 bg-gray-200 {{ $roundedClass }} w-3/4"></div>
            </div>
        </div>
    @elseif($type === 'table-row')
        <tr class="border-b border-gray-200">
            <td class="px-6 py-4">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-200 rounded-full mr-4"></div>
                    <div class="space-y-2">
                        <div class="h-4 bg-gray-200 {{ $roundedClass }} w-32"></div>
                        <div class="h-3 bg-gray-200 {{ $roundedClass }} w-24"></div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4">
                <div class="space-y-2">
                    <div class="h-4 bg-gray-200 {{ $roundedClass }} w-40"></div>
                    <div class="h-3 bg-gray-200 {{ $roundedClass }} w-32"></div>
                </div>
            </td>
            <td class="px-6 py-4">
                <div class="h-6 bg-gray-200 {{ $roundedClass }} w-20"></div>
            </td>
            <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 {{ $roundedClass }} w-12"></div>
            </td>
            <td class="px-6 py-4">
                <div class="h-4 bg-gray-200 {{ $roundedClass }} w-16"></div>
            </td>
            <td class="px-6 py-4">
                <div class="flex space-x-2">
                    <div class="h-8 bg-gray-200 {{ $roundedClass }} w-16"></div>
                    <div class="h-8 bg-gray-200 {{ $roundedClass }} w-16"></div>
                </div>
            </td>
        </tr>
    @else
        <!-- Custom skeleton -->
        <div class="bg-gray-200 {{ $roundedClass }}" 
             style="{{ $height ? 'height: ' . $height . ';' : '' }}{{ $width ? 'width: ' . $width . ';' : '' }}">
        </div>
    @endif
</div>
