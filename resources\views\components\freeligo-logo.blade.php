@props(['size' => 'default', 'showText' => true, 'class' => '', 'theme' => 'default'])

@php
$sizes = [
    'sm' => 'w-6 h-6',
    'default' => 'w-8 h-8',
    'md' => 'w-10 h-10',
    'lg' => 'w-12 h-12',
    'xl' => 'w-16 h-16'
];

$logoSize = $sizes[$size] ?? $sizes['default'];
$textSize = match($size) {
    'sm' => 'text-lg',
    'default' => 'text-xl',
    'md' => 'text-2xl',
    'lg' => 'text-3xl',
    'xl' => 'text-4xl',
    default => 'text-xl'
};

// Theme-based text colors
$textColor = $theme === 'dark' || str_contains($class, 'text-white')
    ? 'text-white'
    : 'text-gray-800';
@endphp

<div class="flex items-center space-x-2 {{ $class }}">
    <!-- Logo Icon -->
    <div class="{{ $logoSize }} bg-gradient-to-br from-emerald-600 to-teal-600 rounded-xl flex items-center justify-center shadow-lg">
        <svg class="w-1/2 h-1/2 text-white" fill="currentColor" viewBox="0 0 24 24">
            <!-- Professional document/freelance icon -->
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6z"/>
            <path d="M14 2v6h6"/>
            <path d="M16 13H8"/>
            <path d="M16 17H8"/>
            <path d="M10 9H8"/>
        </svg>
    </div>
    
    @if($showText)
        <!-- Brand Text -->
        <span class="{{ $textSize }} font-heading font-bold {{ $textColor }}">
            @if($theme === 'dark' || str_contains($class, 'text-white'))
                <span class="text-emerald-400">Free</span><span class="text-white">ligo</span>
            @else
                <span class="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600">Free</span><span class="text-gray-800">ligo</span>
            @endif
        </span>
    @endif
</div>
