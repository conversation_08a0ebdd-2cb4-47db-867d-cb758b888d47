<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Upgrade Required</h1>
                <p class="text-gray-600 mt-1">Unlock more features with a higher plan</p>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            @php
                $feature = request('feature', 'feature');
                $title = request('title', 'Upgrade Required');
                $description = request('description', 'This feature requires a higher plan.');
                $requiredPlan = request('plan', 'Pro');
                
                // Define feature-specific content
                $featureContent = [
                    'invoices_limit' => [
                        'title' => 'Monthly Invoice Limit Reached',
                        'description' => 'You\'ve reached your monthly limit of 3 invoices. Upgrade to Pro for unlimited invoices and professional features.',
                        'plan' => 'Pro',
                        'benefits' => [
                            'Unlimited invoices per month',
                            'Professional invoice templates',
                            'Custom branding options',
                            'TDS reports and management'
                        ]
                    ],
                    'tds_reports' => [
                        'title' => 'TDS Reports - Pro Feature',
                        'description' => 'TDS reports are available for Pro and Business plan users. Generate comprehensive TDS reports for tax compliance.',
                        'plan' => 'Pro',
                        'benefits' => [
                            'Generate TDS reports',
                            'Download in multiple formats',
                            'Financial year wise reports',
                            'Client-wise TDS tracking'
                        ]
                    ],
                    'contracts_limit' => [
                        'title' => 'Contract Limit Reached',
                        'description' => 'You\'ve reached your contract creation limit. Upgrade for unlimited contracts and advanced features.',
                        'plan' => 'Pro',
                        'benefits' => [
                            'Unlimited contracts',
                            'Advanced contract templates',
                            'Digital signatures',
                            'Contract analytics'
                        ]
                    ],
                    'ai_assistant' => [
                        'title' => 'AI Document Assistant - Business Feature',
                        'description' => 'Get AI-powered assistance for creating and managing your documents. Available in Business plan.',
                        'plan' => 'Business',
                        'benefits' => [
                            'AI-powered document creation',
                            'Smart content suggestions',
                            'Automated document review',
                            'Advanced document analytics'
                        ]
                    ],
                    'multi_client_dashboard' => [
                        'title' => 'Multi-Client Dashboard - Business Feature',
                        'description' => 'Manage multiple clients efficiently with advanced dashboard features. Available in Business plan.',
                        'plan' => 'Business',
                        'benefits' => [
                            'Advanced client management',
                            'Multi-client overview',
                            'Client performance analytics',
                            'Bulk operations'
                        ]
                    ],
                    'portfolio_generator' => [
                        'title' => 'Portfolio Generator - Business Feature',
                        'description' => 'Create professional portfolios automatically from your work history. Available in Business plan.',
                        'plan' => 'Business',
                        'benefits' => [
                            'Automated portfolio generation',
                            'Professional templates',
                            'Client testimonials integration',
                            'Portfolio analytics'
                        ]
                    ],
                    'custom_branding' => [
                        'title' => 'Custom Branding - Pro Feature',
                        'description' => 'Add your own branding to invoices and documents. Remove Freeligo branding completely.',
                        'plan' => 'Pro',
                        'benefits' => [
                            'Custom logo on documents',
                            'Remove Freeligo branding',
                            'Custom color schemes',
                            'Professional appearance'
                        ]
                    ]
                ];
                
                $content = $featureContent[$feature] ?? [
                    'title' => $title,
                    'description' => $description,
                    'plan' => $requiredPlan,
                    'benefits' => []
                ];
            @endphp

            <x-upgrade-prompt 
                :feature="$feature"
                :title="$content['title']"
                :description="$content['description']"
                :required-plan="$content['plan']"
                :benefits="$content['benefits']"
                :show-comparison="true"
                size="full" />
        </div>
    </div>
</x-app-layout>
