<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Add New Client') }}
            </h2>
            <a href="{{ route('clients.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Clients
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-4xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('clients.store') }}"
                          x-data="{ type: 'individual', loading: false }"
                          @submit="loading = true"
                          class="animate-fadeIn">
                        @csrf

                        <!-- Client Type -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Client Type</label>
                            <div class="flex space-x-4">
                                <label class="flex items-center">
                                    <input type="radio" name="type" value="individual" x-model="type" class="mr-2" checked>
                                    <span class="text-sm text-gray-700">Individual</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="type" value="company" x-model="type" class="mr-2">
                                    <span class="text-sm text-gray-700">Company</span>
                                </label>
                            </div>
                            @error('type')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700">
                                    <span x-show="type === 'individual'">Full Name</span>
                                    <span x-show="type === 'company'">Contact Person Name</span>
                                </label>
                                <input type="text" name="name" id="name" value="{{ old('name') }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Company Name -->
                            <div x-show="type === 'company'">
                                <label for="company_name" class="block text-sm font-medium text-gray-700">Company Name</label>
                                <input type="text" name="company_name" id="company_name" value="{{ old('company_name') }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('company_name')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Email -->
                            <div>
                                <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                                <input type="email" name="email" id="email" value="{{ old('email') }}" required
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('email')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Phone -->
                            <div>
                                <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
                                <input type="text" name="phone" id="phone" value="{{ old('phone') }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('phone')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- GST Number -->
                            <div x-show="type === 'company'">
                                <label for="gst_number" class="block text-sm font-medium text-gray-700">GST Number</label>
                                <input type="text" name="gst_number" id="gst_number" value="{{ old('gst_number') }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('gst_number')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- PAN Number -->
                            <div>
                                <label for="pan_number" class="block text-sm font-medium text-gray-700">PAN Number</label>
                                <input type="text" name="pan_number" id="pan_number" value="{{ old('pan_number') }}"
                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                @error('pan_number')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <!-- Default TDS Percentage -->
                            <div>
                                <label for="default_tds_percentage" class="block text-sm font-medium text-gray-700">Default TDS Percentage</label>
                                <select name="default_tds_percentage" id="default_tds_percentage" 
                                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <option value="0" {{ old('default_tds_percentage') == '0' ? 'selected' : '' }}>0%</option>
                                    <option value="1" {{ old('default_tds_percentage') == '1' ? 'selected' : '' }}>1%</option>
                                    <option value="2" {{ old('default_tds_percentage') == '2' ? 'selected' : '' }}>2%</option>
                                    <option value="5" {{ old('default_tds_percentage') == '5' ? 'selected' : '' }}>5%</option>
                                    <option value="10" {{ old('default_tds_percentage') == '10' ? 'selected' : '' }}>10%</option>
                                </select>
                                @error('default_tds_percentage')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>

                        <!-- Address -->
                        <div class="mt-6">
                            <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                            <textarea name="address" id="address" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('address') }}</textarea>
                            @error('address')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Notes -->
                        <div class="mt-6">
                            <label for="notes" class="block text-sm font-medium text-gray-700">Notes</label>
                            <textarea name="notes" id="notes" rows="3"
                                      class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('notes') }}</textarea>
                            @error('notes')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- Submit Buttons -->
                        <div class="mt-6 flex flex-col sm:flex-row justify-end gap-3">
                            <x-ui.button href="{{ route('clients.index') }}" variant="secondary" class="w-full sm:w-auto">
                                Cancel
                            </x-ui.button>
                            <x-ui.button type="submit"
                                       variant="primary"
                                       icon="fas fa-save"
                                       :loading="loading"
                                       loading-text="Creating..."
                                       class="w-full sm:w-auto">
                                Create Client
                            </x-ui.button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
