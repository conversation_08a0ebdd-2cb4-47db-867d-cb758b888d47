<?php

namespace App\Repositories;

use App\Models\Client;
use App\Traits\HasFiltering;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class ClientRepository extends BaseRepository
{
    use HasFiltering;

    protected function getModel(): Model
    {
        return new Client();
    }

    /**
     * Get clients for a specific user with filters
     */
    public function getForUser(int $userId, Request $request = null): LengthAwarePaginator
    {
        $query = $this->model->newQuery()
            ->where('user_id', $userId)
            ->withCount(['invoices', 'contracts'])
            ->withSum(['invoices as total_revenue' => function($query) {
                $query->where('status', 'paid');
            }], 'total_amount');

        if ($request) {
            $query = $this->applyFilters($query, $request, [
                'search_fields' => ['name', 'email', 'company_name', 'phone'],
                'sort_by' => 'created_at',
                'sort_direction' => 'desc',
            ]);

            return $this->applyPagination($query, $request);
        }

        return $query->latest()->paginate(15);
    }

    /**
     * Get client with detailed relationships
     */
    public function getWithDetails(int $clientId): ?Client
    {
        return $this->model->newQuery()
            ->with([
                'invoices' => function ($query) {
                    $query->latest()->take(10);
                },
                'contracts' => function ($query) {
                    $query->latest()->take(5);
                }
            ])
            ->find($clientId);
    }

    /**
     * Get top clients by revenue for user
     */
    public function getTopClientsByRevenue(int $userId, int $limit = 10): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->withSum(['invoices as total_revenue' => function($query) {
                $query->where('status', 'paid');
            }], 'total_amount')
            ->orderByDesc('total_revenue')
            ->limit($limit)
            ->get();
    }

    /**
     * Get clients with pending invoices
     */
    public function getWithPendingInvoices(int $userId): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->whereHas('invoices', function ($query) {
                $query->where('status', 'pending');
            })
            ->with(['invoices' => function ($query) {
                $query->where('status', 'pending');
            }])
            ->get();
    }

    /**
     * Get clients with overdue invoices
     */
    public function getWithOverdueInvoices(int $userId): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->whereHas('invoices', function ($query) {
                $query->where('status', '!=', 'paid')
                      ->where('due_date', '<', now());
            })
            ->with(['invoices' => function ($query) {
                $query->where('status', '!=', 'paid')
                      ->where('due_date', '<', now());
            }])
            ->get();
    }

    /**
     * Get client statistics for user
     */
    public function getStatsForUser(int $userId): array
    {
        $baseQuery = $this->model->newQuery()->where('user_id', $userId);

        return [
            'total_count' => $baseQuery->count(),
            'active_count' => (clone $baseQuery)->whereHas('invoices', function ($query) {
                $query->where('created_at', '>=', now()->subMonths(3));
            })->count(),
            'total_revenue' => (clone $baseQuery)->withSum('invoices', 'total_amount')->get()->sum('invoices_sum_total_amount'),
            'avg_revenue_per_client' => $this->getAverageRevenuePerClient($userId),
        ];
    }

    /**
     * Get recent clients for user
     */
    public function getRecentForUser(int $userId, int $limit = 5): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Search clients by name or company
     */
    public function searchByName(int $userId, string $search): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->where(function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('company_name', 'like', "%{$search}%");
            })
            ->get();
    }

    /**
     * Get clients for dropdown/select options
     */
    public function getForSelect(int $userId): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->select('id', 'name', 'company_name', 'default_tds_percentage')
            ->orderBy('name')
            ->get();
    }

    /**
     * Get client analytics data
     */
    public function getAnalyticsData(int $userId): array
    {
        $clients = $this->model->newQuery()
            ->where('user_id', $userId)
            ->with(['invoices', 'contracts'])
            ->get();

        return $clients->map(function ($client) {
            $totalRevenue = $client->invoices->where('status', 'paid')->sum('total_amount');
            $invoiceCount = $client->invoices->count();
            
            return [
                'id' => $client->id,
                'name' => $client->name,
                'company_name' => $client->company_name,
                'total_revenue' => $totalRevenue,
                'invoice_count' => $invoiceCount,
                'avg_invoice_value' => $invoiceCount > 0 ? $totalRevenue / $invoiceCount : 0,
                'pending_amount' => $client->invoices->where('status', 'pending')->sum('total_amount'),
                'overdue_amount' => $client->invoices
                    ->where('status', '!=', 'paid')
                    ->where('due_date', '<', now())
                    ->sum('total_amount'),
                'contracts_count' => $client->contracts->count(),
                'last_invoice_date' => $client->invoices->max('created_at'),
                'client_since' => $client->created_at,
            ];
        })->toArray();
    }

    /**
     * Get clients with no recent activity
     */
    public function getInactiveClients(int $userId, int $months = 6): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->whereDoesntHave('invoices', function ($query) use ($months) {
                $query->where('created_at', '>=', now()->subMonths($months));
            })
            ->whereDoesntHave('contracts', function ($query) use ($months) {
                $query->where('created_at', '>=', now()->subMonths($months));
            })
            ->get();
    }

    /**
     * Get average revenue per client
     */
    private function getAverageRevenuePerClient(int $userId): float
    {
        $totalRevenue = $this->model->newQuery()
            ->where('user_id', $userId)
            ->withSum('invoices', 'total_amount')
            ->get()
            ->sum('invoices_sum_total_amount');

        $clientCount = $this->model->newQuery()
            ->where('user_id', $userId)
            ->count();

        return $clientCount > 0 ? $totalRevenue / $clientCount : 0;
    }
}
