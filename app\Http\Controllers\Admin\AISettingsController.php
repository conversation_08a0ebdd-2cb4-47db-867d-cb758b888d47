<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\AIServiceFactory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;

class AISettingsController extends Controller
{
    /**
     * Display AI settings page
     */
    public function index()
    {
        $currentProvider = AIServiceFactory::getCurrentProvider();
        $providers = AIServiceFactory::getAvailableProviders();
        
        // Test current provider connection
        $connectionStatus = [];
        foreach (array_keys($providers) as $provider) {
            if (AIServiceFactory::isProviderConfigured($provider)) {
                $connectionStatus[$provider] = AIServiceFactory::testProvider($provider);
            } else {
                $connectionStatus[$provider] = [
                    'success' => false,
                    'error' => 'Not configured'
                ];
            }
        }

        return view('admin.ai-settings', compact('currentProvider', 'providers', 'connectionStatus'));
    }

    /**
     * Update AI provider settings
     */
    public function updateProvider(Request $request)
    {
        $validated = $request->validate([
            'provider' => 'required|in:openai,groq',
        ]);

        // Update environment file
        $this->updateEnvFile('AI_PROVIDER', $validated['provider']);

        // Clear config cache
        Artisan::call('config:clear');

        return redirect()->back()->with('success', 'AI provider updated successfully!');
    }

    /**
     * Test AI provider connection
     */
    public function testProvider(Request $request)
    {
        $validated = $request->validate([
            'provider' => 'required|in:openai,groq',
        ]);

        $result = AIServiceFactory::testProvider($validated['provider']);

        return response()->json($result);
    }

    /**
     * Update environment file
     */
    private function updateEnvFile(string $key, string $value): void
    {
        $envFile = base_path('.env');
        $envContent = file_get_contents($envFile);

        $pattern = "/^{$key}=.*/m";
        $replacement = "{$key}={$value}";

        if (preg_match($pattern, $envContent)) {
            $envContent = preg_replace($pattern, $replacement, $envContent);
        } else {
            $envContent .= "\n{$replacement}";
        }

        file_put_contents($envFile, $envContent);
    }
}
