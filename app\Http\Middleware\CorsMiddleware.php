<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CorsMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Handle preflight requests
        if ($request->getMethod() === 'OPTIONS') {
            return response('', 200)
                ->header('Access-Control-Allow-Origin', $this->getAllowedOrigins($request))
                ->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
                ->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With, X-CSRF-TOKEN')
                ->header('Access-Control-Allow-Credentials', 'true')
                ->header('Access-Control-Max-Age', '86400');
        }

        $response = $next($request);

        // Add CORS headers to actual requests
        $response->headers->set('Access-Control-Allow-Origin', $this->getAllowedOrigins($request));
        $response->headers->set('Access-Control-Allow-Credentials', 'true');
        $response->headers->set('Access-Control-Expose-Headers', 'X-RateLimit-Limit, X-RateLimit-Remaining');

        return $response;
    }

    /**
     * Get allowed origins based on environment.
     */
    private function getAllowedOrigins(Request $request): string
    {
        $allowedOrigins = config('cors.allowed_origins', []);
        
        if (empty($allowedOrigins)) {
            // Default allowed origins based on environment
            if (app()->environment('production')) {
                $allowedOrigins = [
                    config('app.url'),
                    'https://yourdomain.com',
                    'https://www.yourdomain.com'
                ];
            } else {
                $allowedOrigins = [
                    'http://localhost:3000',
                    'http://localhost:8000',
                    'http://127.0.0.1:8000'
                ];
            }
        }

        $origin = $request->header('Origin');
        
        if (in_array($origin, $allowedOrigins)) {
            return $origin;
        }

        // Return first allowed origin as fallback
        return $allowedOrigins[0] ?? '*';
    }
}
