<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use InvalidArgumentException;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'client_id',
        'name',
        'description',
        'status',
        'priority',
        'start_date',
        'due_date',
        'completed_date',
        'budget',
        'hourly_rate',
        'billing_type',
        'is_billable',
        'notes',
        'custom_fields',
        'progress_percentage',
    ];

    protected function casts(): array
    {
        return [
            'start_date' => 'date',
            'due_date' => 'date',
            'completed_date' => 'date',
            'budget' => 'decimal:2',
            'hourly_rate' => 'decimal:2',
            'is_billable' => 'boolean',
            'custom_fields' => 'array',
            'progress_percentage' => 'decimal:2',
        ];
    }

    /**
     * Get the user that owns the project.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the traditional client for the project.
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(Client::class);
    }



    /**
     * Get the tasks for the project.
     */
    public function tasks(): HasMany
    {
        return $this->hasMany(Task::class);
    }

    /**
     * Get the time entries for the project.
     */
    public function timeEntries(): HasMany
    {
        return $this->hasMany(TimeEntry::class);
    }

    /**
     * Get the project members.
     */
    public function members(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'project_members')
            ->withPivot(['role', 'hourly_rate', 'can_track_time', 'can_manage_tasks', 'can_view_reports', 'joined_at', 'left_at'])
            ->withTimestamps();
    }

    /**
     * Get the project member records.
     */
    public function projectMembers(): HasMany
    {
        return $this->hasMany(ProjectMember::class);
    }

    /**
     * Scope a query to only include active projects.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope a query to only include completed projects.
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope a query to only include overdue projects.
     */
    public function scopeOverdue($query)
    {
        return $query->where('due_date', '<', now())
            ->whereNotIn('status', ['completed', 'cancelled']);
    }

    /**
     * Get total hours tracked for this project.
     */
    public function getTotalHoursAttribute(): float
    {
        return $this->timeEntries()
            ->whereNotNull('end_time')
            ->sum('duration_minutes') / 60;
    }

    /**
     * Get total billable hours for this project.
     */
    public function getBillableHoursAttribute(): float
    {
        return $this->timeEntries()
            ->where('is_billable', true)
            ->whereNotNull('end_time')
            ->sum('duration_minutes') / 60;
    }

    /**
     * Get total earned amount for this project.
     */
    public function getTotalEarnedAttribute(): float
    {
        $billableEntries = $this->timeEntries()
            ->where('is_billable', true)
            ->whereNotNull('end_time')
            ->get();

        return $billableEntries->sum(function ($entry) {
            $rate = $entry->hourly_rate ?? $this->hourly_rate ?? 0;
            return ($entry->duration_minutes / 60) * $rate;
        });
    }

    /**
     * Get completion percentage based on tasks.
     */
    public function getTaskCompletionPercentageAttribute(): float
    {
        $totalTasks = $this->tasks()->count();
        if ($totalTasks === 0) {
            return 0;
        }

        $completedTasks = $this->tasks()->where('status', 'completed')->count();
        return round(($completedTasks / $totalTasks) * 100, 2);
    }

    /**
     * Check if project is overdue.
     */
    public function getIsOverdueAttribute(): bool
    {
        return $this->due_date &&
               $this->due_date->isPast() &&
               !in_array($this->status, ['completed', 'cancelled']);
    }

    /**
     * Get days until due date.
     */
    public function getDaysUntilDueAttribute(): ?int
    {
        if (!$this->due_date) {
            return null;
        }

        return now()->diffInDays($this->due_date, false);
    }

    /**
     * Get client name.
     */
    public function getClientNameAttribute(): string
    {
        return $this->client?->display_name ?? 'Unknown Client';
    }

    /**
     * Get client email.
     */
    public function getClientEmailAttribute(): ?string
    {
        return $this->client?->email;
    }


}
