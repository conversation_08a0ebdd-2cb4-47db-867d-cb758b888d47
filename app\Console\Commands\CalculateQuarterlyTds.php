<?php

namespace App\Console\Commands;

use App\Services\TdsService;
use App\Models\User;
use Illuminate\Console\Command;

class CalculateQuarterlyTds extends Command
{
    protected $signature = 'tds:calculate-quarterly {--quarter=} {--year=}';
    protected $description = 'Calculate quarterly TDS reports for all users';

    protected $tdsService;

    public function __construct(TdsService $tdsService)
    {
        parent::__construct();
        $this->tdsService = $tdsService;
    }

    public function handle(): int
    {
        $quarter = $this->option('quarter');
        $year = $this->option('year');

        if (!$quarter || !$year) {
            $currentQuarter = $this->tdsService->getCurrentQuarter();
            $quarter = $quarter ?: $currentQuarter['quarter'];
            $year = $year ?: $currentQuarter['year'];
        }

        $this->info("Calculating quarterly TDS reports for {$quarter} {$year}...");

        $users = User::whereHas('tdsRecords')->get();
        $processed = 0;
        $failed = 0;

        foreach ($users as $user) {
            try {
                $report = $this->tdsService->generateQuarterlyReport($user->id, $quarter, $year);
                
                $this->line("Generated report for {$user->name}: ₹{$report['total_tds_deducted']} TDS");
                $processed++;
                
                if (!empty($report['compliance_status']['compliance_issues'])) {
                    $this->warn("  Compliance issues found for {$user->name}");
                }
            } catch (\Exception $e) {
                $this->error("Failed to generate report for {$user->name}: {$e->getMessage()}");
                $failed++;
            }
        }

        $this->info("Processed {$processed} reports successfully, {$failed} failed.");

        return Command::SUCCESS;
    }
}
