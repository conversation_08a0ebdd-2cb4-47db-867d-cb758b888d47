<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\File;
use App\Services\FileService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\JsonResponse;

class FileManagementController extends Controller
{
    public function __construct(
        private FileService $fileService
    ) {}

    /**
     * Display a listing of all files.
     */
    public function index(Request $request): View
    {
        $type = $request->get('type');
        $search = $request->get('search');

        $files = $this->fileService->getAllFiles($type, $search);

        // Get overall statistics
        $stats = [
            'total_files' => File::count(),
            'total_size' => File::sum('size'),
            'total_users_with_files' => File::distinct('user_id')->count(),
            'images_count' => File::byType('images')->count(),
            'documents_count' => File::byType('documents')->count(),
        ];

        return view('admin.files.index', compact('files', 'stats', 'type', 'search'));
    }

    /**
     * Display the specified file.
     */
    public function show(File $file): View
    {
        $file->load('user');
        return view('admin.files.show', compact('file'));
    }

    /**
     * Update the specified file.
     */
    public function update(Request $request, File $file): RedirectResponse
    {
        $request->validate([
            'description' => 'nullable|string|max:500',
            'is_public' => 'boolean',
        ]);

        $this->fileService->updateFile($file, $request->only(['description', 'is_public']));

        return back()->with('success', 'File updated successfully.');
    }

    /**
     * Download the specified file.
     */
    public function download(File $file)
    {
        return $this->fileService->downloadFile($file);
    }

    /**
     * Remove the specified file.
     */
    public function destroy(File $file): JsonResponse|RedirectResponse
    {
        try {
            $this->fileService->deleteFile($file);

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'File deleted successfully.'
                ]);
            }

            return back()->with('success', 'File deleted successfully.');
        } catch (\Exception $e) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete file.'
                ], 500);
            }

            return back()->with('error', 'Failed to delete file.');
        }
    }

    /**
     * Get file statistics for dashboard.
     */
    public function stats(): JsonResponse
    {
        $stats = [
            'total_files' => File::count(),
            'total_size_mb' => round(File::sum('size') / (1024 * 1024), 2),
            'files_today' => File::whereDate('created_at', today())->count(),
            'files_this_month' => File::whereMonth('created_at', now()->month)->count(),
            'top_uploaders' => File::selectRaw('user_id, COUNT(*) as file_count')
                ->with('user:id,name,email')
                ->groupBy('user_id')
                ->orderByDesc('file_count')
                ->limit(5)
                ->get(),
        ];

        return response()->json($stats);
    }
}
