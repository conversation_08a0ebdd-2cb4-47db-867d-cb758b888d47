<?php

namespace App\Jobs;

use App\Models\Workflow;
use App\Models\WorkflowMetric;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessWorkflowMetricsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 90;
    public $tries = 2;
    public $backoff = [20, 40];

    protected int $workflowId;
    protected array $metricsData;

    public function __construct(int $workflowId, array $metricsData)
    {
        $this->workflowId = $workflowId;
        $this->metricsData = $metricsData;
        $this->onQueue('analytics');
    }

    public function handle(): void
    {
        $workflow = Workflow::find($this->workflowId);
        
        if (!$workflow) {
            Log::warning('Workflow not found for metrics update', ['workflow_id' => $this->workflowId]);
            return;
        }

        // Update workflow metrics
        WorkflowMetric::updateMetricsForWorkflow($workflow, $this->metricsData);

        // Update workflow success rate if execution data provided
        if (isset($this->metricsData['success'])) {
            if ($this->metricsData['success']) {
                $workflow->recordSuccess();
            } else {
                $workflow->recordFailure($this->metricsData['error'] ?? 'Unknown error');
            }
        }

        Log::info('Workflow metrics updated', [
            'workflow_id' => $this->workflowId,
            'execution_id' => $this->metricsData['execution_id'] ?? null,
            'success' => $this->metricsData['success'] ?? null
        ]);
    }

    public function failed(Exception $exception): void
    {
        Log::error('Workflow metrics job failed', [
            'workflow_id' => $this->workflowId,
            'metrics_data' => $this->metricsData,
            'error' => $exception->getMessage()
        ]);
    }

    public function tags(): array
    {
        return ['workflow-metrics', "workflow:{$this->workflowId}"];
    }
}
