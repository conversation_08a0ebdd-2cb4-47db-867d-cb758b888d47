<?php

namespace App\Jobs;

use App\Models\RecurringInvoice;
use App\Services\RecurringInvoiceService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessRecurringInvoicesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;
    public $maxExceptions = 3;
    public $backoff = [30, 60, 120]; // Exponential backoff in seconds

    protected ?int $recurringInvoiceId;
    protected array $options;

    /**
     * Create a new job instance.
     */
    public function __construct(?int $recurringInvoiceId = null, array $options = [])
    {
        $this->recurringInvoiceId = $recurringInvoiceId;
        $this->options = $options;
        
        // Set queue based on priority
        $this->onQueue($options['priority'] ?? 'default');
    }

    /**
     * Execute the job.
     */
    public function handle(RecurringInvoiceService $service): void
    {
        try {
            if ($this->recurringInvoiceId) {
                // Process specific recurring invoice
                $this->processSingleRecurringInvoice($service);
            } else {
                // Process all ready recurring invoices
                $this->processAllRecurringInvoices($service);
            }
        } catch (Exception $e) {
            Log::error('Recurring invoice processing job failed', [
                'recurring_invoice_id' => $this->recurringInvoiceId,
                'attempt' => $this->attempts(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * Process a single recurring invoice.
     */
    protected function processSingleRecurringInvoice(RecurringInvoiceService $service): void
    {
        $recurringInvoice = RecurringInvoice::find($this->recurringInvoiceId);
        
        if (!$recurringInvoice) {
            Log::warning('Recurring invoice not found for processing', [
                'recurring_invoice_id' => $this->recurringInvoiceId
            ]);
            return;
        }

        if (!$recurringInvoice->isReadyForGeneration()) {
            Log::info('Recurring invoice not ready for generation', [
                'recurring_invoice_id' => $this->recurringInvoiceId,
                'next_generation_date' => $recurringInvoice->next_generation_date,
                'status' => $recurringInvoice->status
            ]);
            return;
        }

        $result = $service->generateInvoiceFromRecurring($recurringInvoice);
        
        if ($result) {
            Log::info('Recurring invoice processed successfully', [
                'recurring_invoice_id' => $this->recurringInvoiceId,
                'generated_invoice_id' => $result->id
            ]);

            // Dispatch follow-up jobs if needed
            $this->dispatchFollowUpJobs($result, $recurringInvoice);
        }
    }

    /**
     * Process all ready recurring invoices.
     */
    protected function processAllRecurringInvoices(RecurringInvoiceService $service): void
    {
        $results = $service->generateScheduledInvoices();
        
        Log::info('Batch recurring invoice processing completed', [
            'generated' => $results['generated'],
            'failed' => $results['failed'],
            'auto_sent' => $results['auto_sent'],
            'retried' => $results['retried'],
            'paused' => $results['paused']
        ]);

        // Dispatch individual jobs for failed invoices that need retry
        if (!empty($results['errors'])) {
            $this->dispatchRetryJobs($results['errors']);
        }
    }

    /**
     * Dispatch follow-up jobs for generated invoice.
     */
    protected function dispatchFollowUpJobs($invoice, RecurringInvoice $recurringInvoice): void
    {
        // Auto-send if enabled
        if ($recurringInvoice->auto_send) {
            ProcessInvoiceAutoSendJob::dispatch($invoice->id)
                ->onQueue('high')
                ->delay(now()->addMinutes(2));
        }

        // Schedule smart reminders
        ProcessSmartRemindersJob::dispatch($invoice->id)
            ->onQueue('default')
            ->delay(now()->addMinutes(5));

        // Calculate TDS if applicable
        if ($invoice->tds_applicable) {
            ProcessTdsCalculationJob::dispatch($invoice->id)
                ->onQueue('default')
                ->delay(now()->addMinutes(1));
        }

        // Trigger workflow automation
        ProcessWorkflowTriggerJob::dispatch('invoice_created', [
            'invoice_id' => $invoice->id,
            'recurring_invoice_id' => $recurringInvoice->id
        ])->onQueue('default')->delay(now()->addMinutes(3));
    }

    /**
     * Dispatch retry jobs for failed recurring invoices.
     */
    protected function dispatchRetryJobs(array $errors): void
    {
        foreach ($errors as $error) {
            if (isset($error['recurring_invoice_id'])) {
                // Retry with exponential backoff
                $delay = min(pow(2, $this->attempts()) * 30, 3600); // Max 1 hour delay
                
                static::dispatch($error['recurring_invoice_id'], ['priority' => 'high'])
                    ->onQueue('retry')
                    ->delay(now()->addSeconds($delay));
            }
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('Recurring invoice job permanently failed', [
            'recurring_invoice_id' => $this->recurringInvoiceId,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage()
        ]);

        // Mark recurring invoice as having issues if specific ID provided
        if ($this->recurringInvoiceId) {
            $recurringInvoice = RecurringInvoice::find($this->recurringInvoiceId);
            if ($recurringInvoice) {
                $recurringInvoice->recordFailure($exception->getMessage());
            }
        }

        // Dispatch notification to admin
        ProcessAdminNotificationJob::dispatch([
            'type' => 'recurring_invoice_failure',
            'recurring_invoice_id' => $this->recurringInvoiceId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ])->onQueue('notifications');
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        $tags = ['recurring-invoices'];
        
        if ($this->recurringInvoiceId) {
            $tags[] = "recurring-invoice:{$this->recurringInvoiceId}";
        }
        
        return $tags;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return $this->backoff;
    }

    /**
     * Determine if the job should be retried.
     */
    public function shouldRetry(Exception $exception): bool
    {
        // Don't retry for certain types of exceptions
        $nonRetryableExceptions = [
            \Illuminate\Database\Eloquent\ModelNotFoundException::class,
            \InvalidArgumentException::class,
        ];

        foreach ($nonRetryableExceptions as $exceptionClass) {
            if ($exception instanceof $exceptionClass) {
                return false;
            }
        }

        return true;
    }
}
