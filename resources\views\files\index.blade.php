<x-sidebar-app>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                    {{ __('File Manager') }}
                </h2>
                <p class="text-sm text-gray-600 mt-1">Manage your uploaded files and documents</p>
            </div>
            <button onclick="openFileUploadModal()" 
                    class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200 flex items-center">
                <i class="fas fa-upload mr-2"></i>
                Upload File
            </button>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Storage Usage Card -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-medium text-gray-900">Storage Usage</h3>
                    <span class="text-sm text-gray-500">
                        {{ $storageUsage['used_mb'] }} MB 
                        @if($storageUsage['limit_mb'])
                            / {{ $storageUsage['limit_mb'] }} MB
                        @endif
                    </span>
                </div>
                
                @if($storageUsage['limit_mb'])
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-blue-600 h-2 rounded-full" style="width: {{ min($storageUsage['percentage'], 100) }}%"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">{{ $storageUsage['percentage'] }}% used</p>
                @else
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-green-600 h-2 rounded-full" style="width: 10%"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-1">Unlimited storage</p>
                @endif
            </div>

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-blue-100 rounded-lg">
                            <i class="fas fa-file text-blue-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Files</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $stats['total_files'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-green-100 rounded-lg">
                            <i class="fas fa-image text-green-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Images</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $stats['images_count'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-yellow-100 rounded-lg">
                            <i class="fas fa-file-alt text-yellow-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Documents</p>
                            <p class="text-2xl font-bold text-gray-900">{{ $stats['documents_count'] }}</p>
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center">
                        <div class="p-2 bg-purple-100 rounded-lg">
                            <i class="fas fa-hdd text-purple-600"></i>
                        </div>
                        <div class="ml-4">
                            <p class="text-sm font-medium text-gray-600">Total Size</p>
                            <p class="text-2xl font-bold text-gray-900">{{ number_format($stats['total_size'] / (1024 * 1024), 1) }} MB</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
                <form method="GET" action="{{ route('files.index') }}" class="flex flex-col sm:flex-row gap-4">
                    <div class="flex-1">
                        <input type="text" 
                               name="search" 
                               value="{{ $search }}"
                               placeholder="Search files..." 
                               class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <select name="type" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            <option value="">All Types</option>
                            <option value="images" {{ $type === 'images' ? 'selected' : '' }}>Images</option>
                            <option value="documents" {{ $type === 'documents' ? 'selected' : '' }}>Documents</option>
                        </select>
                    </div>
                    <button type="submit" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                        <i class="fas fa-search mr-2"></i>Filter
                    </button>
                    @if($search || $type)
                        <a href="{{ route('files.index') }}" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg transition-colors duration-200">
                            Clear
                        </a>
                    @endif
                </form>
            </div>

            <!-- Files Grid -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                @if($files->count() > 0)
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-6">
                        @foreach($files as $file)
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow duration-200">
                                <!-- File Icon/Preview -->
                                <div class="flex justify-center mb-3">
                                    @if($file->isImage())
                                        <img src="{{ $file->url }}" alt="{{ $file->name }}" class="w-16 h-16 object-cover rounded">
                                    @else
                                        <div class="w-16 h-16 flex items-center justify-center">
                                            <i class="{{ $file->icon }} text-3xl"></i>
                                        </div>
                                    @endif
                                </div>

                                <!-- File Info -->
                                <div class="text-center">
                                    <h4 class="font-medium text-gray-900 text-sm truncate" title="{{ $file->name }}">
                                        {{ $file->name }}
                                    </h4>
                                    <p class="text-xs text-gray-500 mt-1">{{ $file->human_size }}</p>
                                    <p class="text-xs text-gray-400">{{ $file->created_at->format('M j, Y') }}</p>
                                </div>

                                <!-- Actions -->
                                <div class="flex justify-center space-x-2 mt-3">
                                    <a href="{{ route('files.show', $file) }}" 
                                       class="text-blue-600 hover:text-blue-800 text-sm">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('files.download', $file) }}" 
                                       class="text-green-600 hover:text-green-800 text-sm">
                                        <i class="fas fa-download"></i>
                                    </a>
                                    <button onclick="deleteFile({{ $file->id }})" 
                                            class="text-red-600 hover:text-red-800 text-sm">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <!-- Pagination -->
                    <div class="px-6 py-4 border-t border-gray-200">
                        {{ $files->links() }}
                    </div>
                @else
                    <div class="text-center py-12">
                        <i class="fas fa-folder-open text-gray-400 text-6xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No files found</h3>
                        <p class="text-gray-500 mb-4">
                            @if($search || $type)
                                No files match your search criteria.
                            @else
                                You haven't uploaded any files yet.
                            @endif
                        </p>
                        <button onclick="openFileUploadModal()" 
                                class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-upload mr-2"></i>Upload Your First File
                        </button>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- File Upload Modal -->
    @include('files.partials.upload-modal')

    @push('scripts')
    <script>
        function openFileUploadModal() {
            document.getElementById('fileUploadModal').classList.remove('hidden');
        }

        function closeFileUploadModal() {
            document.getElementById('fileUploadModal').classList.add('hidden');
            document.getElementById('uploadForm').reset();
        }

        function deleteFile(fileId) {
            if (confirm('Are you sure you want to delete this file?')) {
                fetch(`/files/${fileId}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                        'Accept': 'application/json',
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showToast(data.message, 'success');
                        location.reload();
                    } else {
                        showToast(data.message, 'error');
                    }
                })
                .catch(error => {
                    showToast('An error occurred while deleting the file.', 'error');
                });
            }
        }
    </script>
    @endpush
</x-sidebar-app>
