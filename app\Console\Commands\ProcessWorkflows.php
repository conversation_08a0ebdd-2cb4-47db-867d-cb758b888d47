<?php

namespace App\Console\Commands;

use App\Services\WorkflowAutomationService;
use Illuminate\Console\Command;

class ProcessWorkflows extends Command
{
    protected $signature = 'workflows:process {--trigger=}';
    protected $description = 'Process automated workflows';

    protected $workflowService;

    public function __construct(WorkflowAutomationService $workflowService)
    {
        parent::__construct();
        $this->workflowService = $workflowService;
    }

    public function handle(): int
    {
        $trigger = $this->option('trigger');

        if ($trigger) {
            $this->info("Processing workflows for trigger: {$trigger}");
            $results = $this->workflowService->processWorkflowsByTrigger($trigger);
        } else {
            $this->info('Processing all workflow triggers...');
            $triggers = ['invoice_overdue', 'contract_expiring', 'payment_received'];
            $results = [
                'processed' => 0,
                'failed' => 0,
                'errors' => []
            ];

            foreach ($triggers as $triggerType) {
                $this->line("Processing {$triggerType} workflows...");
                $triggerResults = $this->workflowService->processWorkflowsByTrigger($triggerType);
                
                $results['processed'] += $triggerResults['processed'];
                $results['failed'] += $triggerResults['failed'];
                $results['errors'] = array_merge($results['errors'], $triggerResults['errors']);
            }
        }

        $this->info("Processed {$results['processed']} workflows successfully.");
        
        if ($results['failed'] > 0) {
            $this->warn("Failed to process {$results['failed']} workflows.");
            foreach ($results['errors'] as $error) {
                $this->error("Error: {$error['error']}");
            }
        }

        return Command::SUCCESS;
    }
}
