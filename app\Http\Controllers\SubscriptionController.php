<?php

namespace App\Http\Controllers;

use App\Models\Plan;
use App\Models\UserSubscription;
use App\Models\Payment;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class SubscriptionController extends Controller
{
    /**
     * Show subscription management page.
     */
    public function index()
    {
        $user = Auth::user();
        $currentSubscription = $user->activeSubscription()->with('plan')->first();
        $plans = Plan::active()->ordered()->with('planFeatures')->get();

        return view('subscriptions.index', compact('currentSubscription', 'plans'));
    }

    /**
     * Show plan selection page.
     */
    public function plans()
    {
        $plans = Plan::active()->ordered()->with('planFeatures')->get();
        return view('subscriptions.plans', compact('plans'));
    }

    /**
     * Subscribe to a plan.
     */
    public function subscribe(Request $request, Plan $plan)
    {
        $user = Auth::user();

        // Check if user already has an active subscription
        if ($user->hasActiveSubscription()) {
            return redirect()->route('subscriptions.index')
                           ->with('error', 'You already have an active subscription.');
        }

        // For free plan, create subscription immediately
        if ($plan->isFree()) {
            return $this->createFreeSubscription($user, $plan);
        }

        // For paid plans, redirect to payment
        return $this->initiatePayment($user, $plan);
    }

    /**
     * Upgrade subscription to a different plan.
     */
    public function upgrade(Request $request, Plan $plan)
    {
        $user = Auth::user();
        $currentSubscription = $user->activeSubscription()->first();

        if (!$currentSubscription) {
            return redirect()->route('subscriptions.plans')
                           ->with('error', 'No active subscription found.');
        }

        if ($currentSubscription->plan_id === $plan->id) {
            return redirect()->route('subscriptions.index')
                           ->with('error', 'You are already on this plan.');
        }

        // For free plan downgrades
        if ($plan->isFree()) {
            return $this->downgradeToPlan($currentSubscription, $plan);
        }

        // For paid plan upgrades
        return $this->initiateUpgradePayment($currentSubscription, $plan);
    }

    /**
     * Cancel subscription.
     */
    public function cancel()
    {
        $user = Auth::user();
        $subscription = $user->activeSubscription()->first();

        if (!$subscription) {
            return redirect()->route('subscriptions.index')
                           ->with('error', 'No active subscription found.');
        }

        $subscription->cancel();

        // Downgrade to free plan
        $freePlan = Plan::where('slug', 'free')->first();
        if ($freePlan) {
            $this->createFreeSubscription($user, $freePlan);
        }

        return redirect()->route('subscriptions.index')
                        ->with('success', 'Subscription cancelled successfully. You have been moved to the free plan.');
    }

    /**
     * Show billing history.
     */
    public function billing()
    {
        $user = Auth::user();
        $payments = $user->payments()->with('userSubscription.plan')->latest()->paginate(15);

        return view('subscriptions.billing', compact('payments'));
    }

    /**
     * Create free subscription.
     */
    private function createFreeSubscription($user, $plan)
    {
        DB::transaction(function () use ($user, $plan) {
            // Cancel any existing subscriptions
            $user->subscriptions()->where('status', 'active')->update(['status' => 'cancelled']);

            // Create new subscription
            $subscription = UserSubscription::create([
                'user_id' => $user->id,
                'plan_id' => $plan->id,
                'status' => 'active',
                'starts_at' => now(),
                'amount_paid' => 0,
                'currency' => 'INR',
            ]);

            // Update user's current plan
            $user->update(['current_plan_id' => $plan->id]);

            // Reset usage stats
            $user->resetMonthlyUsage();
        });

        return redirect()->route('subscriptions.index')
                        ->with('success', 'Successfully subscribed to the free plan!');
    }

    /**
     * Initiate payment for paid plans.
     */
    private function initiatePayment($user, $plan)
    {
        // Create pending subscription
        $subscription = UserSubscription::create([
            'user_id' => $user->id,
            'plan_id' => $plan->id,
            'status' => 'pending',
            'starts_at' => now(),
            'amount_paid' => $plan->price,
            'currency' => $plan->currency,
        ]);

        // Create pending payment
        $payment = Payment::create([
            'user_id' => $user->id,
            'user_subscription_id' => $subscription->id,
            'gateway' => 'pending', // Will be updated when user selects gateway
            'amount' => $plan->price,
            'currency' => $plan->currency,
            'status' => 'pending',
            'type' => 'subscription',
        ]);

        return redirect()->route('payment.gateway', $payment)
                        ->with('info', 'Please complete the payment to activate your subscription.');
    }

    /**
     * Initiate upgrade payment.
     */
    private function initiateUpgradePayment($currentSubscription, $newPlan)
    {
        // Calculate prorated amount (simplified - just charge full amount)
        $amount = $newPlan->price;

        // Create new subscription
        $subscription = UserSubscription::create([
            'user_id' => $currentSubscription->user_id,
            'plan_id' => $newPlan->id,
            'status' => 'pending',
            'starts_at' => now(),
            'amount_paid' => $amount,
            'currency' => $newPlan->currency,
        ]);

        // Create payment
        $payment = Payment::create([
            'user_id' => $currentSubscription->user_id,
            'user_subscription_id' => $subscription->id,
            'gateway' => 'pending',
            'amount' => $amount,
            'currency' => $newPlan->currency,
            'status' => 'pending',
            'type' => 'upgrade',
        ]);

        return redirect()->route('payment.gateway', $payment)
                        ->with('info', 'Please complete the payment to upgrade your subscription.');
    }

    /**
     * Downgrade to a plan (typically free).
     */
    private function downgradeToPlan($currentSubscription, $newPlan)
    {
        DB::transaction(function () use ($currentSubscription, $newPlan) {
            // Cancel current subscription
            $currentSubscription->cancel();

            // Create new subscription
            $subscription = UserSubscription::create([
                'user_id' => $currentSubscription->user_id,
                'plan_id' => $newPlan->id,
                'status' => 'active',
                'starts_at' => now(),
                'amount_paid' => 0,
                'currency' => 'INR',
            ]);

            // Update user's current plan
            $currentSubscription->user->update(['current_plan_id' => $newPlan->id]);

            // Reset usage stats
            $currentSubscription->user->resetMonthlyUsage();
        });

        return redirect()->route('subscriptions.index')
                        ->with('success', 'Successfully downgraded to the free plan.');
    }
}
