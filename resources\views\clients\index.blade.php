<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Clients</h1>
                <p class="text-gray-600 mt-1">Manage your client relationships and information</p>
            </div>
            <a href="{{ route('clients.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <i class="fas fa-plus mr-2"></i>
                Add New Client
            </a>
        </div>
    </x-slot>

    <!-- Search and Filter -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden mb-6">
        <div class="p-6">
            <form method="GET" action="{{ route('clients.index') }}" class="flex flex-wrap gap-4">
                <div class="flex-1 min-w-64">
                    <div class="relative">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <input type="text" name="search" value="{{ request('search') }}"
                               placeholder="Search clients..."
                               class="block w-full pl-10 rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200">
                    </div>
                </div>
                <div>
                    <select name="type" class="block w-full rounded-lg border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 transition-colors duration-200">
                        <option value="">All Types</option>
                        <option value="individual" {{ request('type') === 'individual' ? 'selected' : '' }}>Individual</option>
                        <option value="company" {{ request('type') === 'company' ? 'selected' : '' }}>Company</option>
                    </select>
                </div>
                <div class="flex gap-2">
                    <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                        <i class="fas fa-search mr-2"></i>
                        Search
                    </button>
                    <a href="{{ route('clients.index') }}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- Clients Table -->
    <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-100 bg-gray-50/50">
            <div class="flex items-center justify-between">
                <h3 class="text-lg font-semibold text-gray-900">All Clients</h3>
                <span class="text-sm text-gray-500">{{ $clients->total() }} total clients</span>
            </div>
        </div>
        <div class="p-0">
            @if($clients->count() > 0)
                <div class="overflow-x-auto -mx-4 sm:mx-0">
                    <div class="inline-block min-w-full align-middle">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                                    <th class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Contact</th>
                                    <th class="hidden lg:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                    <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">TDS %</th>
                                    <th class="hidden lg:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Invoices</th>
                                    <th class="px-3 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @foreach($clients as $client)
                                <tr class="hover:bg-gray-50 transition-colors duration-200">
                                    <td class="px-3 sm:px-6 py-4">
                                        <div class="flex items-center">
                                            <div class="flex-shrink-0 h-8 w-8 sm:h-10 sm:w-10">
                                                <div class="h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 flex items-center justify-center text-white font-semibold text-sm">
                                                    {{ substr($client->name, 0, 1) }}
                                                </div>
                                            </div>
                                            <div class="ml-3 sm:ml-4 min-w-0 flex-1">
                                                <div class="text-sm font-medium text-gray-900 truncate">{{ $client->name }}</div>
                                                @if($client->company_name)
                                                    <div class="text-sm text-gray-500 truncate">{{ $client->company_name }}</div>
                                                @endif
                                                <!-- Mobile-only contact info -->
                                                <div class="md:hidden mt-1 space-y-1">
                                                    @if($client->email)
                                                        <div class="flex items-center text-xs text-gray-500">
                                                            <i class="fas fa-envelope mr-1"></i>
                                                            <span class="truncate">{{ $client->email }}</span>
                                                        </div>
                                                    @endif
                                                    @if($client->phone)
                                                        <div class="flex items-center text-xs text-gray-500">
                                                            <i class="fas fa-phone mr-1"></i>
                                                            {{ $client->phone }}
                                                        </div>
                                                    @endif
                                                    <div class="flex items-center space-x-2">
                                                        <span class="sm:hidden inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                                            @if($client->type === 'individual') bg-blue-100 text-blue-800
                                                            @else bg-orange-100 text-orange-800 @endif">
                                                            {{ ucfirst($client->type) }}
                                                        </span>
                                                        <span class="sm:hidden text-xs text-gray-500">TDS: {{ $client->default_tds_percentage }}%</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="hidden md:table-cell px-6 py-4">
                                        <div class="text-sm text-gray-900">
                                            @if($client->email)
                                                <div class="flex items-center mb-1">
                                                    <i class="fas fa-envelope text-gray-400 mr-2"></i>
                                                    <span class="truncate">{{ $client->email }}</span>
                                                </div>
                                            @endif
                                            @if($client->phone)
                                                <div class="flex items-center">
                                                    <i class="fas fa-phone text-gray-400 mr-2"></i>
                                                    {{ $client->phone }}
                                                </div>
                                            @endif
                                        </div>
                                    </td>
                                    <td class="hidden lg:table-cell px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                            @if($client->type === 'individual') bg-blue-100 text-blue-800
                                            @else bg-orange-100 text-orange-800 @endif">
                                            {{ ucfirst($client->type) }}
                                        </span>
                                    </td>
                                    <td class="hidden sm:table-cell px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $client->default_tds_percentage }}%
                                    </td>
                                    <td class="hidden lg:table-cell px-6 py-4 whitespace-nowrap">
                                        <div class="text-sm text-gray-900">
                                            <span class="font-medium">{{ $client->invoices_count }}</span>
                                            <span class="text-gray-500">invoices</span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2">
                                            <a href="{{ route('clients.show', $client) }}"
                                               class="text-blue-600 hover:text-blue-900 transition-colors duration-200">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ route('clients.edit', $client) }}"
                                               class="text-orange-600 hover:text-orange-900 transition-colors duration-200">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            @if($client->invoices_count == 0 && $client->contracts_count == 0)
                                                <button onclick="deleteClient({{ $client->id }})"
                                                        class="text-red-600 hover:text-red-900 transition-colors duration-200">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            @endif
                                        </div>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                            </table>
                        </div>

                <!-- Pagination -->
                <div class="px-6 py-4 border-t border-gray-200">
                    {{ $clients->links() }}
                </div>
            @else
                <div class="text-center py-12">
                    <i class="fas fa-users text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No clients found</h3>
                    @if(request('search') || request('type'))
                        <p class="text-gray-500 mb-4">Try adjusting your search criteria</p>
                        <a href="{{ route('clients.index') }}" class="bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2">
                            Clear Filters
                        </a>
                    @else
                        <p class="text-gray-500 mb-4">Get started by adding your first client</p>
                        <a href="{{ route('clients.create') }}" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                            <i class="fas fa-plus mr-2"></i>
                            Add Your First Client
                        </a>
                    @endif
                </div>
            @endif
                </div>
            </div>
        </div>
    </div>

    <script>
        function deleteClient(clientId) {
            confirmAction(
                'Delete Client',
                'Are you sure you want to delete this client? This action cannot be undone.',
                'Yes, delete it!'
            ).then((result) => {
                if (result.isConfirmed) {
                    // Create and submit delete form
                    const form = document.createElement('form');
                    form.method = 'POST';
                    form.action = `/clients/${clientId}`;
                    form.innerHTML = `
                        @csrf
                        @method('DELETE')
                    `;
                    document.body.appendChild(form);
                    form.submit();
                }
            });
        }
    </script>
</x-app-layout>
