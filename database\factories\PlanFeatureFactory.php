<?php

namespace Database\Factories;

use App\Models\PlanFeature;
use App\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;

class PlanFeatureFactory extends Factory
{
    protected $model = PlanFeature::class;

    public function definition(): array
    {
        return [
            'plan_id' => Plan::factory(),
            'feature_key' => fake()->randomElement([
                'invoices_limit',
                'has_watermark',
                'contracts_limit',
                'tds_reports',
                'custom_branding',
                'whatsapp_shortcuts',
                'priority_support',
                'api_access',
                'ai_assistant',
            ]),
            'feature_value' => fake()->randomElement(['true', 'false', 'unlimited', '3', '10']),
            'feature_type' => fake()->randomElement(['boolean', 'limit']),
        ];
    }

    public function boolean(): static
    {
        return $this->state(fn (array $attributes) => [
            'feature_type' => 'boolean',
            'feature_value' => fake()->randomElement(['true', 'false']),
        ]);
    }

    public function limit(): static
    {
        return $this->state(fn (array $attributes) => [
            'feature_type' => 'limit',
            'feature_value' => fake()->randomElement(['unlimited', '3', '10', '50']),
        ]);
    }

    public function unlimitedInvoices(): static
    {
        return $this->state(fn (array $attributes) => [
            'feature_key' => 'invoices_limit',
            'feature_value' => 'unlimited',
            'feature_type' => 'limit',
        ]);
    }



    public function customBranding(): static
    {
        return $this->state(fn (array $attributes) => [
            'feature_key' => 'custom_branding',
            'feature_value' => 'true',
            'feature_type' => 'boolean',
        ]);
    }

    public function tdsReports(): static
    {
        return $this->state(fn (array $attributes) => [
            'feature_key' => 'tds_reports',
            'feature_value' => 'true',
            'feature_type' => 'boolean',
        ]);
    }

    public function aiAssistant(): static
    {
        return $this->state(fn (array $attributes) => [
            'feature_key' => 'ai_assistant',
            'feature_value' => 'true',
            'feature_type' => 'boolean',
        ]);
    }

    public function apiAccess(): static
    {
        return $this->state(fn (array $attributes) => [
            'feature_key' => 'api_access',
            'feature_value' => 'true',
            'feature_type' => 'boolean',
        ]);
    }

    public function forPlan(Plan $plan): static
    {
        return $this->state(fn (array $attributes) => [
            'plan_id' => $plan->id,
        ]);
    }
}
