<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">AI Assistant</h1>
                <p class="text-gray-600 mt-1">Generate and improve your business documents with AI</p>
            </div>
            <div class="flex items-center space-x-2">
                <span class="bg-purple-100 text-purple-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
                    Business Plan Feature
                </span>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- AI Tools Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <!-- Document Generator -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center mb-4">
                        <div class="bg-blue-100 p-3 rounded-lg">
                            <i class="fas fa-file-alt text-blue-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Document Generator</h3>
                            <p class="text-sm text-gray-600">Create invoices, contracts, and proposals</p>
                        </div>
                    </div>
                    <button class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                            onclick="openGenerator('document')">
                        Generate Document
                    </button>
                </div>

                <!-- Content Analyzer -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center mb-4">
                        <div class="bg-green-100 p-3 rounded-lg">
                            <i class="fas fa-search text-green-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Content Analyzer</h3>
                            <p class="text-sm text-gray-600">Improve your existing documents</p>
                        </div>
                    </div>
                    <button class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                            onclick="openAnalyzer()">
                        Analyze Content
                    </button>
                </div>

                <!-- Smart Templates -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
                    <div class="flex items-center mb-4">
                        <div class="bg-purple-100 p-3 rounded-lg">
                            <i class="fas fa-magic text-purple-600 text-xl"></i>
                        </div>
                        <div class="ml-4">
                            <h3 class="text-lg font-semibold text-gray-900">Smart Templates</h3>
                            <p class="text-sm text-gray-600">AI-powered template suggestions</p>
                        </div>
                    </div>
                    <button class="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
                            onclick="openTemplates()">
                        Browse Templates
                    </button>
                </div>
            </div>

            <!-- Recent AI Activities -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">Recent AI Activities</h3>
                </div>
                <div class="p-6">
                    <div class="text-center py-8">
                        <i class="fas fa-robot text-gray-400 text-4xl mb-4"></i>
                        <p class="text-gray-600">No AI activities yet. Start by generating your first document!</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Document Generator Modal -->
    <div id="generatorModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">AI Document Generator</h3>
                </div>
                <div class="p-6">
                    <form id="generatorForm">
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Document Type</label>
                            <select name="type" class="w-full border border-gray-300 rounded-lg px-3 py-2">
                                <option value="invoice">Invoice</option>
                                <option value="contract">Contract</option>
                                <option value="proposal">Proposal</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Describe what you need</label>
                            <textarea name="prompt" rows="4" 
                                    class="w-full border border-gray-300 rounded-lg px-3 py-2"
                                    placeholder="E.g., Create a web development contract for a 3-month project with milestone payments..."></textarea>
                        </div>
                        <div class="flex justify-end space-x-3">
                            <button type="button" onclick="closeGenerator()" 
                                    class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium py-2 px-4 rounded-lg">
                                Cancel
                            </button>
                            <button type="submit" 
                                    class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg">
                                Generate with AI
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function openGenerator(type) {
            document.getElementById('generatorModal').classList.remove('hidden');
        }

        function closeGenerator() {
            document.getElementById('generatorModal').classList.add('hidden');
        }

        function openAnalyzer() {
            alert('Content Analyzer coming soon!');
        }

        function openTemplates() {
            alert('Smart Templates coming soon!');
        }

        document.getElementById('generatorForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = new FormData(this);
            
            fetch('{{ route("ai-assistant.generate") }}', {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': '{{ csrf_token() }}',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    type: formData.get('type'),
                    prompt: formData.get('prompt')
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.content) {
                    alert('AI generated content: ' + data.content.substring(0, 100) + '...');
                    closeGenerator();
                } else {
                    alert('Error generating content');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error generating content');
            });
        });
    </script>
</x-app-layout>
