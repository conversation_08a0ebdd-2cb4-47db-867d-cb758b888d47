<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Client permissions
            'view clients',
            'create clients',
            'edit clients',
            'delete clients',

            // Invoice permissions
            'view invoices',
            'create invoices',
            'edit invoices',
            'delete invoices',
            'send invoices',

            // Contract permissions
            'view contracts',
            'create contracts',
            'edit contracts',
            'delete contracts',
            'send contracts',

            // TDS permissions
            'view tds',
            'create tds',
            'edit tds',
            'export tds',

            // Follow-up permissions
            'view followups',
            'create followups',
            'edit followups',
            'delete followups',
            'send followups',

            // Dashboard permissions
            'view dashboard',
            'view reports',
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Create roles
        $freelancerRole = Role::firstOrCreate(['name' => 'freelancer']);
        $adminRole = Role::firstOrCreate(['name' => 'admin']);
        $clientRole = Role::firstOrCreate(['name' => 'client']);

        // Assign permissions to roles
        $freelancerRole->givePermissionTo([
            'view clients', 'create clients', 'edit clients', 'delete clients',
            'view invoices', 'create invoices', 'edit invoices', 'delete invoices', 'send invoices',
            'view contracts', 'create contracts', 'edit contracts', 'delete contracts', 'send contracts',
            'view tds', 'create tds', 'edit tds', 'export tds',
            'view followups', 'create followups', 'edit followups', 'delete followups', 'send followups',
            'view dashboard', 'view reports',
        ]);

        $adminRole->givePermissionTo(Permission::all());

        $clientRole->givePermissionTo([
            'view invoices',
            'view contracts',
        ]);

        // Create default admin user
        $adminUser = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => \Illuminate\Support\Facades\Hash::make('admin123'),
                'email_verified_at' => now(),
                'business_name' => 'Freeligo Admin',
            ]
        );

        // Assign admin role to the default admin user
        if (!$adminUser->hasRole('admin')) {
            $adminUser->assignRole('admin');
        }
    }
}
