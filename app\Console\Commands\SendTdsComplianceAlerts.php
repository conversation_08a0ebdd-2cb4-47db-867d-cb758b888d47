<?php

namespace App\Console\Commands;

use App\Services\TdsService;
use Illuminate\Console\Command;

class SendTdsComplianceAlerts extends Command
{
    protected $signature = 'tds:send-compliance-alerts';
    protected $description = 'Send TDS compliance alerts to users';

    protected $tdsService;

    public function __construct(TdsService $tdsService)
    {
        parent::__construct();
        $this->tdsService = $tdsService;
    }

    public function handle(): int
    {
        $this->info('Sending TDS compliance alerts...');

        $results = $this->tdsService->sendComplianceAlerts();

        $this->info("Sent {$results['alerts_sent']} compliance alerts successfully.");
        
        if ($results['failed'] > 0) {
            $this->warn("Failed to send {$results['failed']} alerts.");
            foreach ($results['errors'] as $error) {
                $this->error("User ID {$error['user_id']}: {$error['error']}");
            }
        }

        return Command::SUCCESS;
    }
}
