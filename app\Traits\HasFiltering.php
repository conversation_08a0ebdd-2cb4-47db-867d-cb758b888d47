<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

trait HasFiltering
{
    /**
     * Apply search filters to query
     */
    public function applySearch(Builder $query, Request $request, array $searchFields): Builder
    {
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search, $searchFields) {
                foreach ($searchFields as $field) {
                    if (str_contains($field, '.')) {
                        // Handle relationship fields
                        [$relation, $relationField] = explode('.', $field, 2);
                        $q->orWhereHas($relation, function ($relationQuery) use ($relationField, $search) {
                            $relationQuery->where($relationField, 'like', "%{$search}%");
                        });
                    } else {
                        $q->orWhere($field, 'like', "%{$search}%");
                    }
                }
            });
        }

        return $query;
    }

    /**
     * Apply status filter to query
     */
    public function applyStatusFilter(Builder $query, Request $request, string $statusField = 'status'): Builder
    {
        if ($request->filled('status')) {
            $query->where($statusField, $request->status);
        }

        return $query;
    }

    /**
     * Apply client filter to query
     */
    public function applyClientFilter(Builder $query, Request $request): Builder
    {
        if ($request->filled('client_id')) {
            $query->where('client_id', $request->client_id);
        }

        return $query;
    }

    /**
     * Apply date range filter to query
     */
    public function applyDateRangeFilter(Builder $query, Request $request, string $dateField = 'created_at'): Builder
    {
        if ($request->filled('start_date')) {
            $query->whereDate($dateField, '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->whereDate($dateField, '<=', $request->end_date);
        }

        return $query;
    }

    /**
     * Apply user scope filter
     */
    public function applyUserScope(Builder $query, int $userId): Builder
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Apply sorting to query
     */
    public function applySorting(Builder $query, Request $request, string $defaultSort = 'created_at', string $defaultDirection = 'desc'): Builder
    {
        $sortBy = $request->get('sort_by', $defaultSort);
        $sortDirection = $request->get('sort_direction', $defaultDirection);

        // Validate sort direction
        if (!in_array(strtolower($sortDirection), ['asc', 'desc'])) {
            $sortDirection = $defaultDirection;
        }

        return $query->orderBy($sortBy, $sortDirection);
    }

    /**
     * Apply pagination
     */
    public function applyPagination(Builder $query, Request $request, int $defaultPerPage = 15)
    {
        $perPage = $request->get('per_page', $defaultPerPage);
        
        // Validate per page value
        if (!is_numeric($perPage) || $perPage < 1 || $perPage > 100) {
            $perPage = $defaultPerPage;
        }

        return $query->paginate($perPage);
    }

    /**
     * Apply multiple filters at once
     */
    public function applyFilters(Builder $query, Request $request, array $config = []): Builder
    {
        $config = array_merge([
            'search_fields' => [],
            'status_field' => 'status',
            'date_field' => 'created_at',
            'user_id' => null,
            'sort_by' => 'created_at',
            'sort_direction' => 'desc',
        ], $config);

        // Apply user scope if provided
        if ($config['user_id']) {
            $query = $this->applyUserScope($query, $config['user_id']);
        }

        // Apply search
        if (!empty($config['search_fields'])) {
            $query = $this->applySearch($query, $request, $config['search_fields']);
        }

        // Apply status filter
        $query = $this->applyStatusFilter($query, $request, $config['status_field']);

        // Apply client filter
        $query = $this->applyClientFilter($query, $request);

        // Apply date range filter
        $query = $this->applyDateRangeFilter($query, $request, $config['date_field']);

        // Apply sorting
        $query = $this->applySorting($query, $request, $config['sort_by'], $config['sort_direction']);

        return $query;
    }
}
