<?php

namespace App\Jobs;

use App\Models\Client;
use App\Services\ClientAnalyticsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessClientAnalyticsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 120;
    public $tries = 2;
    public $backoff = [30, 60];

    protected int $clientId;
    protected array $eventData;

    public function __construct(int $clientId, array $eventData)
    {
        $this->clientId = $clientId;
        $this->eventData = $eventData;
        $this->onQueue('analytics');
    }

    public function handle(): void
    {
        $client = Client::find($this->clientId);
        
        if (!$client) {
            Log::warning('Client not found for analytics update', ['client_id' => $this->clientId]);
            return;
        }

        // Update client behavior data
        $behaviorData = $client->behavior_data ?? [];
        $eventType = $this->eventData['event'] ?? 'unknown';
        
        // Initialize event tracking if not exists
        if (!isset($behaviorData[$eventType])) {
            $behaviorData[$eventType] = [
                'count' => 0,
                'last_occurrence' => null,
                'frequency' => 0
            ];
        }

        // Update event data
        $behaviorData[$eventType]['count']++;
        $behaviorData[$eventType]['last_occurrence'] = now()->toISOString();
        
        // Calculate frequency (events per month)
        $firstEvent = $behaviorData[$eventType]['first_occurrence'] ?? now()->toISOString();
        $monthsSinceFirst = max(1, now()->diffInMonths($firstEvent));
        $behaviorData[$eventType]['frequency'] = $behaviorData[$eventType]['count'] / $monthsSinceFirst;

        // Set first occurrence if not set
        if (!isset($behaviorData[$eventType]['first_occurrence'])) {
            $behaviorData[$eventType]['first_occurrence'] = now()->toISOString();
        }

        // Update client
        $client->update(['behavior_data' => $behaviorData]);

        Log::info('Client analytics updated', [
            'client_id' => $this->clientId,
            'event_type' => $eventType,
            'event_count' => $behaviorData[$eventType]['count']
        ]);
    }

    public function failed(Exception $exception): void
    {
        Log::error('Client analytics job failed', [
            'client_id' => $this->clientId,
            'event_data' => $this->eventData,
            'error' => $exception->getMessage()
        ]);
    }

    public function tags(): array
    {
        return ['client-analytics', "client:{$this->clientId}", 'event:' . ($this->eventData['event'] ?? 'unknown')];
    }
}
