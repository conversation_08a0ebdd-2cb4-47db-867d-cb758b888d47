@props([
    'variant' => 'default',
    'size' => 'md',
    'icon' => null
])

@php
    $variants = [
        'default' => 'bg-gray-100 text-gray-800',
        'primary' => 'bg-blue-100 text-blue-800',
        'success' => 'bg-green-100 text-green-800',
        'warning' => 'bg-orange-100 text-orange-800',
        'danger' => 'bg-red-100 text-red-800',
        'info' => 'bg-cyan-100 text-cyan-800',
        'purple' => 'bg-purple-100 text-purple-800'
    ];
    
    $sizes = [
        'sm' => 'px-2 py-1 text-xs',
        'md' => 'px-2.5 py-1 text-xs',
        'lg' => 'px-3 py-1.5 text-sm'
    ];
    
    $classes = 'inline-flex items-center font-semibold rounded-full ' . $variants[$variant] . ' ' . $sizes[$size];
@endphp

<span {{ $attributes->merge(['class' => $classes]) }}>
    @if($icon)
        <i class="{{ $icon }} {{ $slot->isNotEmpty() ? 'mr-1' : '' }}"></i>
    @endif
    {{ $slot }}
</span>
