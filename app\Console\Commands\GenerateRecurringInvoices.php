<?php

namespace App\Console\Commands;

use App\Services\RecurringInvoiceService;
use Illuminate\Console\Command;

class GenerateRecurringInvoices extends Command
{
    protected $signature = 'invoices:generate-recurring';
    protected $description = 'Generate invoices from recurring invoice templates';

    protected RecurringInvoiceService $recurringInvoiceService;

    public function __construct(RecurringInvoiceService $recurringInvoiceService)
    {
        parent::__construct();
        $this->recurringInvoiceService = $recurringInvoiceService;
    }

    public function handle(): int
    {
        $this->info('Starting recurring invoice generation...');

        $results = $this->recurringInvoiceService->generateScheduledInvoices();

        $this->info("Generated {$results['generated']} invoices successfully.");
        
        if ($results['failed'] > 0) {
            $this->warn("Failed to generate {$results['failed']} invoices.");
            foreach ($results['errors'] as $error) {
                $this->error("Recurring ID {$error['recurring_id']}: {$error['error']}");
            }
        }

        return Command::SUCCESS;
    }
}
