<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Workflow Templates Table
        Schema::create('workflow_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->string('category'); // business_process, client_management, financial, etc.
            $table->string('trigger_type');
            $table->json('default_conditions')->nullable();
            $table->json('default_actions');
            $table->json('required_variables')->nullable(); // Variables user must provide
            $table->json('optional_variables')->nullable(); // Optional customizations
            $table->boolean('is_system_template')->default(false); // System vs user-created
            $table->boolean('is_active')->default(true);
            $table->integer('usage_count')->default(0);
            $table->decimal('average_rating', 3, 2)->nullable();
            $table->json('tags')->nullable(); // For categorization and search
            $table->text('setup_instructions')->nullable();
            $table->json('preview_data')->nullable(); // Sample data for preview
            $table->timestamps();
            
            $table->index(['category', 'trigger_type']);
            $table->index(['is_system_template', 'is_active']);
        });

        // Workflow Steps Table (for complex multi-step workflows)
        Schema::create('workflow_steps', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workflow_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->integer('step_order');
            $table->string('step_type'); // action, condition, delay, parallel, etc.
            $table->json('step_config'); // Configuration for this step
            $table->json('conditions')->nullable(); // Conditions to execute this step
            $table->string('status')->default('active'); // active, inactive, skipped
            $table->boolean('is_required')->default(true);
            $table->integer('retry_attempts')->default(0);
            $table->integer('max_retries')->default(3);
            $table->json('dependencies')->nullable(); // Other steps this depends on
            $table->timestamps();
            
            $table->index(['workflow_id', 'step_order']);
            $table->index(['step_type', 'status']);
        });

        // Workflow Triggers Table (for advanced trigger management)
        Schema::create('workflow_triggers', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('trigger_type'); // event, schedule, webhook, manual
            $table->string('event_type')->nullable(); // invoice_created, payment_received, etc.
            $table->json('trigger_conditions')->nullable();
            $table->string('schedule_expression')->nullable(); // Cron expression for scheduled triggers
            $table->string('webhook_url')->nullable(); // For webhook triggers
            $table->json('webhook_config')->nullable(); // Webhook configuration
            $table->boolean('is_active')->default(true);
            $table->integer('execution_count')->default(0);
            $table->timestamp('last_triggered_at')->nullable();
            $table->json('performance_metrics')->nullable();
            $table->timestamps();
            
            $table->index(['user_id', 'trigger_type']);
            $table->index(['event_type', 'is_active']);
        });

        // Workflow Actions Table (for reusable action definitions)
        Schema::create('workflow_actions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->string('action_type'); // send_email, create_record, api_call, etc.
            $table->json('default_config'); // Default configuration
            $table->json('required_parameters'); // Required parameters
            $table->json('optional_parameters')->nullable(); // Optional parameters
            $table->boolean('is_system_action')->default(false);
            $table->boolean('requires_ai_plan')->default(false); // Requires Pro/Business plan
            $table->string('category')->nullable(); // communication, data, integration, etc.
            $table->json('validation_rules')->nullable(); // Parameter validation
            $table->text('help_text')->nullable();
            $table->timestamps();
            
            $table->index(['action_type', 'category']);
            $table->index(['is_system_action', 'requires_ai_plan']);
        });

        // Workflow Conditions Table (for reusable condition definitions)
        Schema::create('workflow_conditions', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description');
            $table->string('condition_type'); // field_comparison, date_range, calculation, etc.
            $table->json('default_config'); // Default configuration
            $table->json('required_parameters'); // Required parameters
            $table->json('optional_parameters')->nullable(); // Optional parameters
            $table->boolean('is_system_condition')->default(false);
            $table->string('category')->nullable(); // data, time, calculation, etc.
            $table->json('validation_rules')->nullable(); // Parameter validation
            $table->text('help_text')->nullable();
            $table->timestamps();
            
            $table->index(['condition_type', 'category']);
            $table->index(['is_system_condition']);
        });

        // Workflow Variables Table (for dynamic variable management)
        Schema::create('workflow_variables', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workflow_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('type'); // string, number, boolean, date, array, object
            $table->text('description')->nullable();
            $table->json('default_value')->nullable();
            $table->json('current_value')->nullable();
            $table->boolean('is_required')->default(false);
            $table->json('validation_rules')->nullable();
            $table->string('source')->nullable(); // user_input, calculated, api_response, etc.
            $table->json('calculation_formula')->nullable(); // For calculated variables
            $table->timestamps();
            
            $table->index(['workflow_id', 'name']);
            $table->index(['type', 'is_required']);
        });

        // Workflow Logs Table (for detailed execution logging)
        Schema::create('workflow_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workflow_execution_id')->constrained()->onDelete('cascade');
            $table->foreignId('workflow_step_id')->nullable()->constrained()->onDelete('set null');
            $table->string('log_level'); // info, warning, error, debug
            $table->string('event_type'); // step_started, step_completed, action_executed, etc.
            $table->text('message');
            $table->json('context_data')->nullable();
            $table->decimal('execution_time_ms', 10, 3)->nullable();
            $table->timestamp('logged_at');
            $table->timestamps();
            
            $table->index(['workflow_execution_id', 'log_level']);
            $table->index(['event_type', 'logged_at']);
        });

        // Workflow Metrics Table (for performance analytics)
        Schema::create('workflow_metrics', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workflow_id')->constrained()->onDelete('cascade');
            $table->date('metric_date');
            $table->integer('executions_count')->default(0);
            $table->integer('successful_executions')->default(0);
            $table->integer('failed_executions')->default(0);
            $table->decimal('average_execution_time', 8, 3)->nullable();
            $table->decimal('success_rate', 5, 2)->nullable();
            $table->json('performance_data')->nullable();
            $table->json('error_summary')->nullable();
            $table->timestamps();
            
            $table->unique(['workflow_id', 'metric_date']);
            $table->index(['metric_date', 'success_rate']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workflow_metrics');
        Schema::dropIfExists('workflow_logs');
        Schema::dropIfExists('workflow_variables');
        Schema::dropIfExists('workflow_conditions');
        Schema::dropIfExists('workflow_actions');
        Schema::dropIfExists('workflow_triggers');
        Schema::dropIfExists('workflow_steps');
        Schema::dropIfExists('workflow_templates');
    }
};
