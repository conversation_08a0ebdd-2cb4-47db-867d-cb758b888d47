<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Models\TdsRecord;
use App\Models\TdsComplianceAlert;
use App\Services\TdsService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessTdsComplianceAlerts extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'tds:process-compliance-alerts 
                            {--user-id= : Process alerts for specific user only}
                            {--alert-type= : Process specific alert type only}';

    /**
     * The console command description.
     */
    protected $description = 'Process and generate TDS compliance alerts';

    protected TdsService $tdsService;

    /**
     * Create a new command instance.
     */
    public function __construct(TdsService $tdsService)
    {
        parent::__construct();
        $this->tdsService = $tdsService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Processing TDS compliance alerts...');
        
        $startTime = microtime(true);
        $alertsCreated = 0;
        
        try {
            $users = $this->getUsersToProcess();
            
            foreach ($users as $user) {
                $this->info("Processing alerts for user: {$user->name} (ID: {$user->id})");
                
                $userAlerts = $this->processUserAlerts($user);
                $alertsCreated += $userAlerts;
                
                $this->line("  Created {$userAlerts} alerts");
            }

            $executionTime = round(microtime(true) - $startTime, 2);
            $this->info("Compliance alerts processing completed in {$executionTime} seconds");
            $this->info("Total alerts created: {$alertsCreated}");

            // Log the results
            Log::info('TDS compliance alerts processed', [
                'users_processed' => $users->count(),
                'alerts_created' => $alertsCreated,
                'execution_time' => $executionTime,
            ]);

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('Failed to process compliance alerts: ' . $e->getMessage());
            
            Log::error('TDS compliance alerts processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return Command::FAILURE;
        }
    }

    /**
     * Get users to process alerts for.
     */
    protected function getUsersToProcess()
    {
        $query = User::whereHas('tdsRecords');
        
        if ($userId = $this->option('user-id')) {
            $query->where('id', $userId);
        }
        
        return $query->get();
    }

    /**
     * Process alerts for a specific user.
     */
    protected function processUserAlerts(User $user): int
    {
        $alertsCreated = 0;
        $alertType = $this->option('alert-type');
        
        // Process quarterly filing alerts
        if (!$alertType || $alertType === 'quarterly_filing') {
            $alertsCreated += $this->processQuarterlyFilingAlerts($user);
        }
        
        // Process missing certificate alerts
        if (!$alertType || $alertType === 'certificate_missing') {
            $alertsCreated += $this->processMissingCertificateAlerts($user);
        }
        
        // Process rate mismatch alerts
        if (!$alertType || $alertType === 'rate_mismatch') {
            $alertsCreated += $this->processRateMismatchAlerts($user);
        }
        
        // Process compliance deadline alerts
        if (!$alertType || $alertType === 'compliance_deadline') {
            $alertsCreated += $this->processComplianceDeadlineAlerts($user);
        }
        
        return $alertsCreated;
    }

    /**
     * Process quarterly filing alerts.
     */
    protected function processQuarterlyFilingAlerts(User $user): int
    {
        $currentQuarter = $this->getCurrentQuarter();
        $quarterlyAmount = $this->getQuarterlyTdsAmount($user->id, $currentQuarter);
        
        if ($quarterlyAmount >= 40000) {
            // Check if alert already exists
            $existingAlert = TdsComplianceAlert::where('user_id', $user->id)
                ->where('alert_type', 'quarterly_filing')
                ->where('status', 'active')
                ->whereJsonContains('metadata->quarter', $currentQuarter['quarter'])
                ->whereJsonContains('metadata->year', $currentQuarter['year'])
                ->first();
                
            if (!$existingAlert) {
                TdsComplianceAlert::createQuarterlyFilingAlert(
                    $user->id,
                    $currentQuarter['quarter'],
                    $currentQuarter['year'],
                    $quarterlyAmount
                );
                return 1;
            }
        }
        
        return 0;
    }

    /**
     * Process missing certificate alerts.
     */
    protected function processMissingCertificateAlerts(User $user): int
    {
        $missingRecords = TdsRecord::where('user_id', $user->id)
            ->missingCertificates()
            ->where('deduction_date', '>=', now()->subMonths(3))
            ->get();
            
        if ($missingRecords->count() > 0) {
            // Check if recent alert exists
            $existingAlert = TdsComplianceAlert::where('user_id', $user->id)
                ->where('alert_type', 'certificate_missing')
                ->where('status', 'active')
                ->where('created_at', '>=', now()->subDays(7))
                ->first();
                
            if (!$existingAlert) {
                TdsComplianceAlert::createMissingCertificateAlert(
                    $user->id,
                    $missingRecords->toArray()
                );
                return 1;
            }
        }
        
        return 0;
    }

    /**
     * Process rate mismatch alerts.
     */
    protected function processRateMismatchAlerts(User $user): int
    {
        $alertsCreated = 0;
        
        $recentRecords = TdsRecord::where('user_id', $user->id)
            ->where('created_at', '>=', now()->subDays(30))
            ->with('client')
            ->get();

        foreach ($recentRecords as $record) {
            $suggestedRate = $this->getSuggestedRate($record);
            
            if ($suggestedRate && abs($record->tds_percentage - $suggestedRate) > 1) {
                // Check if alert already exists for this record
                $existingAlert = TdsComplianceAlert::where('user_id', $user->id)
                    ->where('alert_type', 'rate_mismatch')
                    ->where('status', 'active')
                    ->whereJsonContains('metadata->record_id', $record->id)
                    ->first();
                    
                if (!$existingAlert) {
                    TdsComplianceAlert::createRateMismatchAlert($user->id, [
                        'client_name' => $record->client->name,
                        'applied_rate' => $record->tds_percentage,
                        'suggested_rate' => $suggestedRate,
                        'invoice_amount' => $record->invoice_amount,
                        'record_id' => $record->id,
                    ]);
                    $alertsCreated++;
                }
            }
        }
        
        return $alertsCreated;
    }

    /**
     * Process compliance deadline alerts.
     */
    protected function processComplianceDeadlineAlerts(User $user): int
    {
        $currentQuarter = $this->getCurrentQuarter();
        $quarterlyAmount = $this->getQuarterlyTdsAmount($user->id, $currentQuarter);

        if ($quarterlyAmount >= 40000) {
            $dueDate = $this->getQuarterlyFilingDueDate($currentQuarter['quarter'], $currentQuarter['year']);
            $daysLeft = now()->diffInDays($dueDate, false);

            if ($daysLeft <= 30 && $daysLeft > 0) {
                // Check if alert already exists
                $existingAlert = TdsComplianceAlert::where('user_id', $user->id)
                    ->where('alert_type', 'compliance_deadline')
                    ->where('status', 'active')
                    ->whereDate('due_date', $dueDate)
                    ->first();
                    
                if (!$existingAlert) {
                    TdsComplianceAlert::createComplianceDeadlineAlert(
                        $user->id,
                        "Quarterly TDS Filing - {$currentQuarter['quarter']} {$currentQuarter['year']}",
                        $dueDate
                    );
                    return 1;
                }
            }
        }
        
        return 0;
    }

    /**
     * Get current quarter.
     */
    protected function getCurrentQuarter(): array
    {
        $month = now()->month;
        $year = now()->year;

        if ($month >= 4 && $month <= 6) {
            return ['quarter' => '1', 'year' => $year];
        } elseif ($month >= 7 && $month <= 9) {
            return ['quarter' => '2', 'year' => $year];
        } elseif ($month >= 10 && $month <= 12) {
            return ['quarter' => '3', 'year' => $year];
        } else {
            return ['quarter' => '4', 'year' => $year - 1];
        }
    }

    /**
     * Get quarterly TDS amount.
     */
    protected function getQuarterlyTdsAmount(int $userId, array $quarter): float
    {
        $startDate = $this->getQuarterStartDate($quarter['quarter'], $quarter['year']);
        $endDate = $this->getQuarterEndDate($quarter['quarter'], $quarter['year']);
        
        return TdsRecord::where('user_id', $userId)
            ->whereBetween('deduction_date', [$startDate, $endDate])
            ->sum('tds_amount');
    }

    /**
     * Get quarter start date.
     */
    protected function getQuarterStartDate(string $quarter, string $year): \Carbon\Carbon
    {
        $dates = [
            '1' => "{$year}-04-01",
            '2' => "{$year}-07-01", 
            '3' => "{$year}-10-01",
            '4' => "{$year}-01-01",
        ];

        return \Carbon\Carbon::parse($dates[$quarter]);
    }

    /**
     * Get quarter end date.
     */
    protected function getQuarterEndDate(string $quarter, string $year): \Carbon\Carbon
    {
        $dates = [
            '1' => "{$year}-06-30",
            '2' => "{$year}-09-30",
            '3' => "{$year}-12-31", 
            '4' => "{$year}-03-31",
        ];

        return \Carbon\Carbon::parse($dates[$quarter]);
    }

    /**
     * Get quarterly filing due date.
     */
    protected function getQuarterlyFilingDueDate(string $quarter, string $year): \Carbon\Carbon
    {
        $dueDates = [
            '1' => "{$year}-07-31",
            '2' => "{$year}-10-31",
            '3' => ($year + 1) . "-01-31",
            '4' => ($year + 1) . "-05-31",
        ];

        return \Carbon\Carbon::parse($dueDates[$quarter]);
    }

    /**
     * Get suggested TDS rate for a record.
     */
    protected function getSuggestedRate(TdsRecord $record): ?float
    {
        // This would use the same logic as in TdsService
        return $this->tdsService->getSuggestedRate($record);
    }
}
