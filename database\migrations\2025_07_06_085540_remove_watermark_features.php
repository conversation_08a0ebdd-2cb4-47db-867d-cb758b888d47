<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Remove all watermark-related plan features
        DB::table('plan_features')
            ->where('feature_key', 'has_watermark')
            ->delete();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Re-add watermark features for existing plans
        $plans = DB::table('plans')->get();

        foreach ($plans as $plan) {
            $hasWatermark = $plan->slug === 'free' ? 'true' : 'false';

            DB::table('plan_features')->insert([
                'plan_id' => $plan->id,
                'feature_key' => 'has_watermark',
                'feature_value' => $hasWatermark,
                'feature_type' => 'boolean',
                'created_at' => now(),
                'updated_at' => now(),
            ]);
        }
    }
};
