<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Edit Contract: ') . $contract->title }}
            </h2>
            <div class="flex space-x-2">
                <a href="{{ route('contracts.show', $contract) }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    View Contract
                </a>
                <a href="{{ route('contracts.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Contracts
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <form method="POST" action="{{ route('contracts.update', $contract) }}">
                        @csrf
                        @method('PUT')

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Left Column -->
                            <div class="space-y-6">
                                <!-- Client Selection -->
                                <div>
                                    <label for="client_id" class="block text-sm font-medium text-gray-700">Client</label>
                                    <select name="client_id" id="client_id" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Select a client</option>
                                        @foreach($clients as $client)
                                            <option value="{{ $client->id }}" {{ old('client_id', $contract->client_id) == $client->id ? 'selected' : '' }}>
                                                {{ $client->name }}
                                                @if($client->company_name) - {{ $client->company_name }} @endif
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('client_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Contract Template -->
                                <div>
                                    <label for="contract_template_id" class="block text-sm font-medium text-gray-700">Contract Template</label>
                                    <select name="contract_template_id" id="contract_template_id" required
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Select a template</option>
                                        @foreach($templates as $template)
                                            <option value="{{ $template->id }}" {{ old('contract_template_id', $contract->contract_template_id) == $template->id ? 'selected' : '' }}>
                                                {{ $template->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('contract_template_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Contract Title -->
                                <div>
                                    <label for="title" class="block text-sm font-medium text-gray-700">Contract Title</label>
                                    <input type="text" name="title" id="title" value="{{ old('title', $contract->title) }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    @error('title')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Variables Section -->
                                @if($contract->variables)
                                    <div>
                                        <h3 class="text-lg font-medium text-gray-900 mb-4">Contract Variables</h3>
                                        <div class="space-y-4">
                                            @foreach($contract->variables as $key => $value)
                                                <div>
                                                    <label for="variable_{{ $key }}" class="block text-sm font-medium text-gray-700">
                                                        {{ ucwords(str_replace('_', ' ', $key)) }}
                                                    </label>
                                                    <input type="text" 
                                                           name="variables[{{ $key }}]" 
                                                           id="variable_{{ $key }}"
                                                           value="{{ old('variables.' . $key, $value) }}"
                                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                                </div>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            </div>

                            <!-- Right Column - Content -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Contract Content</h3>
                                <div>
                                    <textarea name="content" rows="20" required
                                              class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('content', $contract->content) }}</textarea>
                                    @error('content')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>
                            </div>
                        </div>

                        <!-- Submit Buttons -->
                        <div class="mt-8 flex justify-end space-x-3">
                            <a href="{{ route('contracts.show', $contract) }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" name="status" value="draft"
                                    class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Save as Draft
                            </button>
                            @if($contract->status === 'draft')
                                <button type="submit" name="status" value="sent"
                                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Update & Send
                                </button>
                            @else
                                <button type="submit"
                                        class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                    Update Contract
                                </button>
                            @endif
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</x-app-layout>
