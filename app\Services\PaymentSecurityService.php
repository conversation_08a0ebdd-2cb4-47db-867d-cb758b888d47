<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;

class PaymentSecurityService
{
    /**
     * Validate payment request security.
     */
    public function validatePaymentRequest(Payment $payment, User $user): array
    {
        $issues = [];

        // Check if user owns the payment
        if ($payment->user_id !== $user->id) {
            $issues[] = 'Payment does not belong to the authenticated user';
        }

        // Check payment status
        if ($payment->status !== 'pending') {
            $issues[] = 'Payment is not in pending status';
        }

        // Check for suspicious activity
        if ($this->detectSuspiciousActivity($user)) {
            $issues[] = 'Suspicious activity detected for this user';
        }

        // Check rate limiting
        if ($this->isRateLimited($user)) {
            $issues[] = 'Too many payment attempts. Please wait before trying again';
        }

        // Validate payment amount
        if (!$this->validatePaymentAmount($payment)) {
            $issues[] = 'Invalid payment amount detected';
        }

        return $issues;
    }

    /**
     * Detect suspicious payment activity.
     */
    public function detectSuspiciousActivity(User $user): bool
    {
        $cacheKey = "suspicious_activity_user_{$user->id}";
        
        // Check for multiple failed payments in short time
        $failedPayments = Payment::where('user_id', $user->id)
            ->where('status', 'failed')
            ->where('created_at', '>', now()->subHour())
            ->count();

        if ($failedPayments >= 3) {
            Cache::put($cacheKey, true, now()->addHours(24));
            $this->logSecurityEvent('Multiple failed payments', [
                'user_id' => $user->id,
                'failed_count' => $failedPayments
            ]);
            return true;
        }

        // Check for rapid payment attempts
        $recentPayments = Payment::where('user_id', $user->id)
            ->where('created_at', '>', now()->subMinutes(5))
            ->count();

        if ($recentPayments >= 5) {
            Cache::put($cacheKey, true, now()->addHours(1));
            $this->logSecurityEvent('Rapid payment attempts', [
                'user_id' => $user->id,
                'attempt_count' => $recentPayments
            ]);
            return true;
        }

        return Cache::get($cacheKey, false);
    }

    /**
     * Check if user is rate limited for payments.
     */
    public function isRateLimited(User $user): bool
    {
        $key = "payment_rate_limit_user_{$user->id}";
        return RateLimiter::tooManyAttempts($key, 10); // 10 attempts per hour
    }

    /**
     * Record payment attempt for rate limiting.
     */
    public function recordPaymentAttempt(User $user): void
    {
        $key = "payment_rate_limit_user_{$user->id}";
        RateLimiter::hit($key, 3600); // 1 hour decay
    }

    /**
     * Validate payment amount against plan pricing.
     */
    public function validatePaymentAmount(Payment $payment): bool
    {
        $subscription = $payment->userSubscription;
        if (!$subscription || !$subscription->plan) {
            return false;
        }

        $expectedAmount = $subscription->plan->price;
        $tolerance = 0.01; // Allow 1 cent tolerance for currency conversion

        return abs($payment->amount - $expectedAmount) <= $tolerance;
    }

    /**
     * Sanitize webhook payload.
     */
    public function sanitizeWebhookPayload(string $payload): array
    {
        try {
            $data = json_decode($payload, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new \Exception('Invalid JSON payload');
            }

            // Remove potentially dangerous fields
            $dangerousFields = ['script', 'javascript', 'eval', 'exec'];
            $data = $this->removeDangerousFields($data, $dangerousFields);

            return $data;
        } catch (\Exception $e) {
            $this->logSecurityEvent('Webhook payload sanitization failed', [
                'error' => $e->getMessage(),
                'payload_length' => strlen($payload)
            ]);
            throw $e;
        }
    }

    /**
     * Remove dangerous fields from array recursively.
     */
    private function removeDangerousFields(array $data, array $dangerousFields): array
    {
        foreach ($data as $key => $value) {
            if (is_string($key) && in_array(strtolower($key), $dangerousFields)) {
                unset($data[$key]);
                continue;
            }

            if (is_array($value)) {
                $data[$key] = $this->removeDangerousFields($value, $dangerousFields);
            } elseif (is_string($value)) {
                // Remove potential script tags and dangerous content
                $data[$key] = strip_tags($value);
            }
        }

        return $data;
    }

    /**
     * Validate webhook source IP.
     */
    public function validateWebhookSource(string $gateway, string $ip): bool
    {
        $allowedIPs = $this->getAllowedWebhookIPs($gateway);
        
        if (empty($allowedIPs)) {
            // If no specific IPs configured, allow all (log warning)
            Log::warning("No webhook IP restrictions configured for {$gateway}");
            return true;
        }

        foreach ($allowedIPs as $allowedIP) {
            if ($this->ipInRange($ip, $allowedIP)) {
                return true;
            }
        }

        $this->logSecurityEvent('Webhook from unauthorized IP', [
            'gateway' => $gateway,
            'ip' => $ip,
            'allowed_ips' => $allowedIPs
        ]);

        return false;
    }

    /**
     * Get allowed webhook IPs for gateway.
     */
    private function getAllowedWebhookIPs(string $gateway): array
    {
        return match($gateway) {
            'paypal' => [
                '**********/20',
                '**********/21',
                '************/22',
                '**********/20'
            ],
            'razorpay' => [
                '***********/32',
                '*********/32',
                '************/32'
            ],
            default => []
        };
    }

    /**
     * Check if IP is in range.
     */
    private function ipInRange(string $ip, string $range): bool
    {
        if (strpos($range, '/') === false) {
            return $ip === $range;
        }

        list($subnet, $bits) = explode('/', $range);
        $ip = ip2long($ip);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $subnet &= $mask;
        
        return ($ip & $mask) === $subnet;
    }

    /**
     * Log security event.
     */
    public function logSecurityEvent(string $event, array $context = []): void
    {
        Log::warning('Payment security event', [
            'event' => $event,
            'context' => $context,
            'ip' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * Encrypt sensitive payment data.
     */
    public function encryptSensitiveData(array $data): array
    {
        $sensitiveFields = ['card_number', 'cvv', 'account_number', 'routing_number'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $data[$field] = encrypt($data[$field]);
            }
        }

        return $data;
    }

    /**
     * Mask sensitive data for logging.
     */
    public function maskSensitiveData(array $data): array
    {
        $sensitiveFields = ['card_number', 'cvv', 'account_number', 'routing_number', 'email'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($data[$field])) {
                $value = $data[$field];
                if ($field === 'email') {
                    $data[$field] = $this->maskEmail($value);
                } else {
                    $data[$field] = $this->maskString($value);
                }
            }
        }

        return $data;
    }

    /**
     * Mask email address.
     */
    private function maskEmail(string $email): string
    {
        $parts = explode('@', $email);
        if (count($parts) !== 2) {
            return '***@***.***';
        }

        $username = $parts[0];
        $domain = $parts[1];
        
        $maskedUsername = substr($username, 0, 2) . str_repeat('*', max(0, strlen($username) - 2));
        $maskedDomain = substr($domain, 0, 1) . str_repeat('*', max(0, strlen($domain) - 4)) . substr($domain, -3);
        
        return $maskedUsername . '@' . $maskedDomain;
    }

    /**
     * Mask string with asterisks.
     */
    private function maskString(string $value): string
    {
        $length = strlen($value);
        if ($length <= 4) {
            return str_repeat('*', $length);
        }
        
        return substr($value, 0, 2) . str_repeat('*', $length - 4) . substr($value, -2);
    }
}
