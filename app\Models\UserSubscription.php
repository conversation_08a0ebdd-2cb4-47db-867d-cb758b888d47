<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class UserSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'plan_id',
        'status',
        'starts_at',
        'ends_at',
        'cancelled_at',
        'amount_paid',
        'currency',
        'payment_gateway',
        'gateway_subscription_id',
        'gateway_data',
        'usage_limits',
    ];

    protected function casts(): array
    {
        return [
            'starts_at' => 'datetime',
            'ends_at' => 'datetime',
            'cancelled_at' => 'datetime',
            'amount_paid' => 'decimal:2',
            'gateway_data' => 'array',
            'usage_limits' => 'array',
        ];
    }

    /**
     * Get the user that owns the subscription.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the plan for this subscription.
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(Plan::class);
    }

    /**
     * Get the payments for this subscription.
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Scope a query to only include active subscriptions.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('starts_at', '<=', now())
                    ->where(function ($q) {
                        $q->whereNull('ends_at')
                          ->orWhere('ends_at', '>', now());
                    });
    }

    /**
     * Check if subscription is currently active.
     */
    public function isActive(): bool
    {
        return $this->status === 'active'
            && $this->starts_at <= now()
            && ($this->ends_at === null || $this->ends_at > now());
    }

    /**
     * Check if subscription has expired.
     */
    public function isExpired(): bool
    {
        return $this->ends_at !== null && $this->ends_at < now();
    }

    /**
     * Cancel the subscription.
     */
    public function cancel(): void
    {
        $this->update([
            'status' => 'cancelled',
            'cancelled_at' => now(),
        ]);
    }

    /**
     * Get usage for a specific feature.
     */
    public function getUsage(string $featureKey): int
    {
        return $this->usage_limits[$featureKey] ?? 0;
    }

    /**
     * Increment usage for a feature.
     */
    public function incrementUsage(string $featureKey, int $amount = 1): void
    {
        $usage = $this->usage_limits ?? [];
        $usage[$featureKey] = ($usage[$featureKey] ?? 0) + $amount;
        $this->update(['usage_limits' => $usage]);
    }

    /**
     * Reset usage for all features (typically done monthly).
     */
    public function resetUsage(): void
    {
        $this->update(['usage_limits' => []]);
    }
}
