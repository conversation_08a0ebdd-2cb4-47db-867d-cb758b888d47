<?php

namespace App\Traits;

trait HasCalculations
{
    /**
     * Calculate invoice totals
     */
    public function calculateInvoiceTotals(array $items, float $taxPercentage = 0, float $tdsPercentage = 0): array
    {
        $subtotal = 0;
        
        foreach ($items as $item) {
            $subtotal += $item['quantity'] * $item['rate'];
        }

        $taxAmount = ($subtotal * $taxPercentage) / 100;
        $totalAmount = $subtotal + $taxAmount;
        $tdsAmount = ($subtotal * $tdsPercentage) / 100;
        $netAmount = $totalAmount - $tdsAmount;

        return [
            'subtotal' => $subtotal,
            'tax_percentage' => $taxPercentage,
            'tax_amount' => $taxAmount,
            'tds_percentage' => $tdsPercentage,
            'tds_amount' => $tdsAmount,
            'total_amount' => $totalAmount,
            'net_amount' => $netAmount,
        ];
    }

    /**
     * Calculate percentage growth
     */
    public function calculateGrowthPercentage(float $current, float $previous): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100 : 0;
        }

        return (($current - $previous) / $previous) * 100;
    }

    /**
     * Calculate average value
     */
    public function calculateAverage(array $values): float
    {
        if (empty($values)) {
            return 0;
        }

        return array_sum($values) / count($values);
    }

    /**
     * Calculate profit margin
     */
    public function calculateProfitMargin(float $revenue, float $costs): float
    {
        if ($revenue == 0) {
            return 0;
        }

        return (($revenue - $costs) / $revenue) * 100;
    }

    /**
     * Calculate TDS amount
     */
    public function calculateTdsAmount(float $amount, float $tdsPercentage): float
    {
        return ($amount * $tdsPercentage) / 100;
    }

    /**
     * Calculate tax amount
     */
    public function calculateTaxAmount(float $amount, float $taxPercentage): float
    {
        return ($amount * $taxPercentage) / 100;
    }

    /**
     * Format currency amount
     */
    public function formatCurrency(float $amount, string $currency = '₹'): string
    {
        return $currency . number_format($amount, 2);
    }

    /**
     * Calculate percentage of total
     */
    public function calculatePercentageOfTotal(float $value, float $total): float
    {
        if ($total == 0) {
            return 0;
        }

        return ($value / $total) * 100;
    }

    /**
     * Round to two decimal places
     */
    public function roundToTwoDecimals(float $value): float
    {
        return round($value, 2);
    }
}
