<?php

namespace App\Console\Commands;

use App\Services\QueueMonitoringService;
use Illuminate\Console\Command;

class QueueMonitorCommand extends Command
{
    protected $signature = 'queue:monitor {--detailed : Show detailed statistics}';
    protected $description = 'Monitor queue status and performance';

    protected $queueMonitoringService;

    public function __construct(QueueMonitoringService $queueMonitoringService)
    {
        parent::__construct();
        $this->queueMonitoringService = $queueMonitoringService;
    }

    public function handle(): int
    {
        $this->info('Queue Monitoring Report');
        $this->line('========================');

        $stats = $this->queueMonitoringService->getQueueStatistics();

        // Queue Health
        $health = $stats['queue_health'];
        $this->line('');
        $this->info('Queue Health: ' . strtoupper($health['status']));
        $this->line('Health Score: ' . $health['score'] . '/100');
        
        if (!empty($health['issues'])) {
            $this->warn('Issues:');
            foreach ($health['issues'] as $issue) {
                $this->line('  - ' . $issue);
            }
        }

        // Pending Jobs
        $this->line('');
        $this->info('Pending Jobs by Queue:');
        foreach ($stats['pending_jobs'] as $queue => $count) {
            if ($queue === 'total') {
                $this->line('  Total: ' . $count);
            } else {
                $this->line('  ' . ucfirst($queue) . ': ' . $count);
            }
        }

        // Failed Jobs
        $this->line('');
        $this->info('Failed Jobs: ' . $stats['failed_jobs']);

        // Worker Status
        $workers = $stats['worker_status'];
        $this->line('');
        $this->info('Workers: ' . $workers['active_workers'] . '/' . $workers['total_workers'] . ' active');
        $this->line('Utilization: ' . $workers['worker_utilization'] . '%');

        if ($this->option('detailed')) {
            $this->showDetailedStats($stats);
        }

        return 0;
    }

    protected function showDetailedStats(array $stats): void
    {
        // Job Types
        $this->line('');
        $this->info('Job Type Distribution:');
        foreach ($stats['job_types'] as $type => $count) {
            $this->line('  ' . $type . ': ' . $count);
        }

        // Performance Metrics
        $performance = $this->queueMonitoringService->getPerformanceMetrics();
        $this->line('');
        $this->info('Performance Metrics:');
        $this->line('  Jobs/minute: ' . $performance['jobs_per_minute']);
        $this->line('  Success rate: ' . $performance['success_rate'] . '%');
        $this->line('  Avg wait time: ' . $performance['average_wait_time'] . 's');

        // Bottlenecks
        if (!empty($performance['bottlenecks'])) {
            $this->line('');
            $this->warn('Bottlenecks Detected:');
            foreach ($performance['bottlenecks'] as $bottleneck) {
                $this->line('  ' . $bottleneck['queue'] . ': ' . $bottleneck['pending_jobs'] . ' jobs (' . $bottleneck['severity'] . ')');
            }
        }

        // Hourly Throughput
        $this->line('');
        $this->info('Last 6 Hours Throughput:');
        $throughput = array_slice($stats['hourly_throughput'], -6, 6, true);
        foreach ($throughput as $hour => $count) {
            $this->line('  ' . $hour . ': ' . $count . ' jobs');
        }
    }
}
