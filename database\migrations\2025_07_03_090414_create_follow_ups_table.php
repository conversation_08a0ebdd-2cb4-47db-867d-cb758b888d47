<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('follow_ups', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('invoice_id')->constrained()->onDelete('cascade');
            $table->enum('type', ['friendly', 'reminder', 'legal_notice']);
            $table->text('message');
            $table->enum('method', ['email', 'whatsapp', 'both']);
            $table->enum('status', ['pending', 'sent', 'failed'])->default('pending');
            $table->timestamp('scheduled_at');
            $table->timestamp('sent_at')->nullable();

            // Enhanced tracking fields
            $table->json('delivery_channels')->nullable();
            $table->decimal('effectiveness_score', 5, 2)->nullable();
            $table->json('client_response_data')->nullable();
            $table->timestamp('client_viewed_at')->nullable();
            $table->timestamp('client_responded_at')->nullable();
            $table->string('response_type')->nullable();
            $table->json('ai_insights')->nullable();
            $table->boolean('auto_generated')->default(false);
            $table->string('template_used')->nullable();
            $table->json('personalization_data')->nullable();
            $table->integer('retry_count')->default(0);
            $table->timestamp('optimal_send_time')->nullable();

            // Performance indexes
            $table->index('status');
            $table->index('scheduled_at');
            $table->index(['user_id', 'status']);
            $table->index(['invoice_id', 'status']);
            $table->index('effectiveness_score');
            $table->index('auto_generated');
            $table->index('client_viewed_at');
            $table->index('optimal_send_time');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('follow_ups');
    }
};
