<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workflow_executions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('workflow_id')->constrained()->onDelete('cascade');
            $table->json('trigger_data'); // Data that triggered the workflow
            $table->json('execution_data')->nullable(); // Results of each action
            $table->string('status')->default('pending'); // pending, running, completed, failed, cancelled
            $table->timestamp('started_at')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->text('error_message')->nullable();
            $table->integer('actions_executed')->default(0);
            $table->integer('actions_failed')->default(0);

            // Enhanced execution tracking
            $table->decimal('execution_time_seconds', 8, 3)->nullable();
            $table->string('trigger_source')->nullable(); // manual, scheduled, event
            $table->json('context_data')->nullable(); // Additional context
            $table->integer('retry_count')->default(0);
            $table->timestamp('next_retry_at')->nullable();
            $table->json('action_results')->nullable(); // Detailed results per action
            $table->decimal('success_score', 5, 2)->nullable(); // 0-100 success rating
            $table->json('performance_data')->nullable(); // Performance metrics
            $table->string('execution_environment')->nullable(); // production, staging, etc.
            $table->boolean('is_test_execution')->default(false);
            $table->json('ai_analysis')->nullable(); // AI insights on execution

            $table->timestamps();

            $table->index(['workflow_id', 'status']);
            $table->index(['status', 'started_at']);
            $table->index(['trigger_source', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workflow_executions');
    }
};
