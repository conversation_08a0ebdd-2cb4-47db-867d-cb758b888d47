@props([
    'title' => null,
    'subtitle' => null,
    'headerActions' => null,
    'padding' => true,
    'shadow' => 'sm'
])

@php
    $shadowClasses = [
        'none' => '',
        'sm' => 'shadow-sm',
        'md' => 'shadow-md',
        'lg' => 'shadow-lg',
        'soft' => 'shadow-soft'
    ];
    
    $cardClasses = 'bg-white rounded-xl border border-gray-100 overflow-hidden ' . $shadowClasses[$shadow];
@endphp

<div {{ $attributes->merge(['class' => $cardClasses]) }}>
    @if($title || $subtitle || $headerActions)
        <div class="px-6 py-4 border-b border-gray-100 bg-gray-50/50">
            <div class="flex items-center justify-between">
                <div>
                    @if($title)
                        <h3 class="text-lg font-semibold text-gray-900">{{ $title }}</h3>
                    @endif
                    @if($subtitle)
                        <p class="text-sm text-gray-600 mt-1">{{ $subtitle }}</p>
                    @endif
                </div>
                @if($headerActions)
                    <div class="flex items-center space-x-2">
                        {{ $headerActions }}
                    </div>
                @endif
            </div>
        </div>
    @endif
    
    <div class="{{ $padding ? 'p-6' : '' }}">
        {{ $slot }}
    </div>
</div>
