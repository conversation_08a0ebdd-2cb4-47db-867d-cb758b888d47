<?php

namespace App\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Collection;

class DatabaseOptimizationService
{
    /**
     * Analyze database performance and suggest optimizations.
     */
    public function analyzePerformance(): array
    {
        return [
            'slow_queries' => $this->getSlowQueries(),
            'missing_indexes' => $this->suggestMissingIndexes(),
            'table_sizes' => $this->getTableSizes(),
            'index_usage' => $this->getIndexUsage(),
            'recommendations' => $this->getOptimizationRecommendations(),
        ];
    }

    /**
     * Get slow queries from the database.
     */
    protected function getSlowQueries(): array
    {
        // This would typically require query log analysis
        // For now, return common slow query patterns
        return [
            'N+1 queries detected in:' => [
                'Client dashboard with invoices/contracts',
                'Project listings with tasks/time entries',
                'Follow-up listings with invoice/client data',
            ],
            'Missing eager loading in:' => [
                'TimeEntry controller index method',
                'Project repository methods',
                'Multi-client dashboard',
            ]
        ];
    }

    /**
     * Suggest missing database indexes.
     */
    protected function suggestMissingIndexes(): array
    {
        $suggestions = [];

        // Check for foreign keys without indexes
        $tables = [
            'invoices' => ['user_id', 'client_id', 'recurring_invoice_id'],
            'contracts' => ['user_id', 'client_id', 'contract_template_id'],
            'projects' => ['user_id', 'client_id'],
            'tasks' => ['project_id', 'assigned_to', 'created_by'],
            'time_entries' => ['user_id', 'project_id', 'task_id', 'invoice_id'],
            'follow_ups' => ['user_id', 'invoice_id'],
            'tds_records' => ['user_id', 'client_id', 'invoice_id'],
        ];

        foreach ($tables as $table => $columns) {
            if (Schema::hasTable($table)) {
                foreach ($columns as $column) {
                    if (Schema::hasColumn($table, $column)) {
                        $suggestions[] = "Consider composite index on {$table}({$column}, status)";
                    }
                }
            }
        }

        return $suggestions;
    }

    /**
     * Get table sizes for optimization analysis.
     */
    protected function getTableSizes(): array
    {
        try {
            $sizes = DB::select("
                SELECT 
                    table_name,
                    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb,
                    table_rows
                FROM information_schema.tables 
                WHERE table_schema = DATABASE()
                ORDER BY (data_length + index_length) DESC
                LIMIT 10
            ");

            return collect($sizes)->mapWithKeys(function ($table) {
                return [$table->table_name => [
                    'size_mb' => $table->size_mb,
                    'rows' => $table->table_rows
                ]];
            })->toArray();
        } catch (\Exception $e) {
            return ['error' => 'Unable to retrieve table sizes: ' . $e->getMessage()];
        }
    }

    /**
     * Get index usage statistics.
     */
    protected function getIndexUsage(): array
    {
        try {
            $indexes = DB::select("
                SELECT 
                    table_name,
                    index_name,
                    non_unique,
                    column_name
                FROM information_schema.statistics 
                WHERE table_schema = DATABASE()
                ORDER BY table_name, index_name
            ");

            return collect($indexes)->groupBy('table_name')->map(function ($tableIndexes) {
                return $tableIndexes->groupBy('index_name')->map(function ($indexColumns) {
                    return [
                        'columns' => $indexColumns->pluck('column_name')->toArray(),
                        'unique' => $indexColumns->first()->non_unique == 0
                    ];
                });
            })->toArray();
        } catch (\Exception $e) {
            return ['error' => 'Unable to retrieve index usage: ' . $e->getMessage()];
        }
    }

    /**
     * Get optimization recommendations.
     */
    protected function getOptimizationRecommendations(): array
    {
        return [
            'immediate_actions' => [
                'Add eager loading to controller methods that access relationships',
                'Implement select() clauses to limit columns in queries',
                'Add composite indexes for frequently filtered combinations',
                'Use pagination for large result sets',
            ],
            'performance_improvements' => [
                'Implement query result caching for dashboard data',
                'Add database query monitoring and logging',
                'Consider read replicas for reporting queries',
                'Optimize JSON column queries with generated columns',
            ],
            'monitoring' => [
                'Set up slow query logging',
                'Monitor database connection pool usage',
                'Track query execution times',
                'Implement database performance alerts',
            ]
        ];
    }

    /**
     * Optimize common query patterns.
     */
    public function optimizeCommonQueries(): array
    {
        $optimizations = [];

        // Dashboard queries optimization
        $optimizations['dashboard'] = $this->optimizeDashboardQueries();
        
        // List view optimizations
        $optimizations['listings'] = $this->optimizeListingQueries();
        
        // Relationship loading optimizations
        $optimizations['relationships'] = $this->optimizeRelationshipLoading();

        return $optimizations;
    }

    /**
     * Optimize dashboard queries.
     */
    protected function optimizeDashboardQueries(): array
    {
        return [
            'cache_duration' => '15 minutes',
            'optimizations' => [
                'Use aggregate queries instead of loading full collections',
                'Cache expensive calculations like revenue trends',
                'Limit recent items to essential columns only',
                'Use database views for complex reporting queries',
            ]
        ];
    }

    /**
     * Optimize listing queries.
     */
    protected function optimizeListingQueries(): array
    {
        return [
            'pagination' => 'Always use pagination for large datasets',
            'select_optimization' => 'Use select() to limit columns',
            'eager_loading' => 'Preload only necessary relationships',
            'filtering' => 'Add indexes for commonly filtered columns',
        ];
    }

    /**
     * Optimize relationship loading.
     */
    protected function optimizeRelationshipLoading(): array
    {
        return [
            'n_plus_one_fixes' => [
                'Use with() for eager loading',
                'Implement select constraints in relationships',
                'Limit relationship data with closures',
            ],
            'lazy_loading' => [
                'Use lazy() for large collections',
                'Implement cursor pagination for memory efficiency',
                'Consider chunking for batch operations',
            ]
        ];
    }

    /**
     * Generate performance report.
     */
    public function generatePerformanceReport(): array
    {
        return [
            'timestamp' => now()->toISOString(),
            'analysis' => $this->analyzePerformance(),
            'optimizations' => $this->optimizeCommonQueries(),
            'health_score' => $this->calculateHealthScore(),
        ];
    }

    /**
     * Calculate database health score.
     */
    protected function calculateHealthScore(): array
    {
        $score = 85; // Base score
        $issues = [];

        // Deduct points for known issues
        $knownIssues = [
            'N+1 queries in controllers' => -10,
            'Missing composite indexes' => -5,
            'Unoptimized relationship loading' => -5,
        ];

        foreach ($knownIssues as $issue => $penalty) {
            $score += $penalty;
            $issues[] = $issue;
        }

        return [
            'score' => max(0, min(100, $score)),
            'grade' => $this->getGrade($score),
            'issues' => $issues,
            'status' => $score >= 80 ? 'Good' : ($score >= 60 ? 'Fair' : 'Needs Improvement'),
        ];
    }

    /**
     * Get performance grade based on score.
     */
    protected function getGrade(int $score): string
    {
        if ($score >= 90) return 'A';
        if ($score >= 80) return 'B';
        if ($score >= 70) return 'C';
        if ($score >= 60) return 'D';
        return 'F';
    }
}
