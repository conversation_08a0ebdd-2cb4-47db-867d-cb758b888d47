<?php

namespace App\Observers;

use App\Models\Invoice;
use App\Models\TdsRecord;

class InvoiceObserver
{
    /**
     * Handle the Invoice "created" event.
     */
    public function created(Invoice $invoice): void
    {
        // Auto-generate TDS record if TDS percentage is set
        if ($invoice->tds_percentage > 0) {
            TdsRecord::create([
                'user_id' => $invoice->user_id,
                'client_id' => $invoice->client_id,
                'invoice_id' => $invoice->id,
                'financial_year' => TdsRecord::getCurrentFinancialYear(),
                'invoice_amount' => $invoice->total_amount,
                'tds_percentage' => $invoice->tds_percentage,
                'tds_amount' => $invoice->tds_amount,
                'net_received' => $invoice->net_amount,
                'deduction_date' => $invoice->invoice_date,
            ]);
        }
    }

    /**
     * Handle the Invoice "updated" event.
     */
    public function updated(Invoice $invoice): void
    {
        // Update TDS record if it exists and TDS amounts changed
        if ($invoice->tdsRecord && $invoice->wasChanged(['tds_percentage', 'tds_amount', 'total_amount'])) {
            $invoice->tdsRecord->update([
                'invoice_amount' => $invoice->total_amount,
                'tds_percentage' => $invoice->tds_percentage,
                'tds_amount' => $invoice->tds_amount,
                'net_received' => $invoice->net_amount,
            ]);
        }
    }

    /**
     * Handle the Invoice "deleting" event.
     */
    public function deleting(Invoice $invoice): void
    {
        // Delete related records
        $invoice->items()->delete();
        $invoice->followUps()->delete();
        $invoice->tdsRecord?->delete();
    }
}
