<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Create New Contract') }}
            </h2>
            <a href="{{ route('contracts.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                Back to Contracts
            </a>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8">
            @if(!auth()->user()->hasRole('admin'))
                @php
                    $usage = \App\Services\PlanChecker::getContractUsage();
                @endphp

                @if($usage['limit'] !== 'unlimited')
                    <!-- Usage Indicator for Free Users -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <i class="fas fa-file-contract text-blue-600 mr-3"></i>
                                <div>
                                    <h4 class="font-medium text-blue-800">Free Plan Contract Usage</h4>
                                    <p class="text-sm text-blue-700">
                                        You've created <strong>{{ $usage['used'] }}</strong> out of <strong>{{ $usage['limit'] }}</strong> contracts.
                                        @if($usage['used'] >= 1)
                                            <span class="text-blue-800 font-medium">Almost at your limit!</span>
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <a href="{{ route('subscriptions.plans') }}"
                               class="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-lg transition-colors">
                                Upgrade to Pro
                            </a>
                        </div>
                        <!-- Progress Bar -->
                        <div class="mt-3">
                            <div class="bg-blue-200 rounded-full h-2">
                                <div class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                     style="width: {{ $usage['percentage'] }}%"></div>
                            </div>
                        </div>
                    </div>
                @endif
            @endif
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                    <form method="POST" action="{{ route('contracts.store') }}" 
                          x-data="contractForm()" 
                          x-init="init()">
                        @csrf

                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <!-- Left Column -->
                            <div class="space-y-6">
                                <!-- Client Selection -->
                                <div>
                                    <label for="client_id" class="block text-sm font-medium text-gray-700">Client</label>
                                    <select name="client_id" id="client_id" required x-model="clientId"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Select a client</option>
                                        @foreach($clients as $client)
                                            <option value="{{ $client->id }}" {{ old('client_id', request('client_id')) == $client->id ? 'selected' : '' }}>
                                                {{ $client->name }}
                                                @if($client->company_name) - {{ $client->company_name }} @endif
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('client_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                @if(\App\Services\PlanChecker::canUseAiAssistant())
                                    <!-- AI Contract Recommendations -->
                                    <div class="bg-gradient-to-r from-indigo-50 to-purple-50 border border-indigo-200 rounded-lg p-4">
                                        <div class="flex items-center justify-between mb-3">
                                            <div class="flex items-center">
                                                <i class="fas fa-robot text-indigo-600 mr-2"></i>
                                                <h4 class="font-medium text-indigo-800">AI Contract Assistant</h4>
                                            </div>
                                            <button type="button" id="getContractRecommendations"
                                                    class="bg-indigo-500 hover:bg-indigo-600 text-white text-xs font-medium py-1 px-3 rounded-lg transition-colors flex items-center">
                                                <i class="fas fa-magic mr-1"></i>
                                                Get Recommendations
                                            </button>
                                        </div>
                                        <p class="text-sm text-indigo-700 mb-3">
                                            Get AI-powered contract recommendations based on your project type and client requirements.
                                        </p>
                                        <div class="grid grid-cols-2 gap-3">
                                            <div>
                                                <label class="block text-xs font-medium text-indigo-700 mb-1">Project Type</label>
                                                <select id="ai_project_type" class="w-full text-xs rounded border-indigo-300 focus:border-indigo-500 focus:ring-indigo-500">
                                                    <option value="web_development">Web Development</option>
                                                    <option value="mobile_app">Mobile App</option>
                                                    <option value="design">Design</option>
                                                    <option value="content_writing">Content Writing</option>
                                                    <option value="consulting">Consulting</option>
                                                    <option value="other">Other</option>
                                                </select>
                                            </div>
                                            <div>
                                                <label class="block text-xs font-medium text-indigo-700 mb-1">Project Duration</label>
                                                <select id="ai_project_duration" class="w-full text-xs rounded border-indigo-300 focus:border-indigo-500 focus:ring-indigo-500">
                                                    <option value="short_term">Short-term (< 1 month)</option>
                                                    <option value="medium_term">Medium-term (1-3 months)</option>
                                                    <option value="long_term">Long-term (> 3 months)</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div id="ai_recommendations" class="mt-3 hidden">
                                            <div class="bg-white rounded border border-indigo-200 p-3">
                                                <h5 class="text-xs font-medium text-indigo-800 mb-2">AI Recommendations:</h5>
                                                <div id="recommendations_content" class="text-xs text-gray-700"></div>
                                            </div>
                                        </div>
                                    </div>
                                @endif

                                <!-- Contract Template -->
                                <div>
                                    <label for="contract_template_id" class="block text-sm font-medium text-gray-700">Contract Template</label>
                                    <select name="contract_template_id" id="contract_template_id" required x-model="templateId" @change="loadTemplate()"
                                            class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="">Select a template</option>
                                        @foreach($templates as $template)
                                            <option value="{{ $template->id }}" data-content="{{ $template->content }}" {{ old('contract_template_id') == $template->id ? 'selected' : '' }}>
                                                {{ $template->name }}
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('contract_template_id')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Contract Title -->
                                <div>
                                    <label for="title" class="block text-sm font-medium text-gray-700">Contract Title</label>
                                    <input type="text" name="title" id="title" value="{{ old('title') }}" required
                                           class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    @error('title')
                                        <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                    @enderror
                                </div>

                                <!-- Variables Section -->
                                <div x-show="variables.length > 0">
                                    <h3 class="text-lg font-medium text-gray-900 mb-4">Contract Variables</h3>
                                    <div class="space-y-4">
                                        <template x-for="(variable, index) in variables" :key="index">
                                            <div>
                                                <label :for="'variable_' + index" class="block text-sm font-medium text-gray-700" x-text="variable.label"></label>
                                                <input type="text" 
                                                       :name="'variables[' + variable.name + ']'" 
                                                       :id="'variable_' + index"
                                                       x-model="variable.value"
                                                       @input="updatePreview()"
                                                       class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                            </div>
                                        </template>
                                    </div>
                                </div>
                            </div>

                            <!-- Right Column - Preview -->
                            <div>
                                <h3 class="text-lg font-medium text-gray-900 mb-4">Contract Preview</h3>
                                <div class="border border-gray-300 rounded-lg p-4 bg-gray-50 min-h-96 max-h-96 overflow-y-auto">
                                    <div x-html="previewContent" class="text-sm whitespace-pre-line"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Contract Content (Hidden) -->
                        <input type="hidden" name="content" x-model="finalContent">

                        <!-- Submit Buttons -->
                        <div class="mt-8 flex justify-end space-x-3">
                            <a href="{{ route('contracts.index') }}" 
                               class="bg-gray-300 hover:bg-gray-400 text-gray-800 font-bold py-2 px-4 rounded">
                                Cancel
                            </a>
                            <button type="submit" name="status" value="draft"
                                    class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                                Save as Draft
                            </button>
                            <button type="submit" name="status" value="sent"
                                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                Create & Send
                            </button>
                        </div>
                    </form>
                </div>
                </div>
        </div>
    </div>

    <script>
        function contractForm() {
            return {
                clientId: '{{ old('client_id', request('client_id')) }}',
                templateId: '{{ old('contract_template_id') }}',
                templateContent: '',
                variables: [],
                previewContent: 'Select a template to see preview...',
                finalContent: '',

                init() {
                    if (this.templateId) {
                        this.loadTemplate();
                    }
                },

                loadTemplate() {
                    const select = document.getElementById('contract_template_id');
                    const selectedOption = select.options[select.selectedIndex];
                    
                    if (selectedOption && selectedOption.dataset.content) {
                        this.templateContent = selectedOption.dataset.content;
                        this.extractVariables();
                        this.updatePreview();
                    } else {
                        this.templateContent = '';
                        this.variables = [];
                        this.previewContent = 'Select a template to see preview...';
                        this.finalContent = '';
                    }
                },

                extractVariables() {
                    const regex = /\{\{(\w+)\}\}/g;
                    const matches = [...this.templateContent.matchAll(regex)];
                    const uniqueVars = [...new Set(matches.map(match => match[1]))];
                    
                    this.variables = uniqueVars.map(varName => ({
                        name: varName,
                        label: this.formatLabel(varName),
                        value: ''
                    }));
                },

                formatLabel(varName) {
                    return varName.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                },

                updatePreview() {
                    let content = this.templateContent;
                    
                    this.variables.forEach(variable => {
                        const regex = new RegExp(`\\{\\{${variable.name}\\}\\}`, 'g');
                        content = content.replace(regex, variable.value || `[${variable.label}]`);
                    });
                    
                    this.previewContent = content;
                    this.finalContent = content;
                }
            }
        }

        // AI Contract Recommendations
        @if(\App\Services\PlanChecker::canUseAiAssistant())
        document.getElementById('getContractRecommendations').addEventListener('click', function() {
            const clientSelect = document.getElementById('client_id');
            const projectType = document.getElementById('ai_project_type').value;
            const projectDuration = document.getElementById('ai_project_duration').value;
            const button = this;
            const recommendationsDiv = document.getElementById('ai_recommendations');
            const contentDiv = document.getElementById('recommendations_content');

            if (!clientSelect.value) {
                alert('Please select a client first');
                return;
            }

            // Show loading state
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-1"></i>Loading...';
            button.disabled = true;

            // Get client type
            const selectedClientOption = clientSelect.options[clientSelect.selectedIndex];
            const clientName = selectedClientOption ? selectedClientOption.text.split(' (')[0] : '';
            const clientType = selectedClientOption.text.includes('(') ? 'business' : 'individual';

            fetch('{{ route("ai-assistant.generate") }}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': '{{ csrf_token() }}'
                },
                body: JSON.stringify({
                    type: 'contract',
                    prompt: `Generate contract recommendations for ${projectType} project`,
                    context: {
                        project_type: projectType,
                        client_type: clientType,
                        duration: projectDuration,
                        budget: 'medium',
                        client_name: clientName
                    }
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.content) {
                    contentDiv.innerHTML = data.content.replace(/\n/g, '<br>');
                    recommendationsDiv.classList.remove('hidden');

                    // Show success message
                    const successMsg = document.createElement('div');
                    successMsg.className = 'mt-2 text-xs text-green-600 flex items-center';
                    successMsg.innerHTML = '<i class="fas fa-check mr-1"></i>AI recommendations generated successfully!';
                    button.parentNode.appendChild(successMsg);
                    setTimeout(() => successMsg.remove(), 3000);
                } else {
                    alert('Error: ' + (data.error || 'Failed to generate recommendations'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while generating recommendations');
            })
            .finally(() => {
                // Restore button state
                button.innerHTML = originalText;
                button.disabled = false;
            });
        });
        @endif
    </script>
</x-app-layout>
