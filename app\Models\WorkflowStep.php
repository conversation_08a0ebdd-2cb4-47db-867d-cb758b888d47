<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WorkflowStep extends Model
{
    use HasFactory;

    protected $fillable = [
        'workflow_id',
        'name',
        'description',
        'step_order',
        'step_type',
        'step_config',
        'conditions',
        'status',
        'is_required',
        'retry_attempts',
        'max_retries',
        'dependencies',
    ];

    protected function casts(): array
    {
        return [
            'step_order' => 'integer',
            'step_config' => 'array',
            'conditions' => 'array',
            'is_required' => 'boolean',
            'retry_attempts' => 'integer',
            'max_retries' => 'integer',
            'dependencies' => 'array',
        ];
    }

    /**
     * Get the workflow that owns the step.
     */
    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    /**
     * Get the workflow logs for this step.
     */
    public function logs(): HasMany
    {
        return $this->hasMany(WorkflowLog::class);
    }

    /**
     * Check if step conditions are met.
     */
    public function conditionsMet(array $data): bool
    {
        if (empty($this->conditions)) {
            return true;
        }

        foreach ($this->conditions as $condition) {
            if (!$this->evaluateCondition($condition, $data)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate a single condition.
     */
    protected function evaluateCondition(array $condition, array $data): bool
    {
        $field = $condition['field'] ?? null;
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? null;

        if (!$field) {
            return false;
        }

        $fieldValue = data_get($data, $field);

        return match ($operator) {
            '=' => $fieldValue == $value,
            '!=' => $fieldValue != $value,
            '>' => $fieldValue > $value,
            '<' => $fieldValue < $value,
            '>=' => $fieldValue >= $value,
            '<=' => $fieldValue <= $value,
            'contains' => str_contains((string)$fieldValue, (string)$value),
            'not_contains' => !str_contains((string)$fieldValue, (string)$value),
            'in' => in_array($fieldValue, (array)$value),
            'not_in' => !in_array($fieldValue, (array)$value),
            'empty' => empty($fieldValue),
            'not_empty' => !empty($fieldValue),
            'regex' => preg_match('/' . $value . '/', (string)$fieldValue),
            default => false,
        };
    }

    /**
     * Check if step dependencies are satisfied.
     */
    public function dependenciesSatisfied(array $completedSteps): bool
    {
        if (empty($this->dependencies)) {
            return true;
        }

        foreach ($this->dependencies as $dependency) {
            if (!in_array($dependency, $completedSteps)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Execute the step.
     */
    public function execute(array $triggerData, array $executionData): array
    {
        $result = [
            'success' => false,
            'data' => [],
            'error' => null,
        ];

        try {
            $result = match ($this->step_type) {
                'action' => $this->executeAction($triggerData, $executionData),
                'condition' => $this->executeCondition($triggerData, $executionData),
                'delay' => $this->executeDelay($triggerData, $executionData),
                'parallel' => $this->executeParallel($triggerData, $executionData),
                'loop' => $this->executeLoop($triggerData, $executionData),
                'variable_set' => $this->executeVariableSet($triggerData, $executionData),
                default => throw new \Exception("Unknown step type: {$this->step_type}"),
            };

            $result['success'] = true;
        } catch (\Exception $e) {
            $result['error'] = $e->getMessage();
            $this->increment('retry_attempts');
        }

        return $result;
    }

    /**
     * Execute action step.
     */
    protected function executeAction(array $triggerData, array $executionData): array
    {
        $actionType = $this->step_config['action_type'] ?? null;
        $actionConfig = $this->step_config['config'] ?? [];

        // Resolve dynamic values in action config
        $resolvedConfig = $this->resolveDynamicValues($actionConfig, $triggerData, $executionData);

        // Execute the action through WorkflowAutomationService
        $workflowService = app(\App\Services\WorkflowAutomationService::class);
        
        return $workflowService->executeAction([
            'type' => $actionType,
            ...$resolvedConfig
        ], $triggerData, $executionData);
    }

    /**
     * Execute condition step.
     */
    protected function executeCondition(array $triggerData, array $executionData): array
    {
        $conditionResult = $this->conditionsMet(array_merge($triggerData, $executionData));
        
        return [
            'condition_result' => $conditionResult,
            'next_step' => $conditionResult ? 
                ($this->step_config['true_step'] ?? null) : 
                ($this->step_config['false_step'] ?? null),
        ];
    }

    /**
     * Execute delay step.
     */
    protected function executeDelay(array $triggerData, array $executionData): array
    {
        $delaySeconds = $this->step_config['delay_seconds'] ?? 0;
        $delayType = $this->step_config['delay_type'] ?? 'fixed';

        if ($delayType === 'dynamic') {
            $delaySeconds = $this->calculateDynamicDelay($triggerData, $executionData);
        }

        // For now, we'll just record the delay - actual delay would be handled by queue
        return [
            'delay_applied' => $delaySeconds,
            'delay_until' => now()->addSeconds($delaySeconds)->toISOString(),
        ];
    }

    /**
     * Execute parallel step.
     */
    protected function executeParallel(array $triggerData, array $executionData): array
    {
        $parallelActions = $this->step_config['parallel_actions'] ?? [];
        $results = [];

        foreach ($parallelActions as $index => $action) {
            try {
                $results[$index] = $this->executeAction($triggerData, $executionData);
            } catch (\Exception $e) {
                $results[$index] = ['error' => $e->getMessage()];
            }
        }

        return ['parallel_results' => $results];
    }

    /**
     * Execute loop step.
     */
    protected function executeLoop(array $triggerData, array $executionData): array
    {
        $loopData = $this->step_config['loop_data'] ?? [];
        $loopAction = $this->step_config['loop_action'] ?? [];
        $results = [];

        foreach ($loopData as $index => $item) {
            $loopContext = array_merge($triggerData, $executionData, ['loop_item' => $item, 'loop_index' => $index]);
            
            try {
                $results[$index] = $this->executeAction($loopContext, $executionData);
            } catch (\Exception $e) {
                $results[$index] = ['error' => $e->getMessage()];
            }
        }

        return ['loop_results' => $results];
    }

    /**
     * Execute variable set step.
     */
    protected function executeVariableSet(array $triggerData, array $executionData): array
    {
        $variables = $this->step_config['variables'] ?? [];
        $setVariables = [];

        foreach ($variables as $name => $value) {
            $resolvedValue = $this->resolveDynamicValues($value, $triggerData, $executionData);
            $setVariables[$name] = $resolvedValue;
        }

        return ['variables_set' => $setVariables];
    }

    /**
     * Resolve dynamic values in configuration.
     */
    protected function resolveDynamicValues($value, array $triggerData, array $executionData)
    {
        if (is_string($value)) {
            return preg_replace_callback('/\{\{([^}]+)\}\}/', function ($matches) use ($triggerData, $executionData) {
                return data_get(array_merge($triggerData, $executionData), $matches[1], $matches[0]);
            }, $value);
        }

        if (is_array($value)) {
            return array_map(function ($item) use ($triggerData, $executionData) {
                return $this->resolveDynamicValues($item, $triggerData, $executionData);
            }, $value);
        }

        return $value;
    }

    /**
     * Calculate dynamic delay based on data.
     */
    protected function calculateDynamicDelay(array $triggerData, array $executionData): int
    {
        $formula = $this->step_config['delay_formula'] ?? 'fixed';
        $baseDelay = $this->step_config['base_delay'] ?? 60;

        return match ($formula) {
            'exponential' => $baseDelay * pow(2, $this->retry_attempts),
            'linear' => $baseDelay + ($this->retry_attempts * 30),
            'random' => rand($baseDelay, $baseDelay * 2),
            default => $baseDelay,
        };
    }

    /**
     * Check if step can be retried.
     */
    public function canRetry(): bool
    {
        return $this->retry_attempts < $this->max_retries;
    }

    /**
     * Reset retry attempts.
     */
    public function resetRetries(): void
    {
        $this->update(['retry_attempts' => 0]);
    }

    /**
     * Scope for active steps.
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for required steps.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope for steps by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('step_type', $type);
    }
}
