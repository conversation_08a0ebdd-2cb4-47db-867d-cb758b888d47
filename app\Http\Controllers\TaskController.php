<?php

namespace App\Http\Controllers;

use App\Models\Task;
use App\Models\Project;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TaskController extends Controller
{
    /**
     * Show the form for creating a new resource.
     */
    public function create(Project $project)
    {
        // Check if user can access this project
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to access this project.');
        }

        $projectMembers = $project->projectMembers()->with('user')->get();

        return view('tasks.create', compact('project', 'projectMembers'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request, Project $project)
    {
        // Check if user can access this project
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to access this project.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'assigned_to' => 'nullable|exists:users,id',
            'priority' => 'required|in:low,medium,high,urgent',
            'status' => 'required|in:todo,in_progress,review,completed',
            'due_date' => 'nullable|date|after:today',
            'estimated_hours' => 'nullable|numeric|min:0',
        ]);

        $task = new Task($validated);
        $task->project_id = $project->id;
        $task->created_by = Auth::id();
        $task->save();

        return redirect()->route('projects.show', $project)
            ->with('success', 'Task created successfully.');
    }

    /**
     * Display the specified resource.
     */
    public function show(Project $project, Task $task)
    {
        // Check if user can access this project
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to access this project.');
        }

        // Ensure task belongs to project
        if ($task->project_id !== $project->id) {
            abort(404);
        }

        $task->load(['assignedUser', 'creator', 'timeEntries.user']);

        return view('tasks.show', compact('project', 'task'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Project $project, Task $task)
    {
        // Check if user can access this project
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to access this project.');
        }

        // Ensure task belongs to project
        if ($task->project_id !== $project->id) {
            abort(404);
        }

        $projectMembers = $project->projectMembers()->with('user')->get();

        return view('tasks.edit', compact('project', 'task', 'projectMembers'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Project $project, Task $task)
    {
        // Check if user can access this project
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to access this project.');
        }

        // Ensure task belongs to project
        if ($task->project_id !== $project->id) {
            abort(404);
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'assigned_to' => 'nullable|exists:users,id',
            'priority' => 'required|in:low,medium,high,urgent',
            'status' => 'required|in:todo,in_progress,review,completed',
            'due_date' => 'nullable|date',
            'estimated_hours' => 'nullable|numeric|min:0',
        ]);

        $task->update($validated);

        return redirect()->route('projects.tasks.show', [$project, $task])
            ->with('success', 'Task updated successfully.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Project $project, Task $task)
    {
        // Check if user can access this project
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to access this project.');
        }

        // Ensure task belongs to project
        if ($task->project_id !== $project->id) {
            abort(404);
        }

        $task->delete();

        return redirect()->route('projects.show', $project)
            ->with('success', 'Task deleted successfully.');
    }

    /**
     * Update task status.
     */
    public function updateStatus(Request $request, Project $project, Task $task)
    {
        // Check if user can access this project
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to access this project.');
        }

        $validated = $request->validate([
            'status' => 'required|in:todo,in_progress,review,completed',
        ]);

        $task->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Task status updated successfully.',
            'task' => $task
        ]);
    }

    /**
     * Assign task to user.
     */
    public function assign(Request $request, Project $project, Task $task)
    {
        // Check if user can access this project
        if (!Auth::user()->canAccessProject($project)) {
            abort(403, 'You do not have permission to access this project.');
        }

        $validated = $request->validate([
            'assigned_to' => 'nullable|exists:users,id',
        ]);

        $task->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Task assignment updated successfully.',
            'task' => $task->load('assignedUser')
        ]);
    }
}
