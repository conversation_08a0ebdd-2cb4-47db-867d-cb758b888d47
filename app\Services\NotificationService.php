<?php

namespace App\Services;

use App\Models\Invoice;
use App\Models\FollowUp;
use App\Models\User;
use App\Models\Client;
use App\Models\NotificationTemplate;
use App\Models\NotificationPreference;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class NotificationService
{
    /**
     * Send invoice via email.
     */
    public function sendInvoiceEmail(Invoice $invoice): bool
    {
        try {
            $client = $invoice->client;
            $user = $invoice->user;

            Mail::send('emails.invoice', [
                'invoice' => $invoice,
                'client' => $client,
                'user' => $user
            ], function ($message) use ($client, $invoice) {
                $message->to($client->email, $client->name)
                    ->subject("Invoice #{$invoice->invoice_number} from {$invoice->user->business_name}")
                    ->attach(storage_path('app/' . $invoice->pdf_path));
            });

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send invoice email', [
                'invoice_id' => $invoice->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send payment reminder via email.
     */
    public function sendPaymentReminderEmail(FollowUp $followUp): bool
    {
        try {
            $invoice = $followUp->invoice;
            $client = $invoice->client;

            Mail::send('emails.payment-reminder', [
                'followUp' => $followUp,
                'invoice' => $invoice,
                'client' => $client
            ], function ($message) use ($client, $followUp) {
                $message->to($client->email, $client->name)
                    ->subject("Payment Reminder - Invoice #{$followUp->invoice->invoice_number}");
            });

            $followUp->update([
                'status' => 'sent',
                'sent_at' => now()
            ]);

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send payment reminder email', [
                'followup_id' => $followUp->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send WhatsApp message.
     */
    public function sendWhatsAppMessage(string $phoneNumber, string $message): bool
    {
        try {
            $whatsappApiUrl = config('services.whatsapp.api_url');
            $whatsappToken = config('services.whatsapp.token');

            if (!$whatsappApiUrl || !$whatsappToken) {
                Log::warning('WhatsApp API not configured');
                return false;
            }

            $response = Http::withToken($whatsappToken)
                ->post($whatsappApiUrl . '/messages', [
                    'messaging_product' => 'whatsapp',
                    'to' => $phoneNumber,
                    'type' => 'text',
                    'text' => ['body' => $message]
                ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Failed to send WhatsApp message', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send SMS message.
     */
    public function sendSmsMessage(string $phoneNumber, string $message): bool
    {
        try {
            $smsApiUrl = config('services.sms.api_url');
            $smsApiKey = config('services.sms.api_key');

            if (!$smsApiUrl || !$smsApiKey) {
                Log::warning('SMS API not configured');
                return false;
            }

            $response = Http::post($smsApiUrl, [
                'api_key' => $smsApiKey,
                'to' => $phoneNumber,
                'message' => $message,
                'sender_id' => config('services.sms.sender_id', 'FREELIGO')
            ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Failed to send SMS message', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send multi-channel notification.
     */
    public function sendMultiChannelNotification(array $channels, string $message, array $data = []): array
    {
        $results = [];

        foreach ($channels as $channel => $recipient) {
            switch ($channel) {
                case 'email':
                    $results['email'] = $this->sendEmail($recipient, $message, $data);
                    break;
                case 'whatsapp':
                    $results['whatsapp'] = $this->sendWhatsAppMessage($recipient, $message);
                    break;
                case 'sms':
                    $results['sms'] = $this->sendSmsMessage($recipient, $message);
                    break;
            }
        }

        return $results;
    }

    /**
     * Send generic email.
     */
    protected function sendEmail(string $email, string $message, array $data = []): bool
    {
        try {
            Mail::raw($message, function ($mail) use ($email, $data) {
                $mail->to($email)
                    ->subject($data['subject'] ?? 'Notification from Freeligo');
            });
            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send email', [
                'email' => $email,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Process scheduled follow-ups.
     */
    public function processScheduledFollowUps(): array
    {
        $results = [
            'processed' => 0,
            'failed' => 0,
            'errors' => []
        ];

        $scheduledFollowUps = FollowUp::where('status', 'scheduled')
            ->where('scheduled_at', '<=', now())
            ->get();

        foreach ($scheduledFollowUps as $followUp) {
            try {
                $success = match ($followUp->method) {
                    'email' => $this->sendPaymentReminderEmail($followUp),
                    'whatsapp' => $this->sendWhatsAppMessage(
                        $followUp->invoice->client->phone,
                        $followUp->message
                    ),
                    'sms' => $this->sendSmsMessage(
                        $followUp->invoice->client->phone,
                        $followUp->message
                    ),
                    default => false
                };

                if ($success) {
                    $followUp->update([
                        'status' => 'sent',
                        'sent_at' => now()
                    ]);
                    $results['processed']++;
                } else {
                    $results['failed']++;
                }
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'followup_id' => $followUp->id,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Send automation alert to user.
     */
    public function sendAutomationAlert(User $user, string $type, array $data): bool
    {
        try {
            $message = $this->buildAutomationAlertMessage($type, $data);
            
            Mail::raw($message, function ($mail) use ($user, $type) {
                $mail->to($user->email)
                    ->subject("Freeligo Automation Alert: " . ucfirst(str_replace('_', ' ', $type)));
            });

            return true;
        } catch (\Exception $e) {
            Log::error('Failed to send automation alert', [
                'user_id' => $user->id,
                'type' => $type,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Build automation alert message.
     */
    protected function buildAutomationAlertMessage(string $type, array $data): string
    {
        $messages = [
            'recurring_invoice_generated' => "A new recurring invoice has been generated and sent to {$data['client_name']}. Invoice #{$data['invoice_number']} for ₹{$data['amount']}.",
            'payment_overdue' => "Payment for invoice #{$data['invoice_number']} from {$data['client_name']} is now {$data['days_overdue']} days overdue.",
            'tds_calculation_completed' => "TDS calculations have been completed for {$data['period']}. Total TDS: ₹{$data['total_tds']}.",
            'frequency_adjustment_suggested' => "Consider adjusting billing frequency for {$data['client_name']} from {$data['current_frequency']} to {$data['suggested_frequency']} due to payment delays."
        ];

        return $messages[$type] ?? "Automation notification: " . json_encode($data);
    }

    /**
     * Send notification using templates.
     */
    public function sendTemplatedNotification(
        User $user,
        string $notificationType,
        array $variables,
        ?Client $client = null
    ): array {
        $results = [];

        // Get user preferences for this notification type
        $preferences = $this->getNotificationPreferences($user->id, $notificationType, $client?->id);

        if ($preferences->isQuietTime()) {
            return ['status' => 'deferred', 'reason' => 'quiet_hours'];
        }

        $enabledChannels = $preferences->getEnabledChannels();

        foreach ($enabledChannels as $channel) {
            try {
                $template = $this->getTemplate($user->id, $notificationType, $channel);

                if (!$template) {
                    $results[$channel] = ['success' => false, 'error' => 'Template not found'];
                    continue;
                }

                $rendered = $template->render($variables);
                $success = $this->sendByChannel($channel, $client, $rendered);

                $results[$channel] = ['success' => $success];

            } catch (\Exception $e) {
                $results[$channel] = ['success' => false, 'error' => $e->getMessage()];
                Log::error("Templated notification failed", [
                    'user_id' => $user->id,
                    'type' => $notificationType,
                    'channel' => $channel,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Get notification preferences.
     */
    protected function getNotificationPreferences(int $userId, string $type, ?int $clientId = null): NotificationPreference
    {
        $preference = NotificationPreference::where('user_id', $userId)
            ->where('notification_type', $type)
            ->where('client_id', $clientId)
            ->first();

        if (!$preference) {
            // Create default preference
            $defaults = NotificationPreference::getDefaultPreferences();
            $preference = NotificationPreference::create(array_merge($defaults, [
                'user_id' => $userId,
                'client_id' => $clientId,
                'notification_type' => $type,
            ]));
        }

        return $preference;
    }

    /**
     * Get notification template.
     */
    protected function getTemplate(int $userId, string $type, string $channel): ?NotificationTemplate
    {
        return NotificationTemplate::where('user_id', $userId)
            ->byType($type)
            ->byChannel($channel)
            ->active()
            ->first() ?:
            NotificationTemplate::where('user_id', $userId)
                ->byType($type)
                ->byChannel($channel)
                ->where('is_default', true)
                ->first();
    }

    /**
     * Send notification by channel using rendered template.
     */
    protected function sendByChannel(string $channel, ?Client $client, array $rendered): bool
    {
        return match ($channel) {
            'email' => $this->sendTemplatedEmail($client, $rendered),
            'whatsapp' => $this->sendTemplatedWhatsApp($client, $rendered),
            'sms' => $this->sendTemplatedSms($client, $rendered),
            default => false
        };
    }

    /**
     * Send templated email.
     */
    protected function sendTemplatedEmail(?Client $client, array $rendered): bool
    {
        if (!$client || !$client->email) {
            return false;
        }

        try {
            Mail::raw($rendered['body'], function ($message) use ($client, $rendered) {
                $message->to($client->email, $client->name)
                    ->subject($rendered['subject']);
            });

            return true;
        } catch (\Exception $e) {
            Log::error('Templated email failed', [
                'client_id' => $client->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Send templated WhatsApp message.
     */
    protected function sendTemplatedWhatsApp(?Client $client, array $rendered): bool
    {
        if (!$client || !$client->phone) {
            return false;
        }

        try {
            $response = Http::withToken(config('services.whatsapp.token'))
                ->post(config('services.whatsapp.api_url') . '/messages', [
                    'messaging_product' => 'whatsapp',
                    'to' => $client->phone,
                    'type' => 'text',
                    'text' => [
                        'body' => $rendered['body']
                    ]
                ]);

            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Templated WhatsApp failed', [
                'client_id' => $client->id,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * Create default templates for user.
     */
    public function createDefaultTemplates(int $userId): array
    {
        $templates = [];
        $defaultTemplates = NotificationTemplate::getDefaultTemplates();

        foreach ($defaultTemplates as $templateData) {
            $templates[] = NotificationTemplate::create(array_merge($templateData, [
                'user_id' => $userId
            ]));
        }

        return $templates;
    }

    /**
     * Create default notification preferences for user.
     */
    public function createDefaultPreferences(int $userId): array
    {
        return NotificationPreference::createDefaultPreferences($userId);
    }
}
