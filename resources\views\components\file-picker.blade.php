@props([
    'id' => 'filePicker',
    'title' => 'Select File',
    'multiple' => false,
    'accept' => null,
    'onSelect' => null
])

<!-- File Picker Modal -->
<div id="{{ $id }}" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-4/5 lg:w-3/4 xl:w-2/3 shadow-lg rounded-md bg-white max-h-screen overflow-hidden">
        <div class="flex flex-col h-full max-h-[90vh]">
            <!-- Modal Header -->
            <div class="flex items-center justify-between mb-4 pb-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{{ $title }}</h3>
                <button onclick="closeFilePicker('{{ $id }}')" class="text-gray-400 hover:text-gray-600">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>

            <!-- Tabs -->
            <div class="flex border-b border-gray-200 mb-4">
                <button onclick="switchTab('{{ $id }}', 'browse')" 
                        class="tab-button px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300 active"
                        data-tab="browse">
                    <i class="fas fa-folder mr-2"></i>Browse Files
                </button>
                <button onclick="switchTab('{{ $id }}', 'upload')" 
                        class="tab-button px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700 border-b-2 border-transparent hover:border-gray-300 ml-4"
                        data-tab="upload">
                    <i class="fas fa-upload mr-2"></i>Upload New
                </button>
            </div>

            <!-- Tab Content -->
            <div class="flex-1 overflow-hidden">
                <!-- Browse Tab -->
                <div id="{{ $id }}_browse" class="tab-content h-full flex flex-col">
                    <!-- Search and Filters -->
                    <div class="mb-4 flex flex-col sm:flex-row gap-4">
                        <div class="flex-1">
                            <input type="text" 
                                   id="{{ $id }}_search"
                                   placeholder="Search files..." 
                                   class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        </div>
                        <div>
                            <select id="{{ $id }}_type" class="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                <option value="">All Types</option>
                                <option value="images">Images</option>
                                <option value="documents">Documents</option>
                            </select>
                        </div>
                        <button onclick="searchFiles('{{ $id }}')" class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors duration-200">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>

                    <!-- Files Grid -->
                    <div class="flex-1 overflow-y-auto">
                        <div id="{{ $id }}_files_grid" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                            <!-- Files will be loaded here -->
                        </div>
                        
                        <!-- Loading -->
                        <div id="{{ $id }}_loading" class="text-center py-8 hidden">
                            <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
                            <p class="text-gray-500 mt-2">Loading files...</p>
                        </div>

                        <!-- No files -->
                        <div id="{{ $id }}_no_files" class="text-center py-8 hidden">
                            <i class="fas fa-folder-open text-gray-400 text-4xl mb-4"></i>
                            <p class="text-gray-500">No files found</p>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div id="{{ $id }}_pagination" class="mt-4 pt-4 border-t border-gray-200">
                        <!-- Pagination will be loaded here -->
                    </div>
                </div>

                <!-- Upload Tab -->
                <div id="{{ $id }}_upload" class="tab-content h-full hidden">
                    <form id="{{ $id }}_upload_form" class="h-full flex flex-col">
                        @csrf
                        
                        <!-- File Drop Zone -->
                        <div class="flex-1 mb-4">
                            <div id="{{ $id }}_drop_zone" class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors duration-200 cursor-pointer h-full flex flex-col justify-center">
                                <div id="{{ $id }}_drop_content">
                                    <i class="fas fa-cloud-upload-alt text-6xl text-gray-400 mb-4"></i>
                                    <p class="text-gray-600 mb-2 text-lg">Drop files here or click to browse</p>
                                    <p class="text-sm text-gray-500">Maximum file size: 10MB</p>
                                    <p class="text-sm text-gray-500">Allowed: JPG, PNG, GIF, PDF, DOC, DOCX</p>
                                </div>
                                <input type="file" 
                                       id="{{ $id }}_file_input" 
                                       name="file" 
                                       class="hidden" 
                                       @if($multiple) multiple @endif
                                       @if($accept) accept="{{ $accept }}" @endif>
                            </div>
                        </div>

                        <!-- Upload Progress -->
                        <div id="{{ $id }}_upload_progress" class="hidden mb-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="text-sm text-gray-600">Uploading...</span>
                                <span id="{{ $id }}_progress_percent" class="text-sm text-gray-600">0%</span>
                            </div>
                            <div class="w-full bg-gray-200 rounded-full h-2">
                                <div id="{{ $id }}_progress_bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description (Optional)</label>
                            <textarea id="{{ $id }}_description" 
                                      name="description" 
                                      rows="2" 
                                      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                      placeholder="Add a description..."></textarea>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Modal Actions -->
            <div class="flex justify-between items-center pt-4 border-t border-gray-200">
                <div id="{{ $id }}_selected_info" class="text-sm text-gray-600">
                    <!-- Selected file info will appear here -->
                </div>
                
                <div class="flex space-x-3">
                    <button onclick="closeFilePicker('{{ $id }}')" 
                            class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300 transition-colors duration-200">
                        Cancel
                    </button>
                    <button id="{{ $id }}_select_button" 
                            onclick="selectFiles('{{ $id }}')"
                            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                            disabled>
                        Select File{{ $multiple ? 's' : '' }}
                    </button>
                    <button id="{{ $id }}_upload_button" 
                            onclick="uploadFiles('{{ $id }}')"
                            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors duration-200 hidden disabled:opacity-50 disabled:cursor-not-allowed">
                        <i class="fas fa-upload mr-2"></i>Upload & Select
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// File picker functionality
window.filePickerData = window.filePickerData || {};

function openFilePicker(pickerId, options = {}) {
    const picker = document.getElementById(pickerId);
    if (!picker) return;

    // Initialize picker data
    window.filePickerData[pickerId] = {
        selectedFiles: [],
        currentPage: 1,
        multiple: {{ $multiple ? 'true' : 'false' }},
        onSelect: options.onSelect || {{ $onSelect ? $onSelect : 'null' }},
        ...options
    };

    picker.classList.remove('hidden');
    switchTab(pickerId, 'browse');
    loadFiles(pickerId);
}

function closeFilePicker(pickerId) {
    const picker = document.getElementById(pickerId);
    if (picker) {
        picker.classList.add('hidden');
        // Reset state
        if (window.filePickerData[pickerId]) {
            window.filePickerData[pickerId].selectedFiles = [];
        }
        updateSelectedInfo(pickerId);
    }
}

function switchTab(pickerId, tab) {
    const browseTab = document.getElementById(`${pickerId}_browse`);
    const uploadTab = document.getElementById(`${pickerId}_upload`);
    const selectButton = document.getElementById(`${pickerId}_select_button`);
    const uploadButton = document.getElementById(`${pickerId}_upload_button`);
    
    // Update tab buttons
    document.querySelectorAll(`#${pickerId} .tab-button`).forEach(btn => {
        btn.classList.remove('active', 'border-blue-500', 'text-blue-600');
        btn.classList.add('border-transparent', 'text-gray-500');
    });
    
    const activeTab = document.querySelector(`#${pickerId} .tab-button[data-tab="${tab}"]`);
    if (activeTab) {
        activeTab.classList.add('active', 'border-blue-500', 'text-blue-600');
        activeTab.classList.remove('border-transparent', 'text-gray-500');
    }

    // Show/hide content
    if (tab === 'browse') {
        browseTab.classList.remove('hidden');
        uploadTab.classList.add('hidden');
        selectButton.classList.remove('hidden');
        uploadButton.classList.add('hidden');
    } else {
        browseTab.classList.add('hidden');
        uploadTab.classList.remove('hidden');
        selectButton.classList.add('hidden');
        uploadButton.classList.remove('hidden');
        initializeUploadTab(pickerId);
    }
}

function loadFiles(pickerId, page = 1) {
    const data = window.filePickerData[pickerId];
    if (!data) return;

    const search = document.getElementById(`${pickerId}_search`).value;
    const type = document.getElementById(`${pickerId}_type`).value;
    const loading = document.getElementById(`${pickerId}_loading`);
    const grid = document.getElementById(`${pickerId}_files_grid`);
    const noFiles = document.getElementById(`${pickerId}_no_files`);

    loading.classList.remove('hidden');
    grid.innerHTML = '';
    noFiles.classList.add('hidden');

    const params = new URLSearchParams({
        page: page,
        search: search,
        type: type
    });

    fetch(`/files/picker?${params}`, {
        headers: {
            'Accept': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        loading.classList.add('hidden');
        
        if (data.files && data.files.length > 0) {
            renderFiles(pickerId, data.files);
            renderPagination(pickerId, data.pagination);
        } else {
            noFiles.classList.remove('hidden');
        }
    })
    .catch(error => {
        loading.classList.add('hidden');
        console.error('Error loading files:', error);
        showToast('Error loading files', 'error');
    });
}

function renderFiles(pickerId, files) {
    const grid = document.getElementById(`${pickerId}_files_grid`);
    const data = window.filePickerData[pickerId];
    
    grid.innerHTML = files.map(file => `
        <div class="file-item border border-gray-200 rounded-lg p-3 cursor-pointer hover:shadow-md transition-shadow duration-200 ${data.selectedFiles.includes(file.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''}" 
             onclick="toggleFileSelection('${pickerId}', ${file.id}, ${JSON.stringify(file).replace(/"/g, '&quot;')})">
            <div class="text-center">
                ${file.icon ? `<i class="${file.icon} text-2xl mb-2"></i>` : '<i class="fas fa-file text-2xl mb-2"></i>'}
                <p class="text-xs font-medium text-gray-900 truncate" title="${file.name}">${file.name}</p>
                <p class="text-xs text-gray-500">${file.human_size || file.size}</p>
            </div>
            ${data.selectedFiles.includes(file.id) ? '<div class="absolute top-1 right-1"><i class="fas fa-check-circle text-blue-600"></i></div>' : ''}
        </div>
    `).join('');
}

function toggleFileSelection(pickerId, fileId, fileData) {
    const data = window.filePickerData[pickerId];
    if (!data) return;

    const index = data.selectedFiles.findIndex(f => f.id === fileId);
    
    if (index > -1) {
        // Deselect
        data.selectedFiles.splice(index, 1);
    } else {
        // Select
        if (!data.multiple) {
            data.selectedFiles = [fileData];
        } else {
            data.selectedFiles.push(fileData);
        }
    }

    updateSelectedInfo(pickerId);
    loadFiles(pickerId, data.currentPage); // Refresh to update selection UI
}

function updateSelectedInfo(pickerId) {
    const data = window.filePickerData[pickerId];
    const info = document.getElementById(`${pickerId}_selected_info`);
    const selectButton = document.getElementById(`${pickerId}_select_button`);
    
    if (data && data.selectedFiles.length > 0) {
        info.textContent = `${data.selectedFiles.length} file${data.selectedFiles.length > 1 ? 's' : ''} selected`;
        selectButton.disabled = false;
    } else {
        info.textContent = '';
        selectButton.disabled = true;
    }
}

function selectFiles(pickerId) {
    const data = window.filePickerData[pickerId];
    if (!data || data.selectedFiles.length === 0) return;

    if (data.onSelect && typeof data.onSelect === 'function') {
        data.onSelect(data.multiple ? data.selectedFiles : data.selectedFiles[0]);
    }

    closeFilePicker(pickerId);
}

function searchFiles(pickerId) {
    loadFiles(pickerId, 1);
}

// Initialize upload functionality
function initializeUploadTab(pickerId) {
    const dropZone = document.getElementById(`${pickerId}_drop_zone`);
    const fileInput = document.getElementById(`${pickerId}_file_input`);
    
    // Click to browse
    dropZone.addEventListener('click', () => fileInput.click());

    // Drag and drop
    dropZone.addEventListener('dragover', (e) => {
        e.preventDefault();
        dropZone.classList.add('border-blue-500', 'bg-blue-50');
    });

    dropZone.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dropZone.classList.remove('border-blue-500', 'bg-blue-50');
    });

    dropZone.addEventListener('drop', (e) => {
        e.preventDefault();
        dropZone.classList.remove('border-blue-500', 'bg-blue-50');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelection(pickerId);
        }
    });

    fileInput.addEventListener('change', () => handleFileSelection(pickerId));
}

function handleFileSelection(pickerId) {
    const fileInput = document.getElementById(`${pickerId}_file_input`);
    const uploadButton = document.getElementById(`${pickerId}_upload_button`);
    
    uploadButton.disabled = fileInput.files.length === 0;
}

function uploadFiles(pickerId) {
    const form = document.getElementById(`${pickerId}_upload_form`);
    const fileInput = document.getElementById(`${pickerId}_file_input`);
    const description = document.getElementById(`${pickerId}_description`);
    const uploadButton = document.getElementById(`${pickerId}_upload_button`);
    const progress = document.getElementById(`${pickerId}_upload_progress`);
    
    if (!fileInput.files.length) {
        showToast('Please select a file to upload.', 'error');
        return;
    }

    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('description', description.value);
    formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));

    uploadButton.disabled = true;
    progress.classList.remove('hidden');

    fetch('/files', {
        method: 'POST',
        body: formData,
        headers: {
            'Accept': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast(data.message, 'success');
            
            // Auto-select the uploaded file
            const pickerData = window.filePickerData[pickerId];
            if (pickerData) {
                pickerData.selectedFiles = [data.file];
                selectFiles(pickerId);
            }
        } else {
            showToast(data.message, 'error');
        }
    })
    .catch(error => {
        showToast('An error occurred while uploading the file.', 'error');
    })
    .finally(() => {
        uploadButton.disabled = false;
        progress.classList.add('hidden');
    });
}
</script>
