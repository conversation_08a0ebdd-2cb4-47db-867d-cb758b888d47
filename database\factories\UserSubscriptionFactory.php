<?php

namespace Database\Factories;

use App\Models\UserSubscription;
use App\Models\User;
use App\Models\Plan;
use Illuminate\Database\Eloquent\Factories\Factory;
use Carbon\Carbon;

class UserSubscriptionFactory extends Factory
{
    protected $model = UserSubscription::class;

    public function definition(): array
    {
        $startDate = Carbon::now()->subDays(30);

        return [
            'user_id' => User::factory(),
            'plan_id' => Plan::factory(),
            'status' => 'active',
            'starts_at' => $startDate,
            'ends_at' => Carbon::now()->addDays(30),
            'cancelled_at' => null,
            'amount_paid' => 199.00,
            'currency' => 'USD',
            'payment_gateway' => 'paypal',
            'gateway_subscription_id' => fake()->uuid(),
            'gateway_data' => json_encode(['source' => 'web']),
            'usage_limits' => json_encode([]),
        ];
    }

    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'starts_at' => Carbon::now()->subDays(30),
            'ends_at' => Carbon::now()->addDays(30),
            'cancelled_at' => null,
        ]);
    }

    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'starts_at' => Carbon::now(),
            'ends_at' => Carbon::now()->addMonth(),
            'cancelled_at' => null,
        ]);
    }

    public function cancelled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'cancelled',
            'cancelled_at' => Carbon::now()->subDays(5),
            'ends_at' => Carbon::now()->addDays(25), // Still has time left
        ]);
    }

    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'expired',
            'starts_at' => Carbon::now()->subMonths(2),
            'ends_at' => Carbon::now()->subDays(5),
            'cancelled_at' => null,
        ]);
    }

    public function withPaypal(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_gateway' => 'paypal',
            'gateway_subscription_id' => 'I-' . fake()->uuid(),
        ]);
    }

    public function withRazorpay(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_gateway' => 'razorpay',
            'gateway_subscription_id' => 'sub_' . fake()->uuid(),
            'currency' => 'INR',
            'amount_paid' => 199.00,
        ]);
    }

    public function expiringSoon(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'starts_at' => Carbon::now()->subDays(25),
            'ends_at' => Carbon::now()->addDays(3), // Expires in 3 days
            'cancelled_at' => null,
        ]);
    }
}
