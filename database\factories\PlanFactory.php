<?php

namespace Database\Factories;

use App\Models\Plan;
use App\Models\PlanFeature;
use Illuminate\Database\Eloquent\Factories\Factory;

class PlanFactory extends Factory
{
    protected $model = Plan::class;

    public function definition(): array
    {
        return [
            'name' => 'Test Plan',
            'slug' => fake()->unique()->slug(),
            'description' => 'Test plan description',
            'price' => 19.99,
            'currency' => 'USD',
            'billing_cycle' => 'monthly',
            'is_active' => true,
            'sort_order' => 1,
        ];
    }

    public function free(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Free',
            'slug' => 'free',
            'price' => 0,
        ])->afterCreating(function (Plan $plan) {
            PlanFeature::factory()->create([
                'plan_id' => $plan->id,
                'feature_key' => 'invoices_limit',
                'feature_value' => '3',
                'feature_type' => 'limit'
            ]);

        });
    }

    public function pro(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Pro',
            'slug' => 'pro',
            'price' => 199,
        ])->afterCreating(function (Plan $plan) {
            PlanFeature::factory()->create([
                'plan_id' => $plan->id,
                'feature_key' => 'invoices_limit',
                'feature_value' => 'unlimited',
                'feature_type' => 'limit'
            ]);
            PlanFeature::factory()->create([
                'plan_id' => $plan->id,
                'feature_key' => 'has_watermark',
                'feature_value' => 'false',
                'feature_type' => 'boolean'
            ]);
        });
    }

    public function business(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Business',
            'slug' => 'business',
            'price' => 499,
        ])->afterCreating(function (Plan $plan) {
            PlanFeature::factory()->create([
                'plan_id' => $plan->id,
                'feature_key' => 'invoices_limit',
                'feature_value' => 'unlimited',
                'feature_type' => 'limit'
            ]);

            PlanFeature::factory()->create([
                'plan_id' => $plan->id,
                'feature_key' => 'analytics',
                'feature_value' => 'true',
                'feature_type' => 'boolean'
            ]);
        });
    }
}
