<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workflows', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('trigger_type'); // invoice_created, client_created, payment_received, etc.
            $table->json('trigger_conditions')->nullable(); // Conditions that must be met
            $table->json('actions'); // Actions to execute
            $table->string('status')->default('active'); // active, inactive, paused
            $table->boolean('is_active')->default(true);
            $table->integer('execution_count')->default(0);
            $table->timestamp('last_executed_at')->nullable();

            // Enhanced automation fields
            $table->string('category')->default('custom'); // custom, predefined, template
            $table->integer('priority')->default(5); // 1-10, higher = more priority
            $table->json('execution_settings')->nullable(); // retry, timeout, etc.
            $table->decimal('success_rate', 5, 2)->default(100.00);
            $table->integer('consecutive_failures')->default(0);
            $table->timestamp('last_failure_at')->nullable();
            $table->json('performance_metrics')->nullable();
            $table->boolean('ai_optimization_enabled')->default(false);
            $table->json('ai_insights')->nullable();
            $table->foreignId('template_id')->nullable()->constrained('workflow_templates')->onDelete('set null'); // Reference to workflow template
            $table->json('custom_variables')->nullable(); // User-defined variables
            $table->boolean('auto_pause_on_failure')->default(false);
            $table->integer('max_executions_per_day')->nullable();
            $table->date('executions_reset_date')->nullable();
            $table->integer('daily_execution_count')->default(0);

            $table->timestamps();

            $table->index(['user_id', 'trigger_type']);
            $table->index(['status', 'is_active']);
            $table->index(['category', 'template_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workflows');
    }
};
