<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ config('app.name', 'Freeligo') }} - Admin</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=inter:400,500,600,700&family=poppins:400,500,600,700&family=manrope:400,500,600,700&family=open-sans:400,500,600,700&family=mulish:400,500,600,700&display=swap" rel="stylesheet" />

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
</head>
<body class="font-body antialiased">
    <x-sidebar :user="auth()->user()">
        <!-- Page Heading -->
        @isset($header)
            <div class="bg-white border-b border-gray-200">
                <div class="px-6 py-6">
                    {{ $header }}
                </div>
            </div>
        @endisset

        <!-- Flash Messages -->
        @if (session('success'))
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    showToast('{{ session('success') }}', 'success');
                });
            </script>
        @endif

        @if (session('error'))
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    showToast('{{ session('error') }}', 'error');
                });
            </script>
        @endif

        @if (session('warning'))
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    showToast('{{ session('warning') }}', 'warning');
                });
            </script>
        @endif

        @if (session('info'))
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    showToast('{{ session('info') }}', 'info');
                });
            </script>
        @endif

        <!-- Validation Errors -->
        @if ($errors->any())
            <script>
                document.addEventListener('DOMContentLoaded', function() {
                    @foreach ($errors->all() as $error)
                        showToast('{{ $error }}', 'error');
                    @endforeach
                });
            </script>
        @endif

        <!-- Page Content -->
        <div class="p-6">
            {{ $slot }}
        </div>
    </x-sidebar>
</body>
</html>
