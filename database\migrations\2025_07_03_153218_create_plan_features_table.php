<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plan_features', function (Blueprint $table) {
            $table->id();
            $table->foreignId('plan_id')->constrained()->onDelete('cascade');
            $table->string('feature_key'); // invoices_limit, contracts_limit, etc.
            $table->string('feature_value'); // unlimited, 3, 1, etc.
            $table->string('feature_type')->default('limit'); // limit, boolean, text
            $table->timestamps();

            // Indexes
            $table->index(['plan_id', 'feature_key']);
            $table->unique(['plan_id', 'feature_key']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plan_features');
    }
};
