<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Too Many Requests - {{ config('app.name') }}</title>
    @vite(['resources/css/app.css'])
</head>
<body class="bg-gray-50">
    <div class="min-h-screen flex items-center justify-center px-4">
        <div class="max-w-md w-full text-center">
            <!-- Logo -->
            <div class="mb-8">
                <x-logo class="h-12 mx-auto" />
            </div>

            <!-- Error Content -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <div class="mb-6">
                    <h1 class="text-6xl font-bold text-yellow-600 mb-2">429</h1>
                    <h2 class="text-2xl font-semibold text-gray-900 mb-4">Too Many Requests</h2>
                    <p class="text-gray-600 mb-6">
                        You've made too many requests in a short period. Please wait a moment before trying again.
                    </p>
                    
                    @if(isset($seconds))
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                            <p class="text-yellow-800 font-medium">
                                Please wait <span id="countdown">{{ $seconds }}</span> seconds before trying again.
                            </p>
                        </div>
                    @endif
                </div>

                <!-- Action Buttons -->
                <div class="space-y-3">
                    <button onclick="window.location.reload()" 
                            class="w-full bg-emerald-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-emerald-700 transition-colors">
                        Try Again
                    </button>
                    
                    <a href="{{ route('home') }}" 
                       class="w-full bg-gray-100 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-200 transition-colors inline-block">
                        Go Home
                    </a>
                </div>

                <!-- Help Text -->
                <div class="mt-6 pt-6 border-t border-gray-200">
                    <p class="text-sm text-gray-500">
                        This limit helps protect our service. If you need higher limits, please <a href="{{ route('contact') }}" class="text-emerald-600 hover:text-emerald-700">contact our support team</a>
                    </p>
                </div>
            </div>
        </div>
    </div>

    @if(isset($seconds))
        <script>
            let seconds = {{ $seconds }};
            const countdownElement = document.getElementById('countdown');
            
            const timer = setInterval(() => {
                seconds--;
                if (countdownElement) {
                    countdownElement.textContent = seconds;
                }
                
                if (seconds <= 0) {
                    clearInterval(timer);
                    window.location.reload();
                }
            }, 1000);
        </script>
    @endif
</body>
</html>
