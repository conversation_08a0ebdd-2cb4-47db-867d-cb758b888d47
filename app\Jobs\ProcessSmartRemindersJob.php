<?php

namespace App\Jobs;

use App\Models\Invoice;
use App\Models\FollowUp;
use App\Services\SmartPaymentReminderService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessSmartRemindersJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 180; // 3 minutes
    public $tries = 3;
    public $maxExceptions = 2;
    public $backoff = [15, 30, 60];

    protected ?int $invoiceId;
    protected ?int $followUpId;
    protected array $options;

    /**
     * Create a new job instance.
     */
    public function __construct(?int $invoiceId = null, ?int $followUpId = null, array $options = [])
    {
        $this->invoiceId = $invoiceId;
        $this->followUpId = $followUpId;
        $this->options = $options;
        
        // Set queue based on urgency
        $priority = $options['urgent'] ?? false ? 'high' : 'default';
        $this->onQueue($priority);
    }

    /**
     * Execute the job.
     */
    public function handle(SmartPaymentReminderService $service): void
    {
        try {
            if ($this->followUpId) {
                // Process specific follow-up
                $this->processSpecificFollowUp($service);
            } elseif ($this->invoiceId) {
                // Create reminder workflow for specific invoice
                $this->createReminderWorkflow($service);
            } else {
                // Process all scheduled reminders
                $this->processScheduledReminders($service);
            }
        } catch (Exception $e) {
            Log::error('Smart reminder processing job failed', [
                'invoice_id' => $this->invoiceId,
                'follow_up_id' => $this->followUpId,
                'attempt' => $this->attempts(),
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Process a specific follow-up.
     */
    protected function processSpecificFollowUp(SmartPaymentReminderService $service): void
    {
        $followUp = FollowUp::find($this->followUpId);
        
        if (!$followUp) {
            Log::warning('Follow-up not found for processing', [
                'follow_up_id' => $this->followUpId
            ]);
            return;
        }

        if ($followUp->status !== 'scheduled' && $followUp->status !== 'pending') {
            Log::info('Follow-up not in processable state', [
                'follow_up_id' => $this->followUpId,
                'status' => $followUp->status
            ]);
            return;
        }

        $result = $service->sendFollowUp($followUp);
        
        if ($result['success']) {
            Log::info('Follow-up processed successfully', [
                'follow_up_id' => $this->followUpId,
                'invoice_id' => $followUp->invoice_id,
                'method' => $followUp->method,
                'type' => $followUp->type
            ]);

            // Update client behavior analytics
            $this->updateClientBehaviorAnalytics($followUp);
        }
    }

    /**
     * Create reminder workflow for specific invoice.
     */
    protected function createReminderWorkflow(SmartPaymentReminderService $service): void
    {
        $invoice = Invoice::find($this->invoiceId);
        
        if (!$invoice) {
            Log::warning('Invoice not found for reminder workflow creation', [
                'invoice_id' => $this->invoiceId
            ]);
            return;
        }

        $reminders = $service->createEscalationWorkflow($invoice);
        
        Log::info('Reminder workflow created', [
            'invoice_id' => $this->invoiceId,
            'reminders_created' => count($reminders)
        ]);

        // Schedule individual reminder jobs
        foreach ($reminders as $reminder) {
            if ($reminder->scheduled_at && $reminder->scheduled_at->isFuture()) {
                static::dispatch(null, $reminder->id, ['urgent' => $reminder->type === 'urgent'])
                    ->delay($reminder->scheduled_at);
            }
        }
    }

    /**
     * Process all scheduled reminders.
     */
    protected function processScheduledReminders(SmartPaymentReminderService $service): void
    {
        $results = $service->processScheduledReminders();
        
        Log::info('Batch reminder processing completed', [
            'processed' => $results['processed'],
            'sent' => $results['sent'],
            'failed' => $results['failed'],
            'optimized' => $results['optimized'] ?? 0
        ]);

        // Dispatch optimization job if needed
        if (($results['processed'] ?? 0) > 10) {
            ProcessReminderOptimizationJob::dispatch()
                ->onQueue('low')
                ->delay(now()->addHours(1));
        }
    }

    /**
     * Update client behavior analytics.
     */
    protected function updateClientBehaviorAnalytics(FollowUp $followUp): void
    {
        try {
            $invoice = $followUp->invoice;
            if (!$invoice || !$invoice->client) {
                return;
            }

            // Dispatch analytics update job
            ProcessClientAnalyticsJob::dispatch($invoice->client_id, [
                'event' => 'reminder_sent',
                'invoice_id' => $invoice->id,
                'follow_up_id' => $followUp->id,
                'reminder_type' => $followUp->type,
                'method' => $followUp->method,
                'days_since_due' => $invoice->due_date ? now()->diffInDays($invoice->due_date, false) : 0
            ])->onQueue('analytics')->delay(now()->addMinutes(2));

        } catch (Exception $e) {
            Log::warning('Failed to update client behavior analytics', [
                'follow_up_id' => $followUp->id,
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * Handle job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('Smart reminder job permanently failed', [
            'invoice_id' => $this->invoiceId,
            'follow_up_id' => $this->followUpId,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage()
        ]);

        // Mark follow-up as failed if specific ID provided
        if ($this->followUpId) {
            $followUp = FollowUp::find($this->followUpId);
            if ($followUp) {
                $followUp->update([
                    'status' => 'failed',
                    'error_message' => $exception->getMessage()
                ]);
            }
        }

        // Dispatch notification for critical failures
        if ($this->options['urgent'] ?? false) {
            ProcessAdminNotificationJob::dispatch([
                'type' => 'urgent_reminder_failure',
                'invoice_id' => $this->invoiceId,
                'follow_up_id' => $this->followUpId,
                'error' => $exception->getMessage()
            ])->onQueue('notifications');
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        $tags = ['smart-reminders'];
        
        if ($this->invoiceId) {
            $tags[] = "invoice:{$this->invoiceId}";
        }
        
        if ($this->followUpId) {
            $tags[] = "follow-up:{$this->followUpId}";
        }
        
        return $tags;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return $this->backoff;
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHours(2);
    }
}
