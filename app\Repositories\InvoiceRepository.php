<?php

namespace App\Repositories;

use App\Models\Invoice;
use App\Traits\HasDateRanges;
use App\Traits\HasFiltering;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;

class InvoiceRepository extends BaseRepository
{
    use HasDateRanges, HasFiltering;

    protected function getModel(): Model
    {
        return new Invoice();
    }

    /**
     * Get invoices for a specific user with filters
     */
    public function getForUser(int $userId, Request $request = null): LengthAwarePaginator
    {
        $query = $this->model->newQuery()
            ->with('client:id,name,company_name')
            ->where('user_id', $userId);

        if ($request) {
            $query = $this->applyFilters($query, $request, [
                'search_fields' => ['invoice_number', 'client.name', 'client.company_name'],
                'status_field' => 'status',
                'date_field' => 'created_at',
                'sort_by' => 'created_at',
                'sort_direction' => 'desc',
            ]);

            return $this->applyPagination($query, $request);
        }

        return $query->latest()->paginate(15);
    }

    /**
     * Get invoices by status for user
     */
    public function getByStatusForUser(int $userId, string $status): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->where('status', $status)
            ->with('client')
            ->get();
    }

    /**
     * Get overdue invoices for user
     */
    public function getOverdueForUser(int $userId, int $limit = null): Collection
    {
        $query = $this->model->newQuery()
            ->where('user_id', $userId)
            ->where('status', '!=', 'paid')
            ->where('due_date', '<', now())
            ->with('client')
            ->orderBy('due_date');

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get recent invoices for user
     */
    public function getRecentForUser(int $userId, int $limit = 10): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->with('client')
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get invoice statistics for user
     */
    public function getStatsForUser(int $userId): array
    {
        $baseQuery = $this->model->newQuery()->where('user_id', $userId);

        return [
            'total_count' => $baseQuery->count(),
            'paid_count' => (clone $baseQuery)->where('status', 'paid')->count(),
            'pending_count' => (clone $baseQuery)->where('status', 'pending')->count(),
            'draft_count' => (clone $baseQuery)->where('status', 'draft')->count(),
            'overdue_count' => (clone $baseQuery)
                ->where('status', '!=', 'paid')
                ->where('due_date', '<', now())
                ->count(),
            'total_amount' => (clone $baseQuery)->sum('total_amount'),
            'paid_amount' => (clone $baseQuery)->where('status', 'paid')->sum('total_amount'),
            'pending_amount' => (clone $baseQuery)->where('status', 'pending')->sum('total_amount'),
            'overdue_amount' => (clone $baseQuery)
                ->where('status', '!=', 'paid')
                ->where('due_date', '<', now())
                ->sum('total_amount'),
        ];
    }

    /**
     * Get monthly revenue data for user
     */
    public function getMonthlyRevenueForUser(int $userId, int $months = 12): Collection
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->where('status', 'paid')
            ->selectRaw('YEAR(created_at) as year, MONTH(created_at) as month, SUM(total_amount) as revenue, COUNT(*) as count')
            ->groupByRaw('YEAR(created_at), MONTH(created_at)')
            ->orderByRaw('YEAR(created_at) DESC, MONTH(created_at) DESC')
            ->limit($months)
            ->get();
    }

    /**
     * Get current month invoice count for user
     */
    public function getCurrentMonthCountForUser(int $userId): int
    {
        $currentMonth = now()->format('Y-m');
        
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->whereRaw("DATE_FORMAT(created_at, '%Y-%m') = ?", [$currentMonth])
            ->count();
    }

    /**
     * Get invoices for client
     */
    public function getForClient(int $clientId, int $limit = null): Collection
    {
        $query = $this->model->newQuery()
            ->where('client_id', $clientId)
            ->latest();

        if ($limit) {
            $query->limit($limit);
        }

        return $query->get();
    }

    /**
     * Get average invoice value for user
     */
    public function getAverageValueForUser(int $userId): float
    {
        return $this->model->newQuery()
            ->where('user_id', $userId)
            ->where('status', 'paid')
            ->avg('total_amount') ?? 0;
    }

    /**
     * Search invoices with advanced filters
     */
    public function searchInvoices(int $userId, array $filters): Collection
    {
        $query = $this->model->newQuery()
            ->where('user_id', $userId)
            ->with('client');

        if (!empty($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (!empty($filters['client_id'])) {
            $query->where('client_id', $filters['client_id']);
        }

        if (!empty($filters['start_date'])) {
            $query->whereDate('invoice_date', '>=', $filters['start_date']);
        }

        if (!empty($filters['end_date'])) {
            $query->whereDate('invoice_date', '<=', $filters['end_date']);
        }

        if (!empty($filters['min_amount'])) {
            $query->where('total_amount', '>=', $filters['min_amount']);
        }

        if (!empty($filters['max_amount'])) {
            $query->where('total_amount', '<=', $filters['max_amount']);
        }

        return $query->latest()->get();
    }
}
