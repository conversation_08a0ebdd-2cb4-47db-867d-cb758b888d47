<x-app-layout>
    <x-slot name="header">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Subscription Management</h1>
            <p class="mt-2 text-gray-600">Manage your subscription and billing information</p>
        </div>
    </x-slot>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

    <!-- Current Subscription -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Current Subscription</h2>
        </div>
        <div class="px-6 py-4">
            @if($currentSubscription)
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900">{{ $currentSubscription->plan->name }}</h3>
                        <p class="text-sm text-gray-500">{{ $currentSubscription->plan->description }}</p>
                        <p class="text-sm text-gray-500 mt-1">
                            Status: <span class="font-medium {{ $currentSubscription->status === 'active' ? 'text-green-600' : 'text-red-600' }}">
                                {{ ucfirst($currentSubscription->status) }}
                            </span>
                        </p>
                        @if($currentSubscription->ends_at)
                        <p class="text-sm text-gray-500">
                            {{ $currentSubscription->status === 'active' ? 'Next billing date' : 'Expires' }}: 
                            {{ $currentSubscription->ends_at->format('M d, Y') }}
                        </p>
                        @endif
                    </div>
                    <div class="text-right">
                        <p class="text-2xl font-bold text-gray-900">${{ $currentSubscription->plan->price }}</p>
                        <p class="text-sm text-gray-500">/{{ $currentSubscription->plan->billing_cycle }}</p>
                    </div>
                </div>
                
                <div class="mt-6 flex space-x-4">
                    @if($currentSubscription->status === 'active')
                    <a href="{{ route('subscriptions.plans') }}" class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                        Change Plan
                    </a>
                    <form action="{{ route('subscriptions.cancel') }}" method="POST" class="inline">
                        @csrf
                        <button type="submit" class="bg-red-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700" 
                                onclick="return confirm('Are you sure you want to cancel your subscription?')">
                            Cancel Subscription
                        </button>
                    </form>
                    @else
                    <a href="{{ route('subscriptions.plans') }}" class="bg-indigo-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                        Reactivate Subscription
                    </a>
                    @endif
                </div>
            @else
                <div class="text-center py-8">
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No Active Subscription</h3>
                    <p class="text-gray-500 mb-4">You don't have an active subscription. Choose a plan to get started.</p>
                    <a href="{{ route('subscriptions.plans') }}" class="bg-indigo-600 text-white px-6 py-2 rounded-md text-sm font-medium hover:bg-indigo-700">
                        Choose a Plan
                    </a>
                </div>
            @endif
        </div>
    </div>

    <!-- Available Plans -->
    <div class="bg-white shadow rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-medium text-gray-900">Available Plans</h2>
        </div>
        <div class="px-6 py-4">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                @foreach($plans as $plan)
                <div class="border border-gray-200 rounded-lg p-4 {{ $currentSubscription && $currentSubscription->plan_id == $plan->id ? 'ring-2 ring-indigo-500' : '' }}">
                    <h3 class="text-lg font-medium text-gray-900">{{ $plan->name }}</h3>
                    <p class="text-sm text-gray-500 mt-1">{{ $plan->description }}</p>
                    <p class="text-2xl font-bold text-gray-900 mt-4">${{ $plan->price }}</p>
                    <p class="text-sm text-gray-500">/{{ $plan->billing_cycle }}</p>
                    
                    <ul class="mt-4 space-y-2">
                        @foreach($plan->planFeatures->take(3) as $feature)
                        <li class="flex items-center text-sm text-gray-600">
                            <i class="fas fa-check text-green-500 mr-2"></i>
                            {{ $feature->feature_name }}
                        </li>
                        @endforeach
                    </ul>
                    
                    @if($currentSubscription && $currentSubscription->plan_id == $plan->id)
                    <button class="mt-4 w-full bg-gray-300 text-gray-500 py-2 px-4 rounded-md text-sm font-medium cursor-not-allowed">
                        Current Plan
                    </button>
                    @else
                    <form action="{{ route('subscriptions.subscribe', $plan) }}" method="POST" class="mt-4">
                        @csrf
                        <button type="submit" class="w-full bg-indigo-600 text-white py-2 px-4 rounded-md text-sm font-medium hover:bg-indigo-700">
                            Select Plan
                        </button>
                    </form>
                    @endif
                </div>
                @endforeach
            </div>
        </div>
    </div>
</div>
</x-app-layout>
