<?php

namespace App\Http\Controllers;

use App\Models\Payment;
use App\Models\UserSubscription;
use App\Services\PayPalService;
use App\Services\RazorpayService;
use App\Services\PaymentSecurityService;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    /**
     * Show payment gateway selection page.
     */
    public function gateway(Payment $payment)
    {
        // Ensure payment belongs to current user
        if ($payment->user_id !== Auth::id()) {
            abort(403);
        }

        if ($payment->status !== 'pending') {
            return redirect()->route('subscriptions.index')
                           ->with('error', 'Payment is no longer pending.');
        }

        $payment->load('userSubscription.plan');

        // Check if international version (USD currency)
        $isInternational = config('services.currency.code') === 'USD';

        if ($isInternational) {
            // For international version, redirect directly to PayPal
            return $this->paypal($payment, app(PayPalService::class), app(PaymentSecurityService::class));
        }

        return view('payments.gateway', compact('payment'));
    }

    /**
     * Process PayPal payment.
     */
    public function paypal(Payment $payment, PayPalService $paypalService, PaymentSecurityService $securityService)
    {
        $user = Auth::user();

        // Validate payment security
        $securityIssues = $securityService->validatePaymentRequest($payment, $user);
        if (!empty($securityIssues)) {
            $securityService->logSecurityEvent('Payment security validation failed', [
                'payment_id' => $payment->id,
                'issues' => $securityIssues
            ]);
            abort(403, 'Payment validation failed');
        }

        // Record payment attempt for rate limiting
        $securityService->recordPaymentAttempt($user);

        // Update payment gateway
        $payment->update(['gateway' => 'paypal']);

        try {
            // Create PayPal subscription
            $approvalUrl = $paypalService->createSubscription($payment);

            // Log successful payment initiation
            Log::info('PayPal payment initiated', [
                'payment_id' => $payment->id,
                'user_id' => $user->id,
                'amount' => $payment->amount
            ]);

            // Redirect to PayPal for approval
            return redirect($approvalUrl);
        } catch (\Exception $e) {
            $securityService->logSecurityEvent('PayPal payment creation failed', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage()
            ]);

            return redirect()->route('payment.gateway', $payment)
                           ->with('error', 'PayPal payment failed. Please try again or contact support.');
        }
    }

    /**
     * Process Razorpay payment.
     */
    public function razorpay(Payment $payment, RazorpayService $razorpayService)
    {
        // Ensure payment belongs to current user
        if ($payment->user_id !== Auth::id()) {
            abort(403);
        }

        // Update payment gateway
        $payment->update(['gateway' => 'razorpay']);

        try {
            // Create Razorpay subscription or order
            if ($payment->type === 'subscription') {
                $subscriptionData = $razorpayService->createSubscription($payment);
                return view('payments.razorpay-checkout', compact('payment', 'subscriptionData'));
            } else {
                $orderData = $razorpayService->createOrder($payment);
                return view('payments.razorpay-checkout', compact('payment', 'orderData'));
            }
        } catch (\Exception $e) {
            return redirect()->route('payment.gateway', $payment)
                           ->with('error', 'Razorpay payment failed: ' . $e->getMessage());
        }
    }

    /**
     * Handle PayPal webhook.
     */
    public function paypalWebhook(Request $request, PayPalService $paypalService, PaymentSecurityService $securityService)
    {
        try {
            // Validate webhook source IP
            if (!$securityService->validateWebhookSource('paypal', $request->ip())) {
                Log::warning('PayPal webhook from unauthorized IP', ['ip' => $request->ip()]);
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $payload = $request->getContent();
            $headers = $request->headers->all();

            // Sanitize webhook payload
            $sanitizedData = $securityService->sanitizeWebhookPayload($payload);

            // Log webhook received (with masked data)
            Log::info('PayPal webhook received', [
                'event_type' => $sanitizedData['event_type'] ?? 'unknown',
                'resource_type' => $sanitizedData['resource_type'] ?? 'unknown',
                'ip' => $request->ip()
            ]);

            $paypalService->handleWebhook($payload, $headers);

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            $securityService->logSecurityEvent('PayPal webhook processing failed', [
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json(['error' => 'Webhook processing failed'], 400);
        }
    }

    /**
     * Handle Razorpay webhook.
     */
    public function razorpayWebhook(Request $request, RazorpayService $razorpayService, PaymentSecurityService $securityService)
    {
        try {
            // Validate webhook source IP
            if (!$securityService->validateWebhookSource('razorpay', $request->ip())) {
                Log::warning('Razorpay webhook from unauthorized IP', ['ip' => $request->ip()]);
                return response()->json(['error' => 'Unauthorized'], 403);
            }

            $payload = $request->getContent();
            $signature = $request->header('X-Razorpay-Signature');

            // Sanitize webhook payload
            $sanitizedData = $securityService->sanitizeWebhookPayload($payload);

            // Log webhook received (with masked data)
            Log::info('Razorpay webhook received', [
                'event' => $sanitizedData['event'] ?? 'unknown',
                'ip' => $request->ip()
            ]);

            $razorpayService->handleWebhook($payload, $signature);

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            $securityService->logSecurityEvent('Razorpay webhook processing failed', [
                'error' => $e->getMessage(),
                'ip' => $request->ip()
            ]);

            return response()->json(['error' => 'Webhook processing failed'], 400);
        }
    }

    /**
     * Payment success callback.
     */
    public function success(Payment $payment)
    {
        if ($payment->user_id !== Auth::id()) {
            abort(403);
        }

        if ($payment->isCompleted()) {
            return redirect()->route('subscriptions.index')
                           ->with('success', 'Payment completed successfully! Your subscription is now active.');
        }

        return redirect()->route('subscriptions.index')
                        ->with('error', 'Payment verification failed. Please contact support.');
    }

    /**
     * Payment failure callback.
     */
    public function failure(Payment $payment)
    {
        if ($payment->user_id !== Auth::id()) {
            abort(403);
        }

        return redirect()->route('subscriptions.plans')
                        ->with('error', 'Payment failed. Please try again or contact support.');
    }

    /**
     * Simulate successful payment (for development).
     * TODO: Remove this method in production.
     */
    private function simulateSuccessfulPayment(Payment $payment, string $gateway)
    {
        DB::transaction(function () use ($payment, $gateway) {
            // Mark payment as completed
            $payment->markAsCompleted([
                'gateway' => $gateway,
                'transaction_id' => 'SIM_' . strtoupper($gateway) . '_' . time(),
                'simulated' => true,
            ]);

            // Activate subscription
            $subscription = $payment->userSubscription;
            $subscription->update([
                'status' => 'active',
                'payment_gateway' => $gateway,
                'gateway_subscription_id' => 'SUB_' . strtoupper($gateway) . '_' . time(),
            ]);

            // Cancel any other active subscriptions for this user
            UserSubscription::where('user_id', $payment->user_id)
                           ->where('id', '!=', $subscription->id)
                           ->where('status', 'active')
                           ->update(['status' => 'cancelled']);

            // Update user's current plan
            $payment->user->update(['current_plan_id' => $subscription->plan_id]);

            // Reset usage stats
            $payment->user->resetMonthlyUsage();
        });

        return redirect()->route('payment.success', $payment);
    }
}
