<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessTestJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 30;
    public $tries = 1;

    protected string $message;

    public function __construct(string $message)
    {
        $this->message = $message;
    }

    public function handle(): void
    {
        // Simulate some processing time
        sleep(rand(1, 5));
        
        Log::info('Test job processed', [
            'message' => $this->message,
            'queue' => $this->queue,
            'processing_time' => rand(1, 5)
        ]);
    }

    public function tags(): array
    {
        return ['test-job'];
    }
}
