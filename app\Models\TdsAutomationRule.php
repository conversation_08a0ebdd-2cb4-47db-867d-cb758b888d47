<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class TdsAutomationRule extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'rule_name',
        'conditions',
        'tds_rate',
        'section_code',
        'auto_apply',
        'require_confirmation',
        'priority',
        'is_active',
    ];

    protected function casts(): array
    {
        return [
            'conditions' => 'array',
            'tds_rate' => 'decimal:2',
            'auto_apply' => 'boolean',
            'require_confirmation' => 'boolean',
            'priority' => 'integer',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Get the user that owns the automation rule.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get active rules.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get rules for a specific user.
     */
    public function scopeForUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get auto-apply rules.
     */
    public function scopeAutoApply($query)
    {
        return $query->where('auto_apply', true);
    }

    /**
     * Scope to order by priority.
     */
    public function scopeByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    /**
     * Check if this rule matches the given context.
     */
    public function matches(array $context): bool
    {
        if (empty($this->conditions)) {
            return false;
        }

        foreach ($this->conditions as $condition) {
            if (!$this->evaluateCondition($condition, $context)) {
                return false;
            }
        }

        return true;
    }

    /**
     * Evaluate a single condition.
     */
    protected function evaluateCondition(array $condition, array $context): bool
    {
        $field = $condition['field'] ?? '';
        $operator = $condition['operator'] ?? '=';
        $value = $condition['value'] ?? '';
        
        $contextValue = $this->getContextValue($field, $context);
        
        return match ($operator) {
            '=' => $contextValue == $value,
            '!=' => $contextValue != $value,
            '>' => is_numeric($contextValue) && is_numeric($value) && $contextValue > $value,
            '>=' => is_numeric($contextValue) && is_numeric($value) && $contextValue >= $value,
            '<' => is_numeric($contextValue) && is_numeric($value) && $contextValue < $value,
            '<=' => is_numeric($contextValue) && is_numeric($value) && $contextValue <= $value,
            'in' => in_array($contextValue, (array) $value),
            'not_in' => !in_array($contextValue, (array) $value),
            'contains' => is_string($contextValue) && str_contains($contextValue, $value),
            'starts_with' => is_string($contextValue) && str_starts_with($contextValue, $value),
            'ends_with' => is_string($contextValue) && str_ends_with($contextValue, $value),
            'regex' => is_string($contextValue) && preg_match($value, $contextValue),
            default => false,
        };
    }

    /**
     * Get context value with support for nested fields.
     */
    protected function getContextValue(string $field, array $context)
    {
        if (str_contains($field, '.')) {
            $parts = explode('.', $field);
            $value = $context;
            
            foreach ($parts as $part) {
                if (is_array($value) && isset($value[$part])) {
                    $value = $value[$part];
                } elseif (is_object($value) && isset($value->$part)) {
                    $value = $value->$part;
                } else {
                    return null;
                }
            }
            
            return $value;
        }
        
        return $context[$field] ?? null;
    }

    /**
     * Get applicable rules for given context.
     */
    public static function getApplicableRules(int $userId, array $context): \Illuminate\Database\Eloquent\Collection
    {
        return self::forUser($userId)
            ->active()
            ->byPriority()
            ->get()
            ->filter(function ($rule) use ($context) {
                return $rule->matches($context);
            });
    }

    /**
     * Get the best matching rule for given context.
     */
    public static function getBestMatch(int $userId, array $context): ?self
    {
        $applicableRules = self::getApplicableRules($userId, $context);
        
        return $applicableRules->first(); // Already ordered by priority
    }

    /**
     * Create a default rule set for a user.
     */
    public static function createDefaultRules(int $userId): void
    {
        $defaultRules = [
            [
                'rule_name' => 'Professional Services - Companies',
                'conditions' => [
                    ['field' => 'client.type', 'operator' => '=', 'value' => 'company'],
                    ['field' => 'service_type', 'operator' => 'in', 'value' => ['professional_services', 'consultancy']],
                ],
                'tds_rate' => 10.00,
                'section_code' => '194J',
                'priority' => 10,
            ],
            [
                'rule_name' => 'Professional Services - Individuals',
                'conditions' => [
                    ['field' => 'client.type', 'operator' => '=', 'value' => 'individual'],
                    ['field' => 'service_type', 'operator' => 'in', 'value' => ['professional_services', 'consultancy']],
                    ['field' => 'amount', 'operator' => '>=', 'value' => 30000],
                ],
                'tds_rate' => 10.00,
                'section_code' => '194J',
                'priority' => 9,
            ],
            [
                'rule_name' => 'Technical Services - Companies',
                'conditions' => [
                    ['field' => 'client.type', 'operator' => '=', 'value' => 'company'],
                    ['field' => 'service_type', 'operator' => 'in', 'value' => ['technical_services', 'software_development']],
                ],
                'tds_rate' => 2.00,
                'section_code' => '194J',
                'priority' => 8,
            ],
            [
                'rule_name' => 'Government Clients',
                'conditions' => [
                    ['field' => 'client.type', 'operator' => '=', 'value' => 'government'],
                ],
                'tds_rate' => 10.00,
                'section_code' => '194C',
                'priority' => 15,
            ],
        ];

        foreach ($defaultRules as $ruleData) {
            self::create(array_merge($ruleData, ['user_id' => $userId]));
        }
    }

    /**
     * Get available condition fields.
     */
    public static function getConditionFields(): array
    {
        return [
            'amount' => 'Invoice Amount',
            'client.type' => 'Client Type',
            'client.name' => 'Client Name',
            'client.pan' => 'Client PAN',
            'client.state' => 'Client State',
            'service_type' => 'Service Type',
            'invoice.subtotal' => 'Invoice Subtotal',
            'invoice.total_amount' => 'Invoice Total',
            'user.state' => 'User State',
            'financial_year' => 'Financial Year',
        ];
    }

    /**
     * Get available operators.
     */
    public static function getOperators(): array
    {
        return [
            '=' => 'Equals',
            '!=' => 'Not Equals',
            '>' => 'Greater Than',
            '>=' => 'Greater Than or Equal',
            '<' => 'Less Than',
            '<=' => 'Less Than or Equal',
            'in' => 'In List',
            'not_in' => 'Not In List',
            'contains' => 'Contains',
            'starts_with' => 'Starts With',
            'ends_with' => 'Ends With',
            'regex' => 'Regular Expression',
        ];
    }
}
