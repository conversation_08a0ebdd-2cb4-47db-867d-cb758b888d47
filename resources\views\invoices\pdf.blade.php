<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice {{ $invoice->invoice_number }}</title>
    <style>
        body {
            font-family: 'Inter', 'Open Sans', 'Mulish', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            line-height: 1.4;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .company-name {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .invoice-title {
            font-size: 20px;
            color: #666;
            margin-top: 10px;
        }
        .invoice-details {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        .invoice-left, .invoice-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        .invoice-right {
            text-align: right;
        }
        .section-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            color: #333;
        }
        .detail-row {
            margin-bottom: 5px;
            font-size: 12px;
        }
        .detail-label {
            font-weight: bold;
            display: inline-block;
            width: 100px;
        }
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 30px 0;
        }
        .items-table th,
        .items-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            font-size: 12px;
        }
        .items-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .items-table .text-right {
            text-align: right;
        }
        .totals-section {
            float: right;
            width: 300px;
            margin-top: 20px;
        }
        .totals-table {
            width: 100%;
            border-collapse: collapse;
        }
        .totals-table td {
            padding: 5px 10px;
            font-size: 12px;
            border-bottom: 1px solid #eee;
        }
        .totals-table .label {
            text-align: left;
            font-weight: normal;
        }
        .totals-table .amount {
            text-align: right;
            font-weight: bold;
        }
        .totals-table .total-row {
            border-top: 2px solid #333;
            font-weight: bold;
            font-size: 14px;
        }
        .notes-section {
            clear: both;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-paid {
            background-color: #d4edda;
            color: #155724;
        }
        .status-sent {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .status-draft {
            background-color: #f8f9fa;
            color: #495057;
        }
        .status-overdue {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="company-name">{{ config('app.name', 'Freeligo') }}</div>
        <div class="invoice-title">INVOICE</div>
    </div>

    <!-- Invoice Details -->
    <div class="invoice-details">
        <div class="invoice-left">
            <div class="section-title">Bill To:</div>
            <div class="detail-row"><strong>{{ $invoice->client->name }}</strong></div>
            @if($invoice->client->company_name)
                <div class="detail-row">{{ $invoice->client->company_name }}</div>
            @endif
            <div class="detail-row">{{ $invoice->client->email }}</div>
            @if($invoice->client->phone)
                <div class="detail-row">{{ $invoice->client->phone }}</div>
            @endif
            @if($invoice->client->address)
                <div class="detail-row">{{ $invoice->client->address }}</div>
            @endif
            @if($invoice->client->gst_number)
                <div class="detail-row"><span class="detail-label">GST:</span> {{ $invoice->client->gst_number }}</div>
            @endif
            @if($invoice->client->pan_number)
                <div class="detail-row"><span class="detail-label">PAN:</span> {{ $invoice->client->pan_number }}</div>
            @endif
        </div>

        <div class="invoice-right">
            <div class="detail-row">
                <span class="detail-label">Invoice #:</span> 
                <strong>{{ $invoice->invoice_number }}</strong>
            </div>
            <div class="detail-row">
                <span class="detail-label">Date:</span> 
                {{ $invoice->invoice_date->format('d/m/Y') }}
            </div>
            <div class="detail-row">
                <span class="detail-label">Due Date:</span> 
                {{ $invoice->due_date->format('d/m/Y') }}
            </div>
            @if($invoice->paid_date)
                <div class="detail-row">
                    <span class="detail-label">Paid Date:</span> 
                    {{ $invoice->paid_date->format('d/m/Y') }}
                </div>
            @endif
            <div class="detail-row">
                <span class="detail-label">Status:</span>
                <span class="status-badge status-{{ $invoice->isOverdue() && $invoice->status !== 'paid' ? 'overdue' : $invoice->status }}">
                    {{ $invoice->isOverdue() && $invoice->status !== 'paid' ? 'Overdue' : ucfirst($invoice->status) }}
                </span>
            </div>
        </div>
    </div>

    <!-- Items Table -->
    <table class="items-table">
        <thead>
            <tr>
                <th style="width: 50%;">Description</th>
                <th style="width: 15%;" class="text-right">Quantity</th>
                <th style="width: 20%;" class="text-right">Rate</th>
                <th style="width: 15%;" class="text-right">Amount</th>
            </tr>
        </thead>
        <tbody>
            @foreach($invoice->items as $item)
                <tr>
                    <td>{{ $item->description }}</td>
                    <td class="text-right">{{ $item->quantity }}</td>
                    <td class="text-right">₹{{ number_format($item->rate, 2) }}</td>
                    <td class="text-right">₹{{ number_format($item->amount, 2) }}</td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <!-- Totals -->
    <div class="totals-section">
        <table class="totals-table">
            <tr>
                <td class="label">Subtotal:</td>
                <td class="amount">₹{{ number_format($invoice->subtotal, 2) }}</td>
            </tr>
            <tr>
                <td class="label">Tax (18%):</td>
                <td class="amount">₹{{ number_format($invoice->tax_amount, 2) }}</td>
            </tr>
            <tr>
                <td class="label">Total:</td>
                <td class="amount">₹{{ number_format($invoice->total_amount, 2) }}</td>
            </tr>
            @if($invoice->tds_amount > 0)
                <tr>
                    <td class="label">TDS ({{ $invoice->tds_percentage }}%):</td>
                    <td class="amount" style="color: #dc3545;">-₹{{ number_format($invoice->tds_amount, 2) }}</td>
                </tr>
            @endif
            <tr class="total-row">
                <td class="label">Net Amount:</td>
                <td class="amount">₹{{ number_format($invoice->net_amount, 2) }}</td>
            </tr>
        </table>
    </div>

    <!-- Notes -->
    @if($invoice->notes)
        <div class="notes-section">
            <div class="section-title">Notes:</div>
            <div style="font-size: 12px; white-space: pre-line;">{{ $invoice->notes }}</div>
        </div>
    @endif



    <!-- Footer -->
    <div class="footer">
        <p>Thank you for your business!</p>
        <p>Generated by {{ config('app.name', 'Freeligo') }} on {{ now()->format('d/m/Y H:i') }}</p>
        <p style="font-size: 8px; color: #999;">Run Your Freelance Business Like a Pro</p>
    </div>
</body>
</html>
