<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\RateLimiter;
use Tests\TestCase;

class SecurityTest extends TestCase
{
    use RefreshDatabase;

    public function test_security_headers_are_applied()
    {
        $response = $this->get('/');

        $response->assertHeader('X-Frame-Options', 'DENY');
        $response->assertHeader('X-Content-Type-Options', 'nosniff');
        $response->assertHeader('X-XSS-Protection', '1; mode=block');
        $response->assertHeader('Referrer-Policy', 'strict-origin-when-cross-origin');
        $response->assertHeaderMissing('Server');
        $response->assertHeaderMissing('X-Powered-By');
    }

    public function test_content_security_policy_header_is_set()
    {
        $response = $this->get('/');

        $response->assertHeader('Content-Security-Policy');
        
        $csp = $response->headers->get('Content-Security-Policy');
        $this->assertStringContains("default-src 'self'", $csp);
        $this->assertStringContains("script-src 'self'", $csp);
        $this->assertStringContains("style-src 'self'", $csp);
    }

    public function test_https_redirect_in_production()
    {
        config(['app.env' => 'production']);
        
        $response = $this->get('http://example.com/test', [
            'HTTP_HOST' => 'example.com'
        ]);

        // In production, should redirect to HTTPS
        $this->assertTrue(
            $response->isRedirect() && 
            str_starts_with($response->headers->get('Location'), 'https://')
        );
    }

    public function test_rate_limiting_on_login()
    {
        // Clear any existing rate limits
        RateLimiter::clear('login:127.0.0.1');

        // Make 5 failed login attempts (the limit)
        for ($i = 0; $i < 5; $i++) {
            $this->post('/login', [
                'email' => '<EMAIL>',
                'password' => 'wrong-password',
            ]);
        }

        // 6th attempt should be rate limited
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'wrong-password',
        ]);

        $response->assertStatus(429);
    }

    public function test_rate_limiting_on_contact_form()
    {
        // Clear any existing rate limits
        RateLimiter::clear('contact:127.0.0.1');

        // Make 5 contact form submissions (the limit)
        for ($i = 0; $i < 5; $i++) {
            $this->post('/contact', [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'message' => 'Test message',
            ]);
        }

        // 6th attempt should be rate limited
        $response = $this->post('/contact', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message',
        ]);

        $response->assertStatus(429);
    }

    public function test_rate_limiting_on_payment_routes()
    {
        $user = User::factory()->create();
        $payment = \App\Models\Payment::factory()->create(['user_id' => $user->id]);

        // Clear any existing rate limits
        RateLimiter::clear('payment:127.0.0.1');

        $this->actingAs($user);

        // Make 10 payment attempts (the limit)
        for ($i = 0; $i < 10; $i++) {
            $this->post(route('payment.paypal', $payment));
        }

        // 11th attempt should be rate limited
        $response = $this->post(route('payment.paypal', $payment));

        $response->assertStatus(429);
    }

    public function test_cors_headers_on_api_routes()
    {
        $response = $this->options('/api/test', [], [
            'Origin' => 'https://example.com',
            'Access-Control-Request-Method' => 'POST',
            'Access-Control-Request-Headers' => 'Content-Type',
        ]);

        $response->assertHeader('Access-Control-Allow-Origin');
        $response->assertHeader('Access-Control-Allow-Methods');
        $response->assertHeader('Access-Control-Allow-Headers');
    }

    public function test_session_security_settings()
    {
        $user = User::factory()->create();

        $response = $this->post('/login', [
            'email' => $user->email,
            'password' => 'password',
        ]);

        // Check that session cookie has secure settings
        $cookies = $response->headers->getCookies();
        $sessionCookie = collect($cookies)->first(function ($cookie) {
            return str_contains($cookie->getName(), 'session');
        });

        if ($sessionCookie) {
            $this->assertTrue($sessionCookie->isHttpOnly());
            // In production, should also be secure
            if (config('app.env') === 'production') {
                $this->assertTrue($sessionCookie->isSecure());
            }
        }
    }

    public function test_sql_injection_protection()
    {
        $user = User::factory()->create();

        // Attempt SQL injection in login
        $response = $this->post('/login', [
            'email' => "admin'; DROP TABLE users; --",
            'password' => 'password',
        ]);

        // Should not cause any issues, user table should still exist
        $this->assertDatabaseHas('users', ['id' => $user->id]);
    }

    public function test_xss_protection_in_forms()
    {
        $user = User::factory()->create();

        // Attempt XSS in contact form
        $response = $this->post('/contact', [
            'name' => '<script>alert("xss")</script>',
            'email' => '<EMAIL>',
            'message' => '<img src=x onerror=alert("xss")>',
        ]);

        // Form should handle this gracefully without executing scripts
        $response->assertStatus(302); // Redirect after submission
    }

    public function test_csrf_protection()
    {
        $user = User::factory()->create();

        // Attempt to submit form without CSRF token
        $response = $this->post('/contact', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'message' => 'Test message',
        ], [
            'X-CSRF-TOKEN' => 'invalid-token'
        ]);

        $response->assertStatus(419); // CSRF token mismatch
    }

    public function test_file_upload_security()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        // Attempt to upload a PHP file (should be blocked)
        $file = \Illuminate\Http\UploadedFile::fake()->create('malicious.php', 100);

        $response = $this->post('/upload', [
            'file' => $file,
        ]);

        // Should reject PHP files
        $response->assertSessionHasErrors();
    }

    public function test_directory_traversal_protection()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        // Attempt directory traversal
        $response = $this->get('/files/../../../etc/passwd');

        $response->assertStatus(404);
    }

    public function test_sensitive_data_not_exposed_in_errors()
    {
        // Force an error in production mode
        config(['app.env' => 'production', 'app.debug' => false]);

        $response = $this->get('/non-existent-route');

        $response->assertStatus(404);
        
        // Should not expose sensitive information
        $content = $response->getContent();
        $this->assertStringNotContainsString('database', strtolower($content));
        $this->assertStringNotContainsString('password', strtolower($content));
        $this->assertStringNotContainsString('secret', strtolower($content));
    }

    public function test_api_authentication_required()
    {
        // Attempt to access protected API endpoint without authentication
        $response = $this->getJson('/api/user');

        $response->assertStatus(401);
    }

    public function test_admin_routes_require_admin_role()
    {
        $user = User::factory()->create();
        $this->actingAs($user);

        // Attempt to access admin route without admin role
        $response = $this->get('/admin/dashboard');

        $response->assertStatus(403);
    }

    protected function tearDown(): void
    {
        // Clear all rate limiters between tests
        RateLimiter::clear('login:127.0.0.1');
        RateLimiter::clear('contact:127.0.0.1');
        RateLimiter::clear('payment:127.0.0.1');
        parent::tearDown();
    }
}
