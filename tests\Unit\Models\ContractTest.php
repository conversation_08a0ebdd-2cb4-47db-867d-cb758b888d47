<?php

namespace Tests\Unit\Models;

use App\Models\Contract;
use App\Models\User;
use App\Models\Client;
use App\Models\ContractTemplate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Carbon\Carbon;

class ContractTest extends TestCase
{
    use RefreshDatabase;

    public function test_contract_has_fillable_attributes()
    {
        $contract = new Contract();
        $fillable = [
            'user_id', 'client_id', 'contract_template_id', 'title', 'content',
            'variables', 'status', 'pdf_path', 'sent_date', 'signed_date'
        ];

        $this->assertEquals($fillable, $contract->getFillable());
    }

    public function test_contract_casts_attributes_correctly()
    {
        $variables = ['client_name' => 'John Doe', 'amount' => 50000];
        $contract = Contract::factory()->create([
            'variables' => $variables,
            'sent_date' => '2024-01-15',
            'signed_date' => '2024-01-20',
        ]);

        $this->assertIsArray($contract->variables);
        $this->assertEquals($variables, $contract->variables);
        $this->assertInstanceOf(Carbon::class, $contract->sent_date);
        $this->assertInstanceOf(Carbon::class, $contract->signed_date);
    }

    public function test_contract_belongs_to_user()
    {
        $user = User::factory()->create();
        $contract = Contract::factory()->forUser($user)->create();

        $this->assertInstanceOf(User::class, $contract->user);
        $this->assertEquals($user->id, $contract->user->id);
    }

    public function test_contract_belongs_to_client()
    {
        $client = Client::factory()->create();
        $contract = Contract::factory()->forClient($client)->create();

        $this->assertInstanceOf(Client::class, $contract->client);
        $this->assertEquals($client->id, $contract->client->id);
    }

    public function test_contract_belongs_to_contract_template()
    {
        $template = ContractTemplate::factory()->create();
        $contract = Contract::factory()->withTemplate($template)->create();

        $this->assertInstanceOf(ContractTemplate::class, $contract->contractTemplate);
        $this->assertEquals($template->id, $contract->contractTemplate->id);
    }

    public function test_contract_status_transitions()
    {
        $draftContract = Contract::factory()->draft()->create();
        $this->assertEquals('draft', $draftContract->status);
        $this->assertNull($draftContract->sent_date);
        $this->assertNull($draftContract->signed_date);

        $sentContract = Contract::factory()->sent()->create();
        $this->assertEquals('sent', $sentContract->status);
        $this->assertNotNull($sentContract->sent_date);
        $this->assertNull($sentContract->signed_date);

        $signedContract = Contract::factory()->signed()->create();
        $this->assertEquals('signed', $signedContract->status);
        $this->assertNotNull($signedContract->sent_date);
        $this->assertNotNull($signedContract->signed_date);

        $expiredContract = Contract::factory()->expired()->create();
        $this->assertEquals('expired', $expiredContract->status);
        $this->assertNotNull($expiredContract->sent_date);
        $this->assertNull($expiredContract->signed_date);
    }

    public function test_contract_variables_are_stored_as_json()
    {
        $variables = [
            'client_name' => 'ABC Corp',
            'project_name' => 'Website Development',
            'amount' => 75000,
            'duration' => '6 months',
            'start_date' => '2024-01-01',
            'payment_terms' => '50% upfront, 50% on completion'
        ];

        $contract = Contract::factory()->create(['variables' => $variables]);

        $this->assertIsArray($contract->variables);
        $this->assertEquals('ABC Corp', $contract->variables['client_name']);
        $this->assertEquals(75000, $contract->variables['amount']);
        $this->assertEquals('6 months', $contract->variables['duration']);
    }

    public function test_contract_content_can_contain_placeholders()
    {
        $content = "This contract is between [client_name] and the service provider for [project_name] worth [amount].";
        $contract = Contract::factory()->create(['content' => $content]);

        $this->assertStringContainsString('[client_name]', $contract->content);
        $this->assertStringContainsString('[project_name]', $contract->content);
        $this->assertStringContainsString('[amount]', $contract->content);
    }

    public function test_contract_belongs_to_same_user_as_client()
    {
        $user = User::factory()->create();
        $client = Client::factory()->forUser($user)->create();
        $contract = Contract::factory()->forClient($client)->create();

        $this->assertEquals($user->id, $contract->user_id);
        $this->assertEquals($user->id, $contract->client->user_id);
        $this->assertEquals($client->id, $contract->client_id);
    }

    public function test_contract_signed_date_is_after_sent_date()
    {
        $sentDate = Carbon::parse('2024-01-15');
        $signedDate = Carbon::parse('2024-01-20');

        $contract = Contract::factory()->create([
            'status' => 'signed',
            'sent_date' => $sentDate,
            'signed_date' => $signedDate,
        ]);

        $this->assertTrue($contract->signed_date->isAfter($contract->sent_date));
    }

    public function test_contract_can_be_created_from_template()
    {
        $template = ContractTemplate::factory()->webDevelopment()->create();
        $contract = Contract::factory()->withTemplate($template)->create([
            'title' => $template->name,
            'content' => $template->content,
        ]);

        $this->assertEquals($template->id, $contract->contract_template_id);
        $this->assertEquals($template->name, $contract->title);
        $this->assertEquals($template->content, $contract->content);
    }

    public function test_contract_pdf_path_is_nullable()
    {
        $contract = Contract::factory()->create(['pdf_path' => null]);
        $this->assertNull($contract->pdf_path);

        $contract = Contract::factory()->create(['pdf_path' => 'contracts/contract_123.pdf']);
        $this->assertEquals('contracts/contract_123.pdf', $contract->pdf_path);
    }

    public function test_contract_can_have_empty_variables()
    {
        $contract = Contract::factory()->create(['variables' => []]);
        $this->assertIsArray($contract->variables);
        $this->assertEmpty($contract->variables);
    }

    public function test_contract_title_is_required()
    {
        $contract = Contract::factory()->create();
        $this->assertNotNull($contract->title);
        $this->assertNotEmpty($contract->title);
    }
}
