<?php

use Illuminate\Support\Facades\Schedule;
use App\Jobs\ProcessRecurringInvoicesJob;
use App\Jobs\ProcessSmartRemindersJob;
use App\Jobs\ProcessTdsCalculationJob;
use App\Jobs\ProcessTdsComplianceAlertsJob;
use App\Jobs\ProcessWorkflowTriggerJob;

// Scheduled Commands
Schedule::command('subscriptions:check-expired')->hourly();

// Queue-Based Automation Commands (Enhanced)
Schedule::job(new ProcessRecurringInvoicesJob())->dailyAt('09:00')->name('recurring-invoices-daily');
Schedule::job(new ProcessRecurringInvoicesJob(null, ['priority' => 'retry']))->everyTwoHours()->name('recurring-invoices-retry');
Schedule::job(new ProcessSmartRemindersJob())->dailyAt('10:00')->name('smart-reminders-daily');
Schedule::job(new ProcessSmartRemindersJob())->everyFifteenMinutes()->name('smart-reminders-scheduled');

// TDS Automation (Queue-Based)
Schedule::job(new ProcessTdsCalculationJob(null, null, 'quarterly'))->quarterly()->name('tds-quarterly');
Schedule::job(new ProcessTdsComplianceAlertsJob())->monthly()->name('tds-compliance-monthly');
Schedule::job(new ProcessTdsCalculationJob(null, null, 'automation'))->dailyAt('02:00')->name('tds-automation-daily');
Schedule::job(new ProcessTdsComplianceAlertsJob())->dailyAt('09:00')->name('tds-compliance-daily');

// Workflow Automation (Queue-Based)
Schedule::job(new ProcessWorkflowTriggerJob('scheduled'))->everyThirtyMinutes()->name('workflows-scheduled');
Schedule::job(new ProcessWorkflowTriggerJob('invoice_overdue'))->dailyAt('11:00')->name('workflows-overdue');
Schedule::job(new ProcessWorkflowTriggerJob('contract_expiring'))->dailyAt('12:00')->name('workflows-expiring');

// Queue Monitoring and Maintenance
Schedule::command('queue:monitor')->hourly()->name('queue-monitoring');
Schedule::command('queue:cleanup --days=7')->daily()->name('queue-cleanup-daily');
Schedule::command('queue:cleanup --retry-failed --force')->dailyAt('03:00')->name('queue-retry-failed');
