<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plans', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Free, Pro, Business
            $table->string('slug')->unique(); // free, pro, business
            $table->text('description')->nullable();
            $table->decimal('price', 10, 2); // Monthly price in INR
            $table->string('currency', 3)->default('INR');
            $table->enum('billing_cycle', ['monthly', 'yearly'])->default('monthly');
            $table->integer('sort_order')->default(0); // For ordering plans
            $table->boolean('is_popular')->default(false); // Mark as "Most Popular"
            $table->boolean('is_active')->default(true);
            $table->json('features')->nullable(); // Store feature descriptions for display
            $table->timestamps();

            // Indexes
            $table->index('slug');
            $table->index('is_active');
            $table->index('sort_order');
        });

        // Add foreign key constraint to users table after plans table is created
        Schema::table('users', function (Blueprint $table) {
            $table->foreign('current_plan_id')->references('id')->on('plans')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plans');
    }
};
