<?php

namespace App\Jobs;

use App\Models\Workflow;
use App\Models\WorkflowExecution;
use App\Services\WorkflowAutomationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Exception;

class ProcessWorkflowTriggerJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $timeout = 300; // 5 minutes
    public $tries = 3;
    public $maxExceptions = 2;
    public $backoff = [20, 60, 180];

    protected string $triggerType;
    protected array $triggerData;
    protected ?int $workflowId;
    protected array $options;

    /**
     * Create a new job instance.
     */
    public function __construct(string $triggerType, array $triggerData = [], ?int $workflowId = null, array $options = [])
    {
        $this->triggerType = $triggerType;
        $this->triggerData = $triggerData;
        $this->workflowId = $workflowId;
        $this->options = $options;
        
        // Set queue based on priority
        $priority = $options['priority'] ?? $this->determinePriority($triggerType);
        $this->onQueue($priority);
    }

    /**
     * Execute the job.
     */
    public function handle(WorkflowAutomationService $service): void
    {
        try {
            if ($this->workflowId) {
                // Process specific workflow
                $this->processSpecificWorkflow($service);
            } else {
                // Process all workflows for trigger type
                $this->processWorkflowsByTrigger($service);
            }
        } catch (Exception $e) {
            Log::error('Workflow trigger processing job failed', [
                'trigger_type' => $this->triggerType,
                'workflow_id' => $this->workflowId,
                'attempt' => $this->attempts(),
                'error' => $e->getMessage()
            ]);

            throw $e;
        }
    }

    /**
     * Process a specific workflow.
     */
    protected function processSpecificWorkflow(WorkflowAutomationService $service): void
    {
        $workflow = Workflow::find($this->workflowId);
        
        if (!$workflow) {
            Log::warning('Workflow not found for processing', [
                'workflow_id' => $this->workflowId
            ]);
            return;
        }

        if (!$workflow->is_active || $workflow->status !== 'active') {
            Log::info('Workflow not active for processing', [
                'workflow_id' => $this->workflowId,
                'status' => $workflow->status,
                'is_active' => $workflow->is_active
            ]);
            return;
        }

        // Check if workflow can execute today
        if (!$workflow->canExecuteToday()) {
            Log::info('Workflow has reached daily execution limit', [
                'workflow_id' => $this->workflowId,
                'daily_executions' => $workflow->daily_executions,
                'max_daily_executions' => $workflow->max_daily_executions
            ]);
            return;
        }

        // Check conditions
        if (!$workflow->conditionsMet($this->triggerData)) {
            Log::info('Workflow conditions not met', [
                'workflow_id' => $this->workflowId,
                'trigger_type' => $this->triggerType
            ]);
            return;
        }

        // Create execution record
        $execution = WorkflowExecution::create([
            'workflow_id' => $workflow->id,
            'trigger_type' => $this->triggerType,
            'trigger_data' => $this->triggerData,
            'status' => 'pending',
            'priority' => $this->options['priority'] ?? 5,
            'scheduled_at' => now()
        ]);

        // Execute workflow
        $result = $service->executeWorkflow($workflow, $this->triggerData, $execution);
        
        Log::info('Workflow executed', [
            'workflow_id' => $this->workflowId,
            'execution_id' => $execution->id,
            'success' => $result['success'],
            'actions_executed' => $result['actions_executed'] ?? 0
        ]);

        // Dispatch follow-up jobs if needed
        $this->dispatchFollowUpJobs($workflow, $execution, $result);
    }

    /**
     * Process all workflows for trigger type.
     */
    protected function processWorkflowsByTrigger(WorkflowAutomationService $service): void
    {
        $results = $service->triggerWorkflows($this->triggerType, $this->triggerData);
        
        Log::info('Batch workflow processing completed', [
            'trigger_type' => $this->triggerType,
            'triggered' => $results['triggered'],
            'failed' => $results['failed']
        ]);

        // Dispatch individual jobs for failed workflows that need retry
        if (!empty($results['errors'])) {
            $this->dispatchRetryJobs($results['errors']);
        }

        // Dispatch optimization job if many workflows were processed
        if ($results['triggered'] > 5) {
            ProcessWorkflowOptimizationJob::dispatch($this->triggerType)
                ->onQueue('low')
                ->delay(now()->addHours(2));
        }
    }

    /**
     * Dispatch follow-up jobs based on workflow execution results.
     */
    protected function dispatchFollowUpJobs(Workflow $workflow, WorkflowExecution $execution, array $result): void
    {
        // Update workflow metrics
        ProcessWorkflowMetricsJob::dispatch($workflow->id, [
            'execution_id' => $execution->id,
            'success' => $result['success'],
            'execution_time' => $result['execution_time'] ?? 0,
            'actions_executed' => $result['actions_executed'] ?? 0
        ])->onQueue('analytics')->delay(now()->addMinutes(1));

        // Schedule AI optimization if enabled and workflow needs it
        if ($workflow->ai_optimization_enabled && $workflow->needsOptimization()) {
            ProcessWorkflowAIOptimizationJob::dispatch($workflow->id)
                ->onQueue('ai')
                ->delay(now()->addHours(1));
        }

        // Dispatch dependent workflows if any
        if (!empty($result['dependent_workflows'])) {
            foreach ($result['dependent_workflows'] as $dependentWorkflowId) {
                static::dispatch($this->triggerType, $this->triggerData, $dependentWorkflowId, [
                    'priority' => 'high',
                    'parent_execution_id' => $execution->id
                ])->onQueue('high')->delay(now()->addMinutes(2));
            }
        }
    }

    /**
     * Dispatch retry jobs for failed workflows.
     */
    protected function dispatchRetryJobs(array $errors): void
    {
        foreach ($errors as $error) {
            if (isset($error['workflow_id'])) {
                // Calculate retry delay based on workflow priority and failure count
                $workflow = Workflow::find($error['workflow_id']);
                $delay = $workflow ? $this->calculateRetryDelay($workflow) : 300;
                
                static::dispatch($this->triggerType, $this->triggerData, $error['workflow_id'], [
                    'priority' => 'retry',
                    'retry_attempt' => true
                ])->onQueue('retry')->delay(now()->addSeconds($delay));
            }
        }
    }

    /**
     * Calculate retry delay based on workflow characteristics.
     */
    protected function calculateRetryDelay(Workflow $workflow): int
    {
        $baseDelay = 300; // 5 minutes
        $priorityMultiplier = match($workflow->priority) {
            1, 2, 3 => 0.5, // High priority - shorter delay
            4, 5, 6 => 1.0, // Normal priority
            7, 8, 9, 10 => 2.0 // Low priority - longer delay
        };
        
        $failureMultiplier = 1 + ($workflow->consecutive_failures * 0.5);
        
        return (int) ($baseDelay * $priorityMultiplier * $failureMultiplier);
    }

    /**
     * Determine priority based on trigger type.
     */
    protected function determinePriority(string $triggerType): string
    {
        return match($triggerType) {
            'payment_received', 'invoice_paid' => 'high',
            'invoice_overdue', 'contract_expiring' => 'default',
            'client_created', 'invoice_created' => 'default',
            'system_maintenance', 'data_cleanup' => 'low',
            default => 'default'
        };
    }

    /**
     * Handle job failure.
     */
    public function failed(Exception $exception): void
    {
        Log::error('Workflow trigger job permanently failed', [
            'trigger_type' => $this->triggerType,
            'workflow_id' => $this->workflowId,
            'attempts' => $this->attempts(),
            'error' => $exception->getMessage()
        ]);

        // Mark specific workflow as having issues if ID provided
        if ($this->workflowId) {
            $workflow = Workflow::find($this->workflowId);
            if ($workflow) {
                $workflow->recordFailure($exception->getMessage());
            }
        }

        // Dispatch critical failure notification
        ProcessAdminNotificationJob::dispatch([
            'type' => 'workflow_trigger_failure',
            'trigger_type' => $this->triggerType,
            'workflow_id' => $this->workflowId,
            'error' => $exception->getMessage(),
            'attempts' => $this->attempts()
        ])->onQueue('notifications');
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        $tags = ['workflow-automation', "trigger:{$this->triggerType}"];
        
        if ($this->workflowId) {
            $tags[] = "workflow:{$this->workflowId}";
        }
        
        return $tags;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return $this->backoff;
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addHours(3);
    }
}
