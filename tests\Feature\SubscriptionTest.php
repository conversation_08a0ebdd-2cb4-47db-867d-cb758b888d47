<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Plan;
use App\Models\UserSubscription;
use App\Models\Payment;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Carbon\Carbon;

class SubscriptionTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $freePlan;
    protected $proPlan;
    protected $businessPlan;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        
        $this->freePlan = Plan::factory()->create([
            'name' => 'Free',
            'price' => 0,
            'invoice_limit' => 3,
            'features' => json_encode(['basic_invoicing', 'watermark']),
        ]);
        
        $this->proPlan = Plan::factory()->create([
            'name' => 'Pro',
            'price' => 199,
            'invoice_limit' => null,
            'features' => json_encode(['unlimited_invoices', 'no_watermark', 'templates']),
        ]);
        
        $this->businessPlan = Plan::factory()->create([
            'name' => 'Business',
            'price' => 499,
            'invoice_limit' => null,
            'features' => json_encode(['unlimited_invoices', 'no_watermark', 'templates', 'analytics', 'api_access']),
        ]);
    }

    public function test_user_can_view_subscription_plans()
    {
        $this->actingAs($this->user)
            ->get(route('subscriptions.plans'))
            ->assertStatus(200)
            ->assertViewIs('subscriptions.plans')
            ->assertSee($this->freePlan->name)
            ->assertSee($this->proPlan->name)
            ->assertSee($this->businessPlan->name);
    }

    public function test_user_can_subscribe_to_free_plan()
    {
        $this->actingAs($this->user)
            ->post(route('subscriptions.subscribe', $this->freePlan))
            ->assertRedirect(route('dashboard'))
            ->assertSessionHas('success');

        $this->assertDatabaseHas('user_subscriptions', [
            'user_id' => $this->user->id,
            'plan_id' => $this->freePlan->id,
            'status' => 'active',
        ]);
    }

    public function test_user_cannot_subscribe_to_same_plan_twice()
    {
        // Create existing subscription
        UserSubscription::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->proPlan->id,
            'status' => 'active',
        ]);

        $this->actingAs($this->user)
            ->post(route('subscriptions.subscribe', $this->proPlan))
            ->assertRedirect()
            ->assertSessionHas('error');
    }

    public function test_paid_plan_subscription_creates_payment()
    {
        $this->actingAs($this->user)
            ->post(route('subscriptions.subscribe', $this->proPlan))
            ->assertRedirect();

        $this->assertDatabaseHas('payments', [
            'user_id' => $this->user->id,
            'amount' => $this->proPlan->price,
            'status' => 'pending',
        ]);

        $this->assertDatabaseHas('user_subscriptions', [
            'user_id' => $this->user->id,
            'plan_id' => $this->proPlan->id,
            'status' => 'pending',
        ]);
    }

    public function test_user_can_upgrade_subscription()
    {
        // Start with free plan
        $freeSubscription = UserSubscription::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->freePlan->id,
            'status' => 'active',
        ]);

        $this->actingAs($this->user)
            ->post(route('subscriptions.subscribe', $this->proPlan))
            ->assertRedirect();

        // Check old subscription is cancelled
        $this->assertDatabaseHas('user_subscriptions', [
            'id' => $freeSubscription->id,
            'status' => 'cancelled',
        ]);

        // Check new subscription is created
        $this->assertDatabaseHas('user_subscriptions', [
            'user_id' => $this->user->id,
            'plan_id' => $this->proPlan->id,
            'status' => 'pending',
        ]);
    }

    public function test_user_can_downgrade_subscription()
    {
        // Start with pro plan
        $proSubscription = UserSubscription::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->proPlan->id,
            'status' => 'active',
        ]);

        $this->actingAs($this->user)
            ->post(route('subscriptions.subscribe', $this->freePlan))
            ->assertRedirect(route('dashboard'));

        // Check old subscription is cancelled
        $this->assertDatabaseHas('user_subscriptions', [
            'id' => $proSubscription->id,
            'status' => 'cancelled',
        ]);

        // Check new subscription is active (free plan)
        $this->assertDatabaseHas('user_subscriptions', [
            'user_id' => $this->user->id,
            'plan_id' => $this->freePlan->id,
            'status' => 'active',
        ]);
    }

    public function test_user_can_cancel_subscription()
    {
        $subscription = UserSubscription::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->proPlan->id,
            'status' => 'active',
        ]);

        $this->actingAs($this->user)
            ->delete(route('subscriptions.cancel', $subscription))
            ->assertRedirect(route('subscriptions.index'))
            ->assertSessionHas('success');

        $this->assertDatabaseHas('user_subscriptions', [
            'id' => $subscription->id,
            'status' => 'cancelled',
        ]);
    }

    public function test_user_cannot_cancel_other_users_subscription()
    {
        $otherUser = User::factory()->create();
        $otherSubscription = UserSubscription::factory()->create([
            'user_id' => $otherUser->id,
            'plan_id' => $this->proPlan->id,
            'status' => 'active',
        ]);

        $this->actingAs($this->user)
            ->delete(route('subscriptions.cancel', $otherSubscription))
            ->assertStatus(403);
    }

    public function test_subscription_expiry_handling()
    {
        $expiredSubscription = UserSubscription::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->proPlan->id,
            'status' => 'active',
            'expires_at' => Carbon::yesterday(),
        ]);

        // Simulate checking expired subscriptions
        $this->artisan('subscriptions:check-expired');

        $this->assertDatabaseHas('user_subscriptions', [
            'id' => $expiredSubscription->id,
            'status' => 'expired',
        ]);
    }

    public function test_plan_limitation_enforcement()
    {
        // Create user with free plan (3 invoice limit)
        $subscription = UserSubscription::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->freePlan->id,
            'status' => 'active',
        ]);

        // Create 3 invoices (at limit)
        for ($i = 0; $i < 3; $i++) {
            \App\Models\Invoice::factory()->create(['user_id' => $this->user->id]);
        }

        // Try to create 4th invoice - should be blocked
        $this->actingAs($this->user)
            ->get(route('invoices.create'))
            ->assertRedirect()
            ->assertSessionHas('error');
    }

    public function test_unlimited_plan_has_no_limitations()
    {
        // Create user with pro plan (unlimited)
        $subscription = UserSubscription::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->proPlan->id,
            'status' => 'active',
        ]);

        // Create many invoices
        for ($i = 0; $i < 10; $i++) {
            \App\Models\Invoice::factory()->create(['user_id' => $this->user->id]);
        }

        // Should still be able to create more
        $this->actingAs($this->user)
            ->get(route('invoices.create'))
            ->assertStatus(200);
    }

    public function test_subscription_status_display()
    {
        $subscription = UserSubscription::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->proPlan->id,
            'status' => 'active',
            'expires_at' => Carbon::now()->addMonth(),
        ]);

        $this->actingAs($this->user)
            ->get(route('subscriptions.index'))
            ->assertStatus(200)
            ->assertSee($this->proPlan->name)
            ->assertSee('Active')
            ->assertSee($subscription->expires_at->format('M d, Y'));
    }

    public function test_subscription_renewal_notification()
    {
        $subscription = UserSubscription::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->proPlan->id,
            'status' => 'active',
            'expires_at' => Carbon::now()->addDays(3), // Expires in 3 days
        ]);

        $this->actingAs($this->user)
            ->get(route('dashboard'))
            ->assertSee('Your subscription expires soon');
    }

    public function test_subscription_features_access()
    {
        $subscription = UserSubscription::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->proPlan->id,
            'status' => 'active',
        ]);

        $this->assertTrue($this->user->hasFeature('unlimited_invoices'));
        $this->assertTrue($this->user->hasFeature('no_watermark'));
        $this->assertFalse($this->user->hasFeature('api_access')); // Business plan feature
    }
}
