<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Follow-up Details') }}
            </h2>
            <div class="flex space-x-2">
                @if($followup->status !== 'completed')
                    <a href="{{ route('followups.edit', $followup) }}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                        Edit
                    </a>
                @endif
                @if($followup->status === 'scheduled')
                    <form method="POST" action="{{ route('followups.send', $followup) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded" 
                                onclick="return confirm('Send this follow-up now?')">
                            Send Now
                        </button>
                    </form>
                @endif
                @if($followup->status === 'sent')
                    <form method="POST" action="{{ route('followups.mark-completed', $followup) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded" 
                                onclick="return confirm('Mark this follow-up as completed?')">
                            Mark Completed
                        </button>
                    </form>
                @endif
                <a href="{{ route('followups.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Follow-ups
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Follow-up Details -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Basic Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Follow-up Information</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Subject</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $followup->subject }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Status</label>
                                    <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        @if($followup->status === 'completed') bg-purple-100 text-purple-800
                                        @elseif($followup->status === 'sent') bg-green-100 text-green-800
                                        @else bg-yellow-100 text-yellow-800 @endif">
                                        {{ ucfirst($followup->status) }}
                                    </span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Scheduled Date</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $followup->scheduled_date->format('d/m/Y H:i') }}</p>
                                    @if($followup->scheduled_date->isPast() && $followup->status === 'scheduled')
                                        <p class="text-sm text-red-500">Overdue</p>
                                    @endif
                                </div>
                                @if($followup->followupTemplate)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Template Used</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $followup->followupTemplate->name }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Timeline Information -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Timeline</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Created</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $followup->created_at->format('d/m/Y H:i') }}</p>
                                </div>
                                @if($followup->sent_at)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Sent</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $followup->sent_at->format('d/m/Y H:i') }}</p>
                                    </div>
                                @endif
                                @if($followup->completed_at)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Completed</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $followup->completed_at->format('d/m/Y H:i') }}</p>
                                    </div>
                                @endif
                                @if($followup->updated_at != $followup->created_at)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Last Updated</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $followup->updated_at->format('d/m/Y H:i') }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Message Content -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Message Content</h3>
                    <div class="border border-gray-200 rounded-lg p-4 bg-gray-50">
                        <div class="whitespace-pre-wrap text-sm text-gray-900">{{ $followup->message }}</div>
                    </div>
                </div>
            </div>

            <!-- Client Information -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Client Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Name</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $followup->client->name }}</p>
                                </div>
                                @if($followup->client->company_name)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Company</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $followup->client->company_name }}</p>
                                    </div>
                                @endif
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Email</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $followup->client->email }}</p>
                                </div>
                                @if($followup->client->phone)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Phone</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $followup->client->phone }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="flex items-end">
                            <a href="{{ route('clients.show', $followup->client) }}" 
                               class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
                                View Client Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Related Invoice -->
            @if($followup->invoice)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Related Invoice</h3>
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Invoice Number</label>
                                    <p class="mt-1 text-sm font-medium text-gray-900">{{ $followup->invoice->invoice_number }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Invoice Date</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $followup->invoice->invoice_date->format('d/m/Y') }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Amount</label>
                                    <p class="mt-1 text-sm text-gray-900">₹{{ number_format($followup->invoice->total_amount, 2) }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Status</label>
                                    <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        @if($followup->invoice->status === 'paid') bg-green-100 text-green-800
                                        @elseif($followup->invoice->status === 'sent') bg-blue-100 text-blue-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        {{ ucfirst($followup->invoice->status) }}
                                    </span>
                                </div>
                            </div>
                            <div class="mt-4">
                                <a href="{{ route('invoices.show', $followup->invoice) }}" 
                                   class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded text-sm">
                                    View Invoice Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Internal Notes -->
            @if($followup->notes)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Internal Notes</h3>
                        <div class="border border-gray-200 rounded-lg p-4 bg-yellow-50">
                            <div class="whitespace-pre-wrap text-sm text-gray-900">{{ $followup->notes }}</div>
                        </div>
                    </div>
                </div>
            @endif

            <!-- Related Follow-ups -->
            @if($relatedFollowups->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Other Follow-ups for {{ $followup->client->name }}</h3>
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Subject</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Scheduled</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($relatedFollowups as $related)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $related->subject }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $related->scheduled_date->format('d/m/Y H:i') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                                    @if($related->status === 'completed') bg-purple-100 text-purple-800
                                                    @elseif($related->status === 'sent') bg-green-100 text-green-800
                                                    @else bg-yellow-100 text-yellow-800 @endif">
                                                    {{ ucfirst($related->status) }}
                                                </span>
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <a href="{{ route('followups.show', $related) }}" class="text-indigo-600 hover:text-indigo-900">View</a>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
