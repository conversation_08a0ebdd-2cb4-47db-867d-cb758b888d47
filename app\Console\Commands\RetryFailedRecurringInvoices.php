<?php

namespace App\Console\Commands;

use App\Services\RecurringInvoiceService;
use Illuminate\Console\Command;

class RetryFailedRecurringInvoices extends Command
{
    protected $signature = 'invoices:retry-failed-recurring';
    protected $description = 'Retry failed recurring invoice generations with intelligent retry logic';

    protected RecurringInvoiceService $recurringInvoiceService;

    public function __construct(RecurringInvoiceService $recurringInvoiceService)
    {
        parent::__construct();
        $this->recurringInvoiceService = $recurringInvoiceService;
    }

    public function handle(): int
    {
        $this->info('Processing failed recurring invoices for retry...');

        $results = $this->recurringInvoiceService->generateScheduledInvoices();

        $this->info("Generated {$results['generated']} invoices successfully.");
        
        if ($results['retried'] > 0) {
            $this->info("Successfully retried {$results['retried']} previously failed invoices.");
        }
        
        if ($results['auto_sent'] > 0) {
            $this->info("Auto-sent {$results['auto_sent']} invoices.");
        }
        
        if ($results['paused'] > 0) {
            $this->warn("Paused {$results['paused']} recurring invoices due to consecutive failures.");
        }
        
        if ($results['failed'] > 0) {
            $this->warn("Failed to generate {$results['failed']} invoices.");
            foreach ($results['errors'] as $error) {
                $this->error("Recurring ID {$error['recurring_id']}: {$error['error']}");
                if ($error['will_retry']) {
                    $this->line("  → Will retry later (failures: {$error['consecutive_failures']})");
                }
            }
        }

        return Command::SUCCESS;
    }
}
