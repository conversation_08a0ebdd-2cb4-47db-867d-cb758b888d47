<?php if (isset($component)) { $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54 = $attributes; } ?>
<?php $component = App\View\Components\AppLayout::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('app-layout'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\App\View\Components\AppLayout::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('header', null, []); ?> 
        <div class="flex justify-between items-center">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Projects</h1>
                <p class="text-gray-600 mt-1">Manage and track your projects</p>
            </div>
            <a href="<?php echo e(route('projects.create')); ?>" class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-all duration-200 shadow-sm hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                <i class="fas fa-plus mr-2"></i>
                Create New Project
            </a>
        </div>
     <?php $__env->endSlot(); ?>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Filters Section -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 mb-6">
                <div class="p-6">
                    <form method="GET" action="<?php echo e(route('projects.index')); ?>" class="grid grid-cols-1 md:grid-cols-4 gap-4">
                        <div>
                            <label for="status" class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                            <select name="status" id="status" class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                <option value="">All Statuses</option>
                                <option value="planning" <?php echo e(request('status') === 'planning' ? 'selected' : ''); ?>>Planning</option>
                                <option value="active" <?php echo e(request('status') === 'active' ? 'selected' : ''); ?>>Active</option>
                                <option value="on_hold" <?php echo e(request('status') === 'on_hold' ? 'selected' : ''); ?>>On Hold</option>
                                <option value="completed" <?php echo e(request('status') === 'completed' ? 'selected' : ''); ?>>Completed</option>
                                <option value="cancelled" <?php echo e(request('status') === 'cancelled' ? 'selected' : ''); ?>>Cancelled</option>
                            </select>
                        </div>
                        <div>
                            <label for="search" class="block text-sm font-medium text-gray-700 mb-2">Search</label>
                            <input type="text" name="search" id="search" value="<?php echo e(request('search')); ?>" placeholder="Search projects..." class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                        </div>
                        <div>
                            <label for="sort_by" class="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                            <select name="sort_by" id="sort_by" class="w-full rounded-lg border-gray-300 focus:border-blue-500 focus:ring-blue-500">
                                <option value="created_at" <?php echo e(request('sort_by') === 'created_at' ? 'selected' : ''); ?>>Created Date</option>
                                <option value="name" <?php echo e(request('sort_by') === 'name' ? 'selected' : ''); ?>>Name</option>
                                <option value="due_date" <?php echo e(request('sort_by') === 'due_date' ? 'selected' : ''); ?>>Due Date</option>
                                <option value="status" <?php echo e(request('sort_by') === 'status' ? 'selected' : ''); ?>>Status</option>
                            </select>
                        </div>
                        <div class="flex items-end">
                            <button type="submit" class="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors">
                                <i class="fas fa-search mr-2"></i>Filter
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Projects Grid -->
            <?php if($projects->count() > 0): ?>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $project): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
                            <div class="p-6">
                                <div class="flex justify-between items-start mb-4">
                                    <h3 class="text-lg font-semibold text-gray-800 truncate">
                                        <a href="<?php echo e(route('projects.show', $project)); ?>" class="hover:text-blue-600 transition-colors">
                                            <?php echo e($project->name); ?>

                                        </a>
                                    </h3>
                                    <span class="px-2 py-1 text-xs font-medium rounded-full
                                        <?php if($project->status === 'active'): ?> bg-green-100 text-green-800
                                        <?php elseif($project->status === 'planning'): ?> bg-blue-100 text-blue-800
                                        <?php elseif($project->status === 'on_hold'): ?> bg-yellow-100 text-yellow-800
                                        <?php elseif($project->status === 'completed'): ?> bg-gray-100 text-gray-800
                                        <?php else: ?> bg-red-100 text-red-800
                                        <?php endif; ?>">
                                        <?php echo e(ucfirst(str_replace('_', ' ', $project->status))); ?>

                                    </span>
                                </div>

                                <?php if($project->description): ?>
                                    <p class="text-gray-600 text-sm mb-4 line-clamp-2">
                                        <?php echo e($project->description); ?>

                                    </p>
                                <?php endif; ?>

                                <div class="space-y-2 text-sm text-gray-500 mb-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-user w-4 h-4 mr-2"></i>
                                        <span><?php echo e($project->client_name); ?></span>
                                    </div>

                                    <?php if($project->due_date): ?>
                                        <div class="flex items-center <?php echo e($project->is_overdue ? 'text-red-600' : ''); ?>">
                                            <i class="fas fa-calendar w-4 h-4 mr-2"></i>
                                            <span>Due: <?php echo e($project->due_date->format('M j, Y')); ?></span>
                                            <?php if($project->is_overdue): ?>
                                                <span class="ml-1 text-red-600">(Overdue)</span>
                                            <?php endif; ?>
                                        </div>
                                    <?php endif; ?>

                                    <?php if($project->budget): ?>
                                        <div class="flex items-center">
                                            <i class="fas fa-dollar-sign w-4 h-4 mr-2"></i>
                                            <span>Budget: ₹<?php echo e(number_format($project->budget, 2)); ?></span>
                                        </div>
                                    <?php endif; ?>

                                    <div class="flex items-center">
                                        <i class="fas fa-tasks w-4 h-4 mr-2"></i>
                                        <span><?php echo e($project->tasks->count()); ?> tasks</span>
                                    </div>
                                </div>

                                <!-- Progress Bar -->
                                <?php if($project->progress_percentage > 0): ?>
                                    <div class="mb-4">
                                        <div class="flex justify-between text-sm text-gray-600 mb-1">
                                            <span>Progress</span>
                                            <span><?php echo e($project->progress_percentage); ?>%</span>
                                        </div>
                                        <div class="w-full bg-gray-200 rounded-full h-2">
                                            <div class="bg-blue-600 h-2 rounded-full" style="width: <?php echo e($project->progress_percentage); ?>%"></div>
                                        </div>
                                    </div>
                                <?php endif; ?>

                                <div class="flex justify-between items-center pt-4 border-t border-gray-100">
                                    <a href="<?php echo e(route('projects.show', $project)); ?>"
                                       class="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors">
                                        View Details
                                    </a>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('update', $project)): ?>
                                        <div class="flex space-x-2">
                                            <a href="<?php echo e(route('projects.edit', $project)); ?>"
                                               class="text-gray-600 hover:text-gray-800 transition-colors">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>

                <!-- Pagination -->
                <div class="mt-6">
                    <?php echo e($projects->appends(request()->query())->links()); ?>

                </div>
            <?php else: ?>
                <!-- Empty State -->
                <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                    <div class="text-center py-12">
                        <div class="mx-auto w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-project-diagram text-3xl text-gray-400"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No projects found</h3>
                        <p class="text-gray-500 mb-6">
                            <?php if(request()->hasAny(['status', 'search', 'client_id'])): ?>
                                No projects match your current filters. Try adjusting your search criteria.
                            <?php else: ?>
                                Get started by creating your first project to track your work and progress.
                            <?php endif; ?>
                        </p>
                        <div class="flex justify-center space-x-4">
                            <?php if(request()->hasAny(['status', 'search', 'client_id'])): ?>
                                <a href="<?php echo e(route('projects.index')); ?>"
                                   class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors">
                                    <i class="fas fa-times mr-2"></i>Clear Filters
                                </a>
                            <?php endif; ?>
                            <a href="<?php echo e(route('projects.create')); ?>"
                               class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2.5 px-4 rounded-lg transition-colors">
                                <i class="fas fa-plus mr-2"></i>Create Project
                            </a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $attributes = $__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__attributesOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54)): ?>
<?php $component = $__componentOriginal9ac128a9029c0e4701924bd2d73d7f54; ?>
<?php unset($__componentOriginal9ac128a9029c0e4701924bd2d73d7f54); ?>
<?php endif; ?>
<?php /**PATH C:\laragon\www\freeligo\resources\views/projects/index.blade.php ENDPATH**/ ?>