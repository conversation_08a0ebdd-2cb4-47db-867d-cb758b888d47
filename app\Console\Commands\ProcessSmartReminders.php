<?php

namespace App\Console\Commands;

use App\Services\SmartPaymentReminderService;
use Illuminate\Console\Command;

class ProcessSmartReminders extends Command
{
    protected $signature = 'reminders:process-smart';
    protected $description = 'Process smart payment reminders for overdue invoices';

    protected SmartPaymentReminderService $reminderService;

    public function __construct(SmartPaymentReminderService $reminderService)
    {
        parent::__construct();
        $this->reminderService = $reminderService;
    }

    public function handle(): int
    {
        $this->info('Processing smart payment reminders...');

        $results = $this->reminderService->processSmartReminders();

        $this->info("Created {$results['reminders_created']} smart reminders.");

        if ($results['multi_channel_sent'] > 0) {
            $this->info("Configured {$results['multi_channel_sent']} reminders for multi-channel delivery.");
        }

        if ($results['ai_optimized'] > 0) {
            $this->info("Applied AI optimization to {$results['ai_optimized']} reminders.");
        }

        if ($results['failed'] > 0) {
            $this->warn("Failed to create {$results['failed']} reminders.");
            foreach ($results['errors'] as $error) {
                $this->error("Invoice ID {$error['invoice_id']}: {$error['error']}");
            }
        }

        return Command::SUCCESS;
    }
}
