<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class PaymentException extends Exception
{
    protected $gateway;
    protected $paymentId;
    protected $errorCode;

    public function __construct(
        string $message = 'Payment processing failed',
        string $gateway = null,
        string $paymentId = null,
        string $errorCode = null,
        int $code = 0,
        \Throwable $previous = null
    ) {
        parent::__construct($message, $code, $previous);
        
        $this->gateway = $gateway;
        $this->paymentId = $paymentId;
        $this->errorCode = $errorCode;
    }

    /**
     * Render the exception as an HTTP response.
     */
    public function render(Request $request): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'error' => 'Payment processing failed',
                'message' => $this->getMessage(),
                'gateway' => $this->gateway,
                'error_code' => $this->errorCode,
            ], 422);
        }

        return response()->view('errors.payment', [
            'message' => $this->getMessage(),
            'gateway' => $this->gateway,
            'paymentId' => $this->paymentId,
        ], 422);
    }

    /**
     * Get the gateway that caused the error.
     */
    public function getGateway(): ?string
    {
        return $this->gateway;
    }

    /**
     * Get the payment ID that caused the error.
     */
    public function getPaymentId(): ?string
    {
        return $this->paymentId;
    }

    /**
     * Get the error code from the gateway.
     */
    public function getErrorCode(): ?string
    {
        return $this->errorCode;
    }
}
