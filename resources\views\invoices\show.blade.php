<x-app-layout>
    <x-slot name="header">
        <div class="flex justify-between items-center">
            <h2 class="font-semibold text-xl text-gray-800 leading-tight">
                {{ __('Invoice: ') . $invoice->invoice_number }}
            </h2>
            <div class="flex space-x-2">
                @if($invoice->status !== 'paid')
                    <a href="{{ route('invoices.edit', $invoice) }}" class="bg-yellow-500 hover:bg-yellow-700 text-white font-bold py-2 px-4 rounded">
                        Edit Invoice
                    </a>
                @endif
                <a href="{{ route('invoices.download', $invoice) }}" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                    Download PDF
                </a>
                @if($invoice->status !== 'paid')
                    <form method="POST" action="{{ route('invoices.mark-paid', $invoice) }}" class="inline">
                        @csrf
                        @method('PATCH')
                        <button type="submit" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded" 
                                onclick="return confirm('Mark this invoice as paid?')">
                            Mark as Paid
                        </button>
                    </form>
                @endif
                @if($invoice->isOverdue() && $invoice->status !== 'paid')
                    <a href="{{ route('followups.create', ['invoice_id' => $invoice->id]) }}" class="bg-purple-500 hover:bg-purple-700 text-white font-bold py-2 px-4 rounded">
                        Follow Up
                    </a>
                @endif
                <a href="{{ route('invoices.index') }}" class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded">
                    Back to Invoices
                </a>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-6xl mx-auto sm:px-6 lg:px-8 space-y-6">
            <!-- Invoice Header -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Invoice Info -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Invoice Details</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Invoice Number</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $invoice->invoice_number }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Status</label>
                                    <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        @if($invoice->status === 'paid') bg-green-100 text-green-800
                                        @elseif($invoice->status === 'sent') bg-blue-100 text-blue-800
                                        @elseif($invoice->isOverdue()) bg-red-100 text-red-800
                                        @else bg-gray-100 text-gray-800 @endif">
                                        @if($invoice->isOverdue() && $invoice->status !== 'paid')
                                            Overdue
                                        @else
                                            {{ ucfirst($invoice->status) }}
                                        @endif
                                    </span>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Invoice Date</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $invoice->invoice_date->format('d/m/Y') }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Due Date</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        {{ $invoice->due_date->format('d/m/Y') }}
                                        @if($invoice->isOverdue())
                                            <span class="text-red-500 text-xs ml-2">({{ $invoice->due_date->diffForHumans() }})</span>
                                        @endif
                                    </p>
                                </div>
                                @if($invoice->paid_date)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Paid Date</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $invoice->paid_date->format('d/m/Y') }}</p>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Client Info -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Client Details</h3>
                            <div class="space-y-3">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Name</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $invoice->client->name }}</p>
                                </div>
                                @if($invoice->client->company_name)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Company</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $invoice->client->company_name }}</p>
                                    </div>
                                @endif
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Email</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $invoice->client->email }}</p>
                                </div>
                                @if($invoice->client->phone)
                                    <div>
                                        <label class="block text-sm font-medium text-gray-500">Phone</label>
                                        <p class="mt-1 text-sm text-gray-900">{{ $invoice->client->phone }}</p>
                                    </div>
                                @endif
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Type</label>
                                    <span class="mt-1 inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                        {{ $invoice->client->type === 'company' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                                        {{ ucfirst($invoice->client->type) }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Invoice Items -->
            <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                <div class="p-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Invoice Items</h3>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200">
                            <thead class="bg-gray-50">
                                <tr>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                                @foreach($invoice->items as $item)
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->description }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ $item->quantity }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{{ number_format($item->rate, 2) }}</td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">₹{{ number_format($item->amount, 2) }}</td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>

                    <!-- Totals -->
                    <div class="mt-6 flex justify-end">
                        <div class="w-64 bg-gray-50 p-4 rounded-lg">
                            <div class="space-y-2">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Subtotal:</span>
                                    <span class="text-sm font-medium">₹{{ number_format($invoice->subtotal, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Tax (18%):</span>
                                    <span class="text-sm font-medium">₹{{ number_format($invoice->tax_amount, 2) }}</span>
                                </div>
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-600">Total:</span>
                                    <span class="text-sm font-medium">₹{{ number_format($invoice->total_amount, 2) }}</span>
                                </div>
                                @if($invoice->tds_amount > 0)
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-600">TDS ({{ $invoice->tds_percentage }}%):</span>
                                        <span class="text-sm font-medium text-red-600">₹{{ number_format($invoice->tds_amount, 2) }}</span>
                                    </div>
                                @endif
                                <div class="flex justify-between border-t pt-2">
                                    <span class="text-base font-medium text-gray-900">Net Amount:</span>
                                    <span class="text-base font-bold text-gray-900">₹{{ number_format($invoice->net_amount, 2) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Notes -->
            @if($invoice->notes)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Notes</h3>
                        <p class="text-sm text-gray-700 whitespace-pre-line">{{ $invoice->notes }}</p>
                    </div>
                </div>
            @endif

            <!-- TDS Record -->
            @if($invoice->tdsRecord)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">TDS Record</h3>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-500">TDS Amount</label>
                                <p class="mt-1 text-sm text-gray-900">₹{{ number_format($invoice->tdsRecord->tds_amount, 2) }}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-500">Financial Year</label>
                                <p class="mt-1 text-sm text-gray-900">{{ $invoice->tdsRecord->financial_year }}</p>
                            </div>
                            @if($invoice->tdsRecord->certificate_number)
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Certificate Number</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $invoice->tdsRecord->certificate_number }}</p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            <!-- Follow-ups -->
            @if($invoice->followUps->count() > 0)
                <div class="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Follow-ups</h3>
                        <div class="space-y-3">
                            @foreach($invoice->followUps as $followUp)
                                <div class="border border-gray-200 rounded-lg p-4">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">{{ $followUp->subject }}</p>
                                            <p class="text-xs text-gray-500">{{ $followUp->scheduled_date->format('d/m/Y H:i') }}</p>
                                        </div>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full 
                                            {{ $followUp->status === 'sent' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                            {{ ucfirst($followUp->status) }}
                                        </span>
                                    </div>
                                    @if($followUp->message)
                                        <p class="mt-2 text-sm text-gray-700">{{ Str::limit($followUp->message, 100) }}</p>
                                    @endif
                                </div>
                            @endforeach
                        </div>
                    </div>
                </div>
            @endif
        </div>
    </div>
</x-app-layout>
