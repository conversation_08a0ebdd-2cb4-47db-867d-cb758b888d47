<?php

namespace Database\Seeders;

use App\Models\TdsRateConfiguration;
use Illuminate\Database\Seeder;

class TdsRateConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $currentYear = $this->getCurrentFinancialYear();
        
        $configurations = [
            // Professional Services - Section 194J
            [
                'service_type' => 'professional_services',
                'client_type' => 'company',
                'tds_rate' => 10.00,
                'threshold_amount' => 0,
                'section_code' => '194J',
                'description' => 'Professional services to companies',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],
            [
                'service_type' => 'professional_services',
                'client_type' => 'individual',
                'tds_rate' => 10.00,
                'threshold_amount' => 30000,
                'section_code' => '194J',
                'description' => 'Professional services to individuals (threshold ₹30,000)',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],
            [
                'service_type' => 'professional_services',
                'client_type' => 'government',
                'tds_rate' => 10.00,
                'threshold_amount' => 0,
                'section_code' => '194J',
                'description' => 'Professional services to government entities',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],

            // Technical Services - Section 194J
            [
                'service_type' => 'technical_services',
                'client_type' => 'company',
                'tds_rate' => 2.00,
                'threshold_amount' => 0,
                'section_code' => '194J',
                'description' => 'Technical services to companies',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],
            [
                'service_type' => 'technical_services',
                'client_type' => 'individual',
                'tds_rate' => 2.00,
                'threshold_amount' => 30000,
                'section_code' => '194J',
                'description' => 'Technical services to individuals (threshold ₹30,000)',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],

            // Software Development - Section 194J
            [
                'service_type' => 'software_development',
                'client_type' => 'company',
                'tds_rate' => 2.00,
                'threshold_amount' => 0,
                'section_code' => '194J',
                'description' => 'Software development services to companies',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],
            [
                'service_type' => 'software_development',
                'client_type' => 'individual',
                'tds_rate' => 2.00,
                'threshold_amount' => 30000,
                'section_code' => '194J',
                'description' => 'Software development services to individuals',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],

            // Consultancy - Section 194J
            [
                'service_type' => 'consultancy',
                'client_type' => 'company',
                'tds_rate' => 10.00,
                'threshold_amount' => 0,
                'section_code' => '194J',
                'description' => 'Consultancy services to companies',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],
            [
                'service_type' => 'consultancy',
                'client_type' => 'individual',
                'tds_rate' => 10.00,
                'threshold_amount' => 30000,
                'section_code' => '194J',
                'description' => 'Consultancy services to individuals',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],

            // Design Services - Section 194J
            [
                'service_type' => 'design_services',
                'client_type' => 'company',
                'tds_rate' => 10.00,
                'threshold_amount' => 0,
                'section_code' => '194J',
                'description' => 'Design services to companies',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],
            [
                'service_type' => 'design_services',
                'client_type' => 'individual',
                'tds_rate' => 10.00,
                'threshold_amount' => 30000,
                'section_code' => '194J',
                'description' => 'Design services to individuals',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],

            // Marketing Services - Section 194J
            [
                'service_type' => 'marketing_services',
                'client_type' => 'company',
                'tds_rate' => 10.00,
                'threshold_amount' => 0,
                'section_code' => '194J',
                'description' => 'Marketing services to companies',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],
            [
                'service_type' => 'marketing_services',
                'client_type' => 'individual',
                'tds_rate' => 10.00,
                'threshold_amount' => 30000,
                'section_code' => '194J',
                'description' => 'Marketing services to individuals',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],

            // Legal Services - Section 194J
            [
                'service_type' => 'legal_services',
                'client_type' => 'company',
                'tds_rate' => 10.00,
                'threshold_amount' => 0,
                'section_code' => '194J',
                'description' => 'Legal services to companies',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],
            [
                'service_type' => 'legal_services',
                'client_type' => 'individual',
                'tds_rate' => 10.00,
                'threshold_amount' => 30000,
                'section_code' => '194J',
                'description' => 'Legal services to individuals',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],

            // Accounting Services - Section 194J
            [
                'service_type' => 'accounting_services',
                'client_type' => 'company',
                'tds_rate' => 10.00,
                'threshold_amount' => 0,
                'section_code' => '194J',
                'description' => 'Accounting services to companies',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],
            [
                'service_type' => 'accounting_services',
                'client_type' => 'individual',
                'tds_rate' => 10.00,
                'threshold_amount' => 30000,
                'section_code' => '194J',
                'description' => 'Accounting services to individuals',
                'financial_year' => $currentYear,
                'conditions' => [],
            ],

            // Contract Services - Section 194C
            [
                'service_type' => 'other',
                'client_type' => 'company',
                'tds_rate' => 1.00,
                'threshold_amount' => 30000,
                'section_code' => '194C',
                'description' => 'Contract services to companies (single contract)',
                'financial_year' => $currentYear,
                'conditions' => [
                    ['field' => 'contract_type', 'operator' => '=', 'value' => 'single'],
                ],
            ],
            [
                'service_type' => 'other',
                'client_type' => 'company',
                'tds_rate' => 2.00,
                'threshold_amount' => 100000,
                'section_code' => '194C',
                'description' => 'Contract services to companies (aggregate)',
                'financial_year' => $currentYear,
                'conditions' => [
                    ['field' => 'contract_type', 'operator' => '=', 'value' => 'aggregate'],
                ],
            ],
        ];

        foreach ($configurations as $config) {
            TdsRateConfiguration::updateOrCreate(
                [
                    'service_type' => $config['service_type'],
                    'client_type' => $config['client_type'],
                    'section_code' => $config['section_code'],
                    'financial_year' => $config['financial_year'],
                ],
                $config
            );
        }

        $this->command->info('TDS rate configurations seeded successfully.');
    }

    /**
     * Get current financial year.
     */
    protected function getCurrentFinancialYear(): string
    {
        $currentDate = now();
        $year = $currentDate->year;
        
        // Financial year starts from April 1st
        if ($currentDate->month >= 4) {
            return $year . '-' . ($year + 1);
        } else {
            return ($year - 1) . '-' . $year;
        }
    }
}
