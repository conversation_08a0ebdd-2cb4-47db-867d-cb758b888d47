<?php

namespace App\Http\Controllers;

use App\Services\AutomationAnalyticsService;
use App\Services\WorkflowAutomationService;
use App\Services\NotificationService;
use App\Models\Workflow;
use App\Models\NotificationTemplate;
use App\Models\NotificationPreference;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AutomationController extends Controller
{
    protected $analyticsService;
    protected $workflowService;
    protected $notificationService;

    public function __construct(
        AutomationAnalyticsService $analyticsService,
        WorkflowAutomationService $workflowService,
        NotificationService $notificationService
    ) {
        $this->analyticsService = $analyticsService;
        $this->workflowService = $workflowService;
        $this->notificationService = $notificationService;
    }

    /**
     * Show automation dashboard.
     */
    public function dashboard(Request $request)
    {
        $period = $request->get('period', '30_days');
        $userId = Auth::id();

        // Get user-specific automation data
        $automationStats = $this->getUserAutomationStats($userId, $period);
        $recentExecutions = $this->getUserRecentExecutions($userId);
        $activeWorkflows = $this->workflowService->getUserActiveWorkflows($userId);
        $automationSettings = $this->getUserAutomationSettings($userId);

        return view('automation.dashboard', compact(
            'automationStats',
            'recentExecutions',
            'activeWorkflows',
            'automationSettings',
            'period'
        ));
    }

    /**
     * Show workflows management.
     */
    public function workflows()
    {
        $workflows = Workflow::where('user_id', Auth::id())
            ->withCount('executions')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('automation.workflows', compact('workflows'));
    }

    /**
     * Create workflow.
     */
    public function createWorkflow(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'trigger_type' => 'required|string',
            'trigger_conditions' => 'nullable|array',
            'actions' => 'required|array',
        ]);

        $workflow = Workflow::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
            'description' => $request->description,
            'trigger_type' => $request->trigger_type,
            'trigger_conditions' => $request->trigger_conditions ?? [],
            'actions' => $request->actions,
            'status' => 'active',
            'is_active' => true,
        ]);

        return response()->json([
            'success' => true,
            'workflow' => $workflow,
            'message' => 'Workflow created successfully'
        ]);
    }

    /**
     * Update workflow.
     */
    public function updateWorkflow(Request $request, Workflow $workflow)
    {
        $this->authorize('update', $workflow);

        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'trigger_conditions' => 'nullable|array',
            'actions' => 'required|array',
            'is_active' => 'boolean',
        ]);

        $workflow->update($request->only([
            'name', 'description', 'trigger_conditions', 'actions', 'is_active'
        ]));

        return response()->json([
            'success' => true,
            'workflow' => $workflow,
            'message' => 'Workflow updated successfully'
        ]);
    }

    /**
     * Delete workflow.
     */
    public function deleteWorkflow(Workflow $workflow)
    {
        $this->authorize('delete', $workflow);

        $workflow->delete();

        return response()->json([
            'success' => true,
            'message' => 'Workflow deleted successfully'
        ]);
    }

    /**
     * Toggle workflow status.
     */
    public function toggleWorkflow(Workflow $workflow)
    {
        $this->authorize('update', $workflow);

        $workflow->update(['is_active' => !$workflow->is_active]);

        return response()->json([
            'success' => true,
            'is_active' => $workflow->is_active,
            'message' => 'Workflow status updated'
        ]);
    }

    /**
     * Show notification templates.
     */
    public function templates()
    {
        $templates = NotificationTemplate::where('user_id', Auth::id())
            ->orderBy('type')
            ->orderBy('channel')
            ->get()
            ->groupBy(['type', 'channel']);

        return view('automation.templates', compact('templates'));
    }

    /**
     * Create notification template.
     */
    public function createTemplate(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|string',
            'channel' => 'required|string|in:email,whatsapp,sms',
            'subject_template' => 'nullable|string',
            'body_template' => 'required|string',
            'variables' => 'nullable|array',
        ]);

        $template = NotificationTemplate::create([
            'user_id' => Auth::id(),
            'name' => $request->name,
            'type' => $request->type,
            'channel' => $request->channel,
            'subject_template' => $request->subject_template,
            'body_template' => $request->body_template,
            'variables' => $request->variables ?? [],
            'is_active' => true,
        ]);

        return response()->json([
            'success' => true,
            'template' => $template,
            'message' => 'Template created successfully'
        ]);
    }

    /**
     * Update notification template.
     */
    public function updateTemplate(Request $request, NotificationTemplate $template)
    {
        $this->authorize('update', $template);

        $request->validate([
            'name' => 'required|string|max:255',
            'subject_template' => 'nullable|string',
            'body_template' => 'required|string',
            'variables' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        $template->update($request->only([
            'name', 'subject_template', 'body_template', 'variables', 'is_active'
        ]));

        return response()->json([
            'success' => true,
            'template' => $template,
            'message' => 'Template updated successfully'
        ]);
    }

    /**
     * Show notification preferences.
     */
    public function preferences()
    {
        $preferences = NotificationPreference::where('user_id', Auth::id())
            ->orderBy('notification_type')
            ->get()
            ->groupBy('notification_type');

        return view('automation.preferences', compact('preferences'));
    }

    /**
     * Update notification preferences.
     */
    public function updatePreferences(Request $request)
    {
        $request->validate([
            'preferences' => 'required|array',
            'preferences.*.id' => 'required|exists:notification_preferences,id',
            'preferences.*.email_enabled' => 'boolean',
            'preferences.*.whatsapp_enabled' => 'boolean',
            'preferences.*.sms_enabled' => 'boolean',
            'preferences.*.preferred_channel' => 'required|string',
            'preferences.*.preferred_time' => 'nullable|date_format:H:i:s',
            'preferences.*.quiet_hours_start' => 'nullable|date_format:H:i:s',
            'preferences.*.quiet_hours_end' => 'nullable|date_format:H:i:s',
        ]);

        foreach ($request->preferences as $prefData) {
            $preference = NotificationPreference::find($prefData['id']);
            if ($preference && $preference->user_id === Auth::id()) {
                $preference->update($prefData);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Preferences updated successfully'
        ]);
    }

    /**
     * Initialize default automation setup.
     */
    public function initializeDefaults()
    {
        $userId = Auth::id();

        // Create default workflows
        $workflows = $this->workflowService->createPredefinedWorkflows($userId);

        // Create default templates
        $templates = $this->notificationService->createDefaultTemplates($userId);

        // Create default preferences
        $preferences = $this->notificationService->createDefaultPreferences($userId);

        return response()->json([
            'success' => true,
            'created' => [
                'workflows' => count($workflows),
                'templates' => count($templates),
                'preferences' => count($preferences),
            ],
            'message' => 'Default automation setup completed'
        ]);
    }

    /**
     * Get user-specific automation statistics.
     */
    protected function getUserAutomationStats(int $userId, string $period): array
    {
        $days = $this->getPeriodDays($period);
        $startDate = \Carbon\Carbon::now()->subDays($days);

        return [
            'recurring_invoices' => [
                'total' => \App\Models\RecurringInvoice::where('user_id', $userId)->count(),
                'active' => \App\Models\RecurringInvoice::where('user_id', $userId)->where('status', 'active')->count(),
                'generated_this_period' => \App\Models\RecurringInvoice::where('user_id', $userId)
                    ->where('last_generated_at', '>=', $startDate)->count(),
            ],
            'payment_reminders' => [
                'total_sent' => \App\Models\FollowUp::where('user_id', $userId)
                    ->where('type', 'payment_reminder')
                    ->where('created_at', '>=', $startDate)->count(),
                'successful' => \App\Models\FollowUp::where('user_id', $userId)
                    ->where('type', 'payment_reminder')
                    ->where('status', 'completed')
                    ->where('created_at', '>=', $startDate)->count(),
            ],
            'tds_calculations' => [
                'auto_calculated' => \App\Models\TdsRecord::where('user_id', $userId)
                    ->where('is_auto_calculated', true)
                    ->where('created_at', '>=', $startDate)->count(),
                'compliance_rate' => $this->getTdsComplianceRate($userId, $startDate),
            ],
            'workflow_executions' => [
                'total' => \App\Models\WorkflowExecution::where('user_id', $userId)
                    ->where('created_at', '>=', $startDate)->count(),
                'successful' => \App\Models\WorkflowExecution::where('user_id', $userId)
                    ->where('status', 'completed')
                    ->where('created_at', '>=', $startDate)->count(),
            ],
        ];
    }

    /**
     * Get user's recent automation executions.
     */
    protected function getUserRecentExecutions(int $userId): array
    {
        return \App\Models\WorkflowExecution::where('user_id', $userId)
            ->with(['workflow'])
            ->latest()
            ->take(10)
            ->get()
            ->toArray();
    }

    /**
     * Get user's automation settings.
     */
    protected function getUserAutomationSettings(int $userId): array
    {
        $preferences = \App\Models\NotificationPreference::where('user_id', $userId)->first();

        return [
            'email_notifications' => $preferences->email_notifications ?? true,
            'sms_notifications' => $preferences->sms_notifications ?? false,
            'auto_invoice_generation' => $preferences->auto_invoice_generation ?? true,
            'auto_payment_reminders' => $preferences->auto_payment_reminders ?? true,
            'auto_tds_calculation' => $preferences->auto_tds_calculation ?? true,
        ];
    }

    /**
     * Get TDS compliance rate for user.
     */
    protected function getTdsComplianceRate(int $userId, \Carbon\Carbon $startDate): float
    {
        $total = \App\Models\TdsRecord::where('user_id', $userId)
            ->where('created_at', '>=', $startDate)->count();

        $compliant = \App\Models\TdsRecord::where('user_id', $userId)
            ->where('compliance_status', 'compliant')
            ->where('created_at', '>=', $startDate)->count();

        return $total > 0 ? round(($compliant / $total) * 100, 2) : 0;
    }

    /**
     * Convert period string to days.
     */
    protected function getPeriodDays(string $period): int
    {
        return match($period) {
            '7_days' => 7,
            '30_days' => 30,
            '90_days' => 90,
            default => 30,
        };
    }

    /**
     * Get automation analytics API.
     */
    public function analyticsApi(Request $request)
    {
        $period = $request->get('period', '30_days');
        $userId = Auth::id();

        $data = $this->analyticsService->getDashboardData($userId, $period);

        return response()->json([
            'success' => true,
            'data' => $data
        ]);
    }

    /**
     * Test workflow execution.
     */
    public function testWorkflow(Request $request, Workflow $workflow)
    {
        $this->authorize('update', $workflow);

        $request->validate([
            'test_data' => 'required|array',
        ]);

        try {
            $execution = $this->workflowService->executeWorkflow($workflow, $request->test_data);

            return response()->json([
                'success' => true,
                'execution' => $execution,
                'message' => 'Workflow test completed'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => $e->getMessage(),
                'message' => 'Workflow test failed'
            ], 422);
        }
    }
}
