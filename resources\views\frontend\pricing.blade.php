<x-frontend-layout>
    <x-slot name="title">Pricing - Freeligo</x-slot>
    <x-slot name="description">Choose the perfect plan for your freelance business. Transparent pricing with no hidden fees. Start with our free trial.</x-slot>
<!-- Hero Section -->
<section class="bg-gradient-to-br from-slate-50 via-emerald-50 to-teal-50 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-heading font-bold text-gray-800 mb-6">
            Simple, Transparent
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600">Pricing</span>
        </h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Start free forever and upgrade as you grow. No credit card required to get started.
        </p>
    </div>
</section>

<!-- Pricing Cards -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 lg:gap-8">
            @foreach($plans as $plan)
            <div class="bg-white rounded-2xl border {{ $plan->is_popular ? 'border-2 border-emerald-500' : 'border border-gray-200' }} p-6 sm:p-8 hover:shadow-lg transition-shadow {{ $plan->is_popular ? 'relative' : '' }}">
                @if($plan->is_popular)
                <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span class="bg-emerald-500 text-white px-4 py-2 rounded-full text-sm font-medium">Most Popular</span>
                </div>
                @endif

                <div class="text-center">
                    <h3 class="text-xl sm:text-2xl font-heading font-bold text-gray-900 mb-2">{{ $plan->name }}</h3>
                    <p class="text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base">{{ $plan->description }}</p>
                    <div class="mb-4 sm:mb-6">
                        <span class="text-3xl sm:text-4xl font-bold {{ $plan->is_popular ? 'text-emerald-600' : ($plan->price == 0 ? 'text-emerald-600' : 'text-gray-900') }}">
                            {{ $plan->formatted_price }}
                        </span>
                        <span class="text-gray-600 text-sm sm:text-base">/{{ $plan->billing_cycle }}</span>
                    </div>

                    @if($plan->slug === 'free')
                        <a href="{{ route('register') }}" class="w-full bg-emerald-600 text-white py-3 px-6 rounded-lg font-semibold hover:bg-emerald-700 transition-colors block text-center mb-4 sm:mb-6 touch-manipulation">
                            Get Started Free
                        </a>
                    @elseif($plan->slug === 'pro')
                        <a href="{{ route('register') }}" class="w-full bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-3 px-6 rounded-xl font-semibold hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 transform hover:scale-105 block text-center mb-6 shadow-lg">
                            Start Free Trial
                        </a>
                    @else
                        <a href="{{ route('contact') }}" class="w-full bg-gray-900 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-800 transition-colors block text-center mb-6">
                            Contact Sales
                        </a>
                    @endif
                </div>

                <ul class="space-y-4">
                    @foreach($plan->planFeatures as $feature)
                        @if($feature->feature_type === 'boolean' && $feature->getBooleanValue())
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">{{ $feature->feature_name }}</span>
                            </li>
                        @elseif($feature->feature_type === 'limit')
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">
                                    {{ $feature->feature_name }}:
                                    @if($feature->feature_value === 'unlimited')
                                        Unlimited
                                    @else
                                        {{ $feature->feature_value }}
                                    @endif
                                </span>
                            </li>
                        @elseif($feature->feature_type === 'text')
                            <li class="flex items-center">
                                <svg class="w-5 h-5 text-emerald-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span class="text-gray-700">{{ $feature->feature_value }}</span>
                            </li>
                        @endif
                    @endforeach
                </ul>
            </div>
            @endforeach
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-heading font-bold text-gray-900 mb-4">
                Frequently Asked Questions
            </h2>
            <p class="text-xl text-gray-600">
                Everything you need to know about our pricing and plans.
            </p>
        </div>

        <div x-data="{ openFaq: null }" class="space-y-4">
            <div class="bg-white rounded-lg border border-gray-200">
                <button @click="openFaq = openFaq === 1 ? null : 1" class="w-full px-6 py-4 text-left flex items-center justify-between">
                    <span class="font-semibold text-gray-900">Can I change my plan at any time?</span>
                    <svg class="w-5 h-5 text-gray-500 transform transition-transform" :class="openFaq === 1 ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 1" x-transition class="px-6 pb-4">
                    <p class="text-gray-600">Yes, you can upgrade or downgrade your plan at any time. Changes will be reflected in your next billing cycle.</p>
                </div>
            </div>

            <div class="bg-white rounded-lg border border-gray-200">
                <button @click="openFaq = openFaq === 2 ? null : 2" class="w-full px-6 py-4 text-left flex items-center justify-between">
                    <span class="font-semibold text-gray-900">Is there really a free plan?</span>
                    <svg class="w-5 h-5 text-gray-500 transform transition-transform" :class="openFaq === 2 ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 2" x-transition class="px-6 pb-4">
                    <p class="text-gray-600">Yes! Our Free plan is completely free forever. You can create up to 3 invoices per month and access basic features. No credit card required to get started.</p>
                </div>
            </div>

            <div class="bg-white rounded-lg border border-gray-200">
                <button @click="openFaq = openFaq === 3 ? null : 3" class="w-full px-6 py-4 text-left flex items-center justify-between">
                    <span class="font-semibold text-gray-900">What payment methods do you accept?</span>
                    <svg class="w-5 h-5 text-gray-500 transform transition-transform" :class="openFaq === 3 ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 3" x-transition class="px-6 pb-4">
                    <p class="text-gray-600">We accept all major credit cards (Visa, MasterCard, American Express) and PayPal for your convenience.</p>
                </div>
            </div>

            <div class="bg-white rounded-lg border border-gray-200">
                <button @click="openFaq = openFaq === 4 ? null : 4" class="w-full px-6 py-4 text-left flex items-center justify-between">
                    <span class="font-semibold text-gray-900">Can I cancel my subscription?</span>
                    <svg class="w-5 h-5 text-gray-500 transform transition-transform" :class="openFaq === 4 ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 4" x-transition class="px-6 pb-4">
                    <p class="text-gray-600">Yes, you can cancel your subscription at any time. You'll continue to have access until the end of your current billing period.</p>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="bg-gradient-to-r from-emerald-600 to-teal-600 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-heading font-bold text-white mb-4">
            Ready to Get Started?
        </h2>
        <p class="text-xl text-emerald-100 mb-8 max-w-2xl mx-auto">
            Join thousands of freelancers who trust Freeligo to manage their business.
        </p>
        <a href="{{ route('register') }}" class="bg-white text-emerald-600 px-8 py-4 rounded-xl font-semibold hover:bg-emerald-50 transition-colors inline-block">
            Get Started Free
        </a>
    </div>
</section>
</x-frontend-layout>
