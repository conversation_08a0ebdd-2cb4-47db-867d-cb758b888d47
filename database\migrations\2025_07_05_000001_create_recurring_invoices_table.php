<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recurring_invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->json('template_data');
            $table->enum('frequency', ['daily', 'weekly', 'monthly', 'quarterly', 'yearly']);
            $table->integer('interval_count')->default(1);
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->date('next_generation_date');
            $table->enum('status', ['active', 'paused', 'cancelled', 'completed'])->default('active');
            $table->boolean('auto_send')->default(false);
            $table->timestamp('last_generated_at')->nullable();
            $table->integer('total_generated')->default(0);
            $table->integer('max_occurrences')->nullable();

            // Enhanced automation fields
            $table->json('automation_settings')->nullable();
            $table->decimal('success_rate', 5, 2)->default(100.00);
            $table->integer('consecutive_failures')->default(0);
            $table->timestamp('last_failure_at')->nullable();
            $table->json('client_behavior_data')->nullable();
            $table->boolean('dynamic_frequency_enabled')->default(false);
            $table->string('original_frequency')->nullable();
            $table->json('frequency_adjustment_history')->nullable();
            $table->boolean('ai_description_enabled')->default(false);
            $table->json('retry_settings')->nullable();

            $table->timestamps();

            // Performance indexes
            $table->index('status');
            $table->index('next_generation_date');
            $table->index(['user_id', 'status']);
            $table->index(['client_id', 'status']);
            $table->index('frequency');
            $table->index('success_rate');
            $table->index('consecutive_failures');
            $table->index('dynamic_frequency_enabled');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recurring_invoices');
    }
};
