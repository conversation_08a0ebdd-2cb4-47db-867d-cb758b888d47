<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recurring_invoices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('client_id')->constrained()->onDelete('cascade');
            $table->json('template_data');
            $table->enum('frequency', ['daily', 'weekly', 'monthly', 'quarterly', 'yearly']);
            $table->integer('interval_count')->default(1);
            $table->date('start_date');
            $table->date('end_date')->nullable();
            $table->date('next_generation_date');
            $table->enum('status', ['active', 'paused', 'cancelled', 'completed'])->default('active');
            $table->boolean('auto_send')->default(false);
            $table->timestamp('last_generated_at')->nullable();
            $table->integer('total_generated')->default(0);
            $table->integer('max_occurrences')->nullable();
            $table->timestamps();

            // Performance indexes
            $table->index('status');
            $table->index('next_generation_date');
            $table->index(['user_id', 'status']);
            $table->index(['client_id', 'status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recurring_invoices');
    }
};
