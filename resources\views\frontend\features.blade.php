<x-frontend-layout>
    <x-slot name="title">Features - Freeligo</x-slot>
    <x-slot name="description">Discover all the powerful features that make Freeligo the perfect freelance management platform. Professional invoicing, contracts, client management, and more.</x-slot>
<!-- Hero Section -->
<section class="bg-gradient-to-br from-slate-50 via-emerald-50 to-teal-50 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-heading font-bold text-gray-800 mb-6">
            Powerful Features for
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600">Modern Freelancers</span>
        </h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Everything you need to run a professional freelance business, from client management to AI-powered document generation.
        </p>
    </div>
</section>

<!-- Features Grid -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
            <div>
                <div class="w-16 h-16 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-heading font-bold text-gray-800 mb-4">Professional Invoicing</h2>
                <p class="text-lg text-gray-600 mb-6">Create stunning, professional invoices that get you paid faster. Our invoicing system handles everything from tax calculations to payment tracking.</p>
                <ul class="space-y-3">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700">Customizable invoice templates</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700">Automatic tax calculations</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700">Payment status tracking</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700">PDF generation and download</span>
                    </li>
                </ul>
            </div>
            <div class="bg-gray-50 rounded-2xl p-8">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-gray-900">Invoice #INV-2024-001</h3>
                        <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">Pending</span>
                    </div>
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Website Development</span>
                            <span>$3,500.00</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">SEO Optimization</span>
                            <span>$800.00</span>
                        </div>
                    </div>
                    <div class="border-t pt-2">
                        <div class="flex justify-between font-bold">
                            <span>Total</span>
                            <span class="text-emerald-600">$4,300.00</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center mb-20">
            <div class="order-2 lg:order-1 bg-gray-50 rounded-2xl p-8">
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="font-semibold text-gray-900">Service Agreement</h3>
                        <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm">Signed</span>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm text-gray-600">Digital signature received</span>
                        </div>
                        <div class="text-sm text-gray-500">
                            Project: E-commerce Platform<br>
                            Duration: 3 months<br>
                            Value: $15,000
                        </div>
                    </div>
                </div>
            </div>
            <div class="order-1 lg:order-2">
                <div class="w-16 h-16 bg-gradient-to-br from-slate-100 to-gray-100 rounded-xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h2 class="text-3xl font-heading font-bold text-gray-800 mb-4">Smart Contract Management</h2>
                <p class="text-lg text-gray-600 mb-6">Generate, customize, and manage contracts with ease. Digital signatures and automated workflows keep your projects moving forward.</p>
                <ul class="space-y-3">
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700">Pre-built contract templates</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700">Digital signature integration</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700">Automated reminders</span>
                    </li>
                    <li class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700">Version control and history</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Additional Features Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div class="bg-white p-8 rounded-xl border border-gray-100 hover:shadow-lg transition-shadow">
                <div class="w-12 h-12 bg-gradient-to-br from-teal-100 to-cyan-100 rounded-xl flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-heading font-semibold text-gray-800 mb-3">Client Management</h3>
                <p class="text-gray-600 mb-4">Organize all your client information in one place with detailed profiles, project history, and communication logs.</p>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• Contact information management</li>
                    <li>• Project history tracking</li>
                    <li>• Communication logs</li>
                    <li>• Client portal access</li>
                </ul>
            </div>

            <div class="bg-white p-8 rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 hover:border-emerald-200">
                <div class="w-12 h-12 bg-gradient-to-br from-amber-100 to-yellow-100 rounded-xl flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-amber-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-heading font-semibold text-gray-800 mb-3">AI-Powered Documents</h3>
                <p class="text-gray-600 mb-4">Generate smart contracts and proposals with AI assistance for better business decisions and professional documentation.</p>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• AI contract generation</li>
                    <li>• Smart proposal creation</li>
                    <li>• Document optimization</li>
                    <li>• Content suggestions</li>
                </ul>
            </div>

            <div class="bg-white p-8 rounded-xl border border-slate-200 hover:shadow-xl transition-all duration-300 hover:border-emerald-200">
                <div class="w-12 h-12 bg-gradient-to-br from-rose-100 to-pink-100 rounded-xl flex items-center justify-center mb-6">
                    <svg class="w-6 h-6 text-rose-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-heading font-semibold text-gray-800 mb-3">Financial Tracking</h3>
                <p class="text-gray-600 mb-4">Monitor your income, expenses, and tax obligations with comprehensive financial reports and analytics.</p>
                <ul class="text-sm text-gray-600 space-y-1">
                    <li>• Income and expense tracking</li>
                    <li>• Tax calculation and reports</li>
                    <li>• Financial analytics</li>
                    <li>• Revenue forecasting</li>
                </ul>
            </div>
        </div>
    </div>
</section>

<!-- CTA Section -->
<section class="bg-gradient-to-r from-emerald-600 via-teal-600 to-slate-700 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-heading font-bold text-white mb-4">
            Ready to Experience These Features?
        </h2>
        <p class="text-xl text-emerald-100 mb-8 max-w-2xl mx-auto">
            Start your free trial today and see how Freeligo can transform your freelance business.
        </p>
        <a href="{{ route('register') }}" class="bg-white text-emerald-600 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-all duration-300 transform hover:scale-105 inline-block shadow-lg">
            Start Free Trial
        </a>
    </div>
</section>
</x-frontend-layout>
