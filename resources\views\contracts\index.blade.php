<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Contracts</h1>
                <p class="text-gray-600 mt-1">Manage your client contracts and agreements</p>
            </div>
            <div class="flex items-center space-x-3">
                <x-ui.button href="{{ route('contracts.create') }}" variant="primary" icon="fas fa-plus">
                    Create New Contract
                </x-ui.button>
            </div>
        </div>
    </x-slot>

    <div class="py-12">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8">
            <!-- Search and Filter -->
            <x-ui.card class="mb-6">
                <form method="GET" action="{{ route('contracts.index') }}" class="flex flex-wrap gap-4">
                    <div class="flex-1 min-w-64">
                        <x-ui.input
                            name="search"
                            value="{{ request('search') }}"
                            placeholder="Search contracts..."
                            icon="fas fa-search" />
                    </div>
                    <div>
                        <x-ui.input type="select" name="status">
                            <option value="">All Status</option>
                            <option value="draft" {{ request('status') === 'draft' ? 'selected' : '' }}>Draft</option>
                            <option value="sent" {{ request('status') === 'sent' ? 'selected' : '' }}>Sent</option>
                            <option value="signed" {{ request('status') === 'signed' ? 'selected' : '' }}>Signed</option>
                        </x-ui.input>
                    </div>
                    <div>
                        <x-ui.input type="select" name="client_id">
                            <option value="">All Clients</option>
                            @foreach($clients as $client)
                                <option value="{{ $client->id }}" {{ request('client_id') == $client->id ? 'selected' : '' }}>
                                    {{ $client->name }}
                                </option>
                            @endforeach
                        </x-ui.input>
                    </div>
                    <div class="flex gap-2">
                        <x-ui.button type="submit" variant="primary" icon="fas fa-search">
                            Search
                        </x-ui.button>
                        <x-ui.button href="{{ route('contracts.index') }}" variant="secondary" icon="fas fa-times">
                            Clear
                        </x-ui.button>
                    </div>
                </form>
            </x-ui.card>

            <!-- Contracts Table -->
            <x-ui.card title="All Contracts" :subtitle="$contracts->total() . ' total contracts'">
                <x-slot name="headerActions">
                    <span class="text-sm text-gray-500">{{ $contracts->total() }} contracts</span>
                </x-slot>
                    @if($contracts->count() > 0)
                        <div class="overflow-x-auto">
                            <table class="min-w-full divide-y divide-gray-200">
                                <thead class="bg-gray-50">
                                    <tr>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Contract
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Client
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Template
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Created
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Status
                                        </th>
                                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Actions
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    @foreach($contracts as $contract)
                                        <tr class="hover:bg-gray-50">
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm font-medium text-gray-900">{{ $contract->title }}</div>
                                                @if($contract->signed_date)
                                                    <div class="text-sm text-gray-500">Signed: {{ $contract->signed_date->format('d/m/Y') }}</div>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                <div class="text-sm text-gray-900">{{ $contract->client->name }}</div>
                                                @if($contract->client->company_name)
                                                    <div class="text-sm text-gray-500">{{ $contract->client->company_name }}</div>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $contract->contractTemplate->name }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                                {{ $contract->created_at->format('d/m/Y') }}
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap">
                                                @if($contract->status === 'signed')
                                                    <x-ui.badge variant="success">{{ ucfirst($contract->status) }}</x-ui.badge>
                                                @elseif($contract->status === 'sent')
                                                    <x-ui.badge variant="primary">{{ ucfirst($contract->status) }}</x-ui.badge>
                                                @else
                                                    <x-ui.badge variant="default">{{ ucfirst($contract->status) }}</x-ui.badge>
                                                @endif
                                            </td>
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                                <div class="flex items-center space-x-2">
                                                    <x-ui.button href="{{ route('contracts.show', $contract) }}" variant="ghost" size="sm" icon="fas fa-eye">
                                                        View
                                                    </x-ui.button>
                                                    @if($contract->status !== 'signed')
                                                        <x-ui.button href="{{ route('contracts.edit', $contract) }}" variant="ghost" size="sm" icon="fas fa-edit">
                                                            Edit
                                                        </x-ui.button>
                                                    @endif
                                                    <x-ui.button href="{{ route('contracts.download', $contract) }}" variant="ghost" size="sm" icon="fas fa-download">
                                                        PDF
                                                    </x-ui.button>
                                                    @if($contract->status === 'sent')
                                                        <form method="POST" action="{{ route('contracts.mark-signed', $contract) }}" class="inline">
                                                            @csrf
                                                            @method('PATCH')
                                                            <x-ui.button type="submit" variant="ghost" size="sm" icon="fas fa-check"
                                                                    onclick="return confirm('Mark this contract as signed?')">
                                                                Mark Signed
                                                            </x-ui.button>
                                                        </form>
                                                    @endif
                                                    @if($contract->status === 'draft')
                                                        <form method="POST" action="{{ route('contracts.send', $contract) }}" class="inline">
                                                            @csrf
                                                            @method('PATCH')
                                                            <x-ui.button type="submit" variant="ghost" size="sm" icon="fas fa-paper-plane"
                                                                    onclick="return confirm('Send this contract to client?')">
                                                                Send
                                                            </x-ui.button>
                                                        </form>
                                                    @endif
                                                </div>
                                            </td>
                                        </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>

                        <!-- Pagination -->
                        <div class="mt-6">
                            {{ $contracts->links() }}
                        </div>
                    @else
                        <x-ui.empty-state
                            icon="fas fa-file-contract"
                            title="No contracts found"
                            description="Get started by creating your first contract to manage client agreements."
                            :action-href="route('contracts.create')"
                            action-text="Create New Contract" />
                    @endif
            </x-ui.card>
        </div>
    </div>
</x-app-layout>
