<?php

namespace App\Http\Controllers;

use App\Models\File;
use App\Services\FileService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class FileController extends Controller
{
    public function __construct(
        private FileService $fileService
    ) {}

    /**
     * Display a listing of files.
     */
    public function index(Request $request): View
    {
        $type = $request->get('type');
        $search = $request->get('search');

        $files = $this->fileService->getUserFiles($type, $search);
        $stats = $this->fileService->getUserFileStats(Auth::id());
        $storageUsage = $this->fileService->getStorageUsage(Auth::id());

        return view('files.index', compact('files', 'stats', 'storageUsage', 'type', 'search'));
    }

    /**
     * Store a newly uploaded file.
     */
    public function store(Request $request): JsonResponse|RedirectResponse
    {
        $request->validate([
            'file' => [
                'required',
                'file',
                'max:' . config('security.uploads.max_file_size'),
                'mimes:jpeg,jpg,png,gif,pdf,doc,docx'
            ],
            'description' => 'nullable|string|max:500',
            'is_public' => 'boolean',
        ]);

        // Check if user can upload files
        if (!$this->fileService->canUploadFile(Auth::id())) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'You have reached your file storage limit. Upgrade your plan for more storage.'
                ], 422);
            }

            return back()->with('error', 'You have reached your file storage limit. Upgrade your plan for more storage.');
        }

        try {
            $file = $this->fileService->uploadFile(
                $request->file('file'),
                $request->input('description'),
                $request->boolean('is_public', false)
            );

            if ($request->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'File uploaded successfully.',
                    'file' => [
                        'id' => $file->id,
                        'name' => $file->name,
                        'size' => $file->human_size,
                        'url' => $file->url,
                        'icon' => $file->icon,
                    ]
                ]);
            }

            return back()->with('success', 'File uploaded successfully.');
        } catch (\Exception $e) {
            if ($request->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage()
                ], 422);
            }

            return back()->with('error', $e->getMessage());
        }
    }

    /**
     * Display the specified file.
     */
    public function show(File $file): View
    {
        $this->authorize('view', $file);

        $file->markAsAccessed();

        return view('files.show', compact('file'));
    }

    /**
     * Update the specified file.
     */
    public function update(Request $request, File $file): RedirectResponse
    {
        $this->authorize('update', $file);

        $request->validate([
            'description' => 'nullable|string|max:500',
            'is_public' => 'boolean',
        ]);

        try {
            $this->fileService->updateFile($file, $request->only(['description', 'is_public']));
            return back()->with('success', 'File updated successfully.');
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update file. Please try again.');
        }
    }

    /**
     * Download the specified file.
     */
    public function download(File $file)
    {
        $this->authorize('view', $file);

        try {
            return $this->fileService->downloadFile($file);
        } catch (\Exception $e) {
            return back()->with('error', 'Failed to download file. Please try again.');
        }
    }

    /**
     * Remove the specified file.
     */
    public function destroy(File $file): JsonResponse|RedirectResponse
    {
        $this->authorize('delete', $file);

        try {
            $this->fileService->deleteFile($file);

            if (request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => 'File deleted successfully.'
                ]);
            }

            return back()->with('success', 'File deleted successfully.');
        } catch (\Exception $e) {
            if (request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to delete file.'
                ], 500);
            }

            return back()->with('error', 'Failed to delete file.');
        }
    }

    /**
     * Get files for file picker modal (AJAX).
     */
    public function picker(Request $request): JsonResponse
    {
        $type = $request->get('type');
        $search = $request->get('search');
        $page = $request->get('page', 1);

        $files = $this->fileService->getUserFiles($type, $search, 12);

        return response()->json([
            'files' => $files->items(),
            'pagination' => [
                'current_page' => $files->currentPage(),
                'last_page' => $files->lastPage(),
                'per_page' => $files->perPage(),
                'total' => $files->total(),
            ]
        ]);
    }
}
