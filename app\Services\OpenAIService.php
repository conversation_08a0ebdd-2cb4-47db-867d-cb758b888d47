<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class OpenAIService
{
    private ?string $apiKey;
    private ?string $organization;
    private ?string $model;
    private ?int $maxTokens;
    private ?float $temperature;
    private ?int $timeout;
    private string $baseUrl = 'https://api.openai.com/v1';

    public function __construct()
    {
        $this->apiKey = config('services.openai.api_key');
        $this->organization = config('services.openai.organization');
        $this->model = config('services.openai.model');
        $this->maxTokens = config('services.openai.max_tokens');
        $this->temperature = config('services.openai.temperature');
        $this->timeout = config('services.openai.timeout');

        // Note: API key validation is done at runtime when making requests
    }

    /**
     * Generate text completion using OpenAI GPT
     */
    public function generateCompletion(string $prompt, array $options = []): array
    {
        if (empty($this->apiKey)) {
            return ['success' => false, 'error' => 'OpenAI API key is not configured'];
        }

        try {
            $headers = [
                'Authorization' => 'Bearer ' . $this->apiKey,
                'Content-Type' => 'application/json',
            ];

            if ($this->organization) {
                $headers['OpenAI-Organization'] = $this->organization;
            }

            $data = [
                'model' => $options['model'] ?? $this->model,
                'messages' => [
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => $options['max_tokens'] ?? $this->maxTokens,
                'temperature' => $options['temperature'] ?? $this->temperature,
            ];

            $response = Http::withHeaders($headers)
                ->timeout($this->timeout)
                ->post($this->baseUrl . '/chat/completions', $data);

            if ($response->successful()) {
                $result = $response->json();
                return [
                    'success' => true,
                    'content' => $result['choices'][0]['message']['content'] ?? '',
                    'usage' => $result['usage'] ?? [],
                ];
            }

            return [
                'success' => false,
                'error' => 'OpenAI API request failed: ' . $response->body(),
            ];

        } catch (Exception $e) {
            Log::error('OpenAI API Error', [
                'message' => $e->getMessage(),
                'prompt' => substr($prompt, 0, 100) . '...',
            ]);

            return [
                'success' => false,
                'error' => 'Failed to generate AI content: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Generate smart invoice description based on client history and project details
     */
    public function generateInvoiceDescription(array $context): array
    {
        $prompt = $this->buildInvoiceDescriptionPrompt($context);
        
        return $this->generateCompletion($prompt, [
            'max_tokens' => 200,
            'temperature' => 0.7,
        ]);
    }

    /**
     * Generate follow-up message suggestions
     */
    public function generateFollowUpMessage(array $context): array
    {
        $prompt = $this->buildFollowUpPrompt($context);
        
        return $this->generateCompletion($prompt, [
            'max_tokens' => 300,
            'temperature' => 0.8,
        ]);
    }

    /**
     * Generate contract recommendations
     */
    public function generateContractRecommendations(array $context): array
    {
        $prompt = $this->buildContractPrompt($context);
        
        return $this->generateCompletion($prompt, [
            'max_tokens' => 500,
            'temperature' => 0.6,
        ]);
    }

    /**
     * Analyze business data and provide insights
     */
    public function generateBusinessInsights(array $data): array
    {
        $prompt = $this->buildBusinessInsightsPrompt($data);
        
        return $this->generateCompletion($prompt, [
            'max_tokens' => 600,
            'temperature' => 0.5,
        ]);
    }

    /**
     * Build prompt for invoice description generation
     */
    private function buildInvoiceDescriptionPrompt(array $context): string
    {
        $clientName = $context['client_name'] ?? 'Client';
        $projectType = $context['project_type'] ?? 'project';
        $previousWork = $context['previous_work'] ?? [];
        $amount = $context['amount'] ?? 0;

        $prompt = "Generate a professional invoice description for a freelance {$projectType} project for {$clientName}. ";
        
        if (!empty($previousWork)) {
            $prompt .= "Previous work includes: " . implode(', ', $previousWork) . ". ";
        }
        
        $prompt .= "The invoice amount is {$amount}. ";
        $prompt .= "Create a clear, professional description that explains the work completed. ";
        $prompt .= "Keep it concise but detailed enough for business records. ";
        $prompt .= "Format as a single paragraph without bullet points.";

        return $prompt;
    }

    /**
     * Build prompt for follow-up message generation
     */
    private function buildFollowUpPrompt(array $context): string
    {
        $clientName = $context['client_name'] ?? 'Client';
        $invoiceNumber = $context['invoice_number'] ?? '';
        $amount = $context['amount'] ?? 0;
        $daysOverdue = $context['days_overdue'] ?? 0;
        $followUpType = $context['type'] ?? 'reminder';
        $clientRelationship = $context['relationship'] ?? 'professional';

        $prompt = "Generate a {$followUpType} message for {$clientName} regarding invoice {$invoiceNumber} ";
        $prompt .= "for amount {$amount}. ";
        
        if ($daysOverdue > 0) {
            $prompt .= "The payment is {$daysOverdue} days overdue. ";
        }
        
        $prompt .= "The client relationship is {$clientRelationship}. ";
        $prompt .= "Create a professional but " . ($followUpType === 'friendly' ? 'friendly' : 'firm') . " tone. ";
        $prompt .= "Keep it concise and actionable. Include a clear call to action.";

        return $prompt;
    }

    /**
     * Build prompt for contract recommendations
     */
    private function buildContractPrompt(array $context): string
    {
        $projectType = $context['project_type'] ?? 'general';
        $clientType = $context['client_type'] ?? 'business';
        $projectDuration = $context['duration'] ?? 'short-term';
        $budget = $context['budget'] ?? 'medium';

        $prompt = "Recommend contract clauses and terms for a {$projectType} freelance project. ";
        $prompt .= "Client type: {$clientType}, Duration: {$projectDuration}, Budget: {$budget}. ";
        $prompt .= "Provide specific recommendations for: payment terms, deliverables, ";
        $prompt .= "intellectual property rights, cancellation policy, and any special considerations. ";
        $prompt .= "Format as clear, actionable recommendations.";

        return $prompt;
    }

    /**
     * Build prompt for business insights
     */
    private function buildBusinessInsightsPrompt(array $data): string
    {
        $totalRevenue = $data['total_revenue'] ?? 0;
        $clientCount = $data['client_count'] ?? 0;
        $avgProjectValue = $data['avg_project_value'] ?? 0;
        $topClients = $data['top_clients'] ?? [];
        $revenueGrowth = $data['revenue_growth'] ?? 0;

        $prompt = "Analyze this freelance business data and provide actionable insights: ";
        $prompt .= "Total Revenue: {$totalRevenue}, Client Count: {$clientCount}, ";
        $prompt .= "Average Project Value: {$avgProjectValue}, Revenue Growth: {$revenueGrowth}%. ";
        
        if (!empty($topClients)) {
            $prompt .= "Top clients: " . implode(', ', $topClients) . ". ";
        }
        
        $prompt .= "Provide specific recommendations for: revenue optimization, ";
        $prompt .= "client relationship management, pricing strategy, and business growth. ";
        $prompt .= "Focus on actionable insights that can be implemented immediately.";

        return $prompt;
    }

    /**
     * Check if OpenAI service is properly configured
     */
    public function isConfigured(): bool
    {
        return !empty($this->apiKey);
    }

    /**
     * Test the OpenAI connection
     */
    public function testConnection(): array
    {
        try {
            $result = $this->generateCompletion('Test connection. Respond with "OK" if you can read this.');
            return [
                'success' => $result['success'],
                'message' => $result['success'] ? 'OpenAI connection successful' : $result['error'],
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed: ' . $e->getMessage(),
            ];
        }
    }
}
