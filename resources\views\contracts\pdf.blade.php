<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $contract->title }}</title>
    <style>
        body {
            font-family: 'Inter', 'Open Sans', 'Mulish', Arial, sans-serif;
            margin: 0;
            padding: 30px;
            color: #333;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .contract-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            text-transform: uppercase;
        }
        .contract-subtitle {
            font-size: 16px;
            color: #666;
        }
        .contract-details {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        .contract-left, .contract-right {
            display: table-cell;
            width: 50%;
            vertical-align: top;
        }
        .contract-right {
            text-align: right;
        }
        .section-title {
            font-weight: bold;
            font-size: 14px;
            margin-bottom: 10px;
            color: #333;
        }
        .detail-row {
            margin-bottom: 5px;
            font-size: 12px;
        }
        .detail-label {
            font-weight: bold;
            display: inline-block;
            width: 100px;
        }
        .contract-content {
            margin: 30px 0;
            text-align: justify;
            font-size: 12px;
            line-height: 1.8;
        }
        .contract-content h1,
        .contract-content h2,
        .contract-content h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 10px;
        }
        .contract-content h1 {
            font-size: 16px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
        }
        .contract-content h2 {
            font-size: 14px;
        }
        .contract-content h3 {
            font-size: 13px;
        }
        .contract-content p {
            margin-bottom: 10px;
        }
        .contract-content ul,
        .contract-content ol {
            margin-left: 20px;
            margin-bottom: 10px;
        }
        .contract-content li {
            margin-bottom: 5px;
        }
        .signature-section {
            margin-top: 50px;
            page-break-inside: avoid;
        }
        .signature-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 30px;
        }
        .signature-table td {
            padding: 20px;
            border: 1px solid #ddd;
            vertical-align: top;
            width: 50%;
        }
        .signature-label {
            font-weight: bold;
            margin-bottom: 10px;
        }
        .signature-line {
            border-bottom: 1px solid #333;
            height: 40px;
            margin-bottom: 10px;
        }
        .signature-info {
            font-size: 11px;
            color: #666;
        }
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 10px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-signed {
            background-color: #d4edda;
            color: #155724;
        }
        .status-sent {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        .status-draft {
            background-color: #f8f9fa;
            color: #495057;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="contract-title">{{ $contract->title }}</div>
        <div class="contract-subtitle">Contract Agreement</div>
    </div>

    <!-- Contract Details -->
    <div class="contract-details">
        <div class="contract-left">
            <div class="section-title">Client Information:</div>
            <div class="detail-row"><strong>{{ $contract->client->name }}</strong></div>
            @if($contract->client->company_name)
                <div class="detail-row">{{ $contract->client->company_name }}</div>
            @endif
            <div class="detail-row">{{ $contract->client->email }}</div>
            @if($contract->client->phone)
                <div class="detail-row">{{ $contract->client->phone }}</div>
            @endif
            @if($contract->client->address)
                <div class="detail-row">{{ $contract->client->address }}</div>
            @endif
            @if($contract->client->gst_number)
                <div class="detail-row"><span class="detail-label">GST:</span> {{ $contract->client->gst_number }}</div>
            @endif
            @if($contract->client->pan_number)
                <div class="detail-row"><span class="detail-label">PAN:</span> {{ $contract->client->pan_number }}</div>
            @endif
        </div>

        <div class="contract-right">
            <div class="detail-row">
                <span class="detail-label">Contract ID:</span> 
                <strong>{{ $contract->id }}</strong>
            </div>
            <div class="detail-row">
                <span class="detail-label">Template:</span> 
                {{ $contract->contractTemplate->name }}
            </div>
            <div class="detail-row">
                <span class="detail-label">Created:</span> 
                {{ $contract->created_at->format('d/m/Y') }}
            </div>
            @if($contract->sent_date)
                <div class="detail-row">
                    <span class="detail-label">Sent:</span> 
                    {{ $contract->sent_date->format('d/m/Y') }}
                </div>
            @endif
            @if($contract->signed_date)
                <div class="detail-row">
                    <span class="detail-label">Signed:</span> 
                    {{ $contract->signed_date->format('d/m/Y') }}
                </div>
            @endif
            <div class="detail-row">
                <span class="detail-label">Status:</span>
                <span class="status-badge status-{{ $contract->status }}">
                    {{ ucfirst($contract->status) }}
                </span>
            </div>
        </div>
    </div>

    <!-- Contract Variables (if any) -->
    @if($contract->variables && count($contract->variables) > 0)
        <div style="margin-bottom: 30px;">
            <div class="section-title">Contract Variables:</div>
            <div style="display: table; width: 100%;">
                @php $count = 0; @endphp
                @foreach($contract->variables as $key => $value)
                    @if($count % 2 == 0)
                        <div style="display: table-row;">
                    @endif
                    <div style="display: table-cell; width: 50%; padding: 5px;">
                        <span class="detail-label">{{ ucwords(str_replace('_', ' ', $key)) }}:</span> {{ $value }}
                    </div>
                    @php $count++; @endphp
                    @if($count % 2 == 0)
                        </div>
                    @endif
                @endforeach
                @if($count % 2 != 0)
                    </div>
                @endif
            </div>
        </div>
    @endif

    <!-- Contract Content -->
    <div class="contract-content">
        {!! nl2br(e($contract->content)) !!}
    </div>

    <!-- Signature Section -->
    <div class="signature-section">
        <table class="signature-table">
            <tr>
                <td>
                    <div class="signature-label">Service Provider</div>
                    <div class="signature-line"></div>
                    <div class="signature-info">
                        <strong>{{ config('app.name', 'Freelancer Legal Kit') }}</strong><br>
                        Date: _______________
                    </div>
                </td>
                <td>
                    <div class="signature-label">Client</div>
                    <div class="signature-line"></div>
                    <div class="signature-info">
                        <strong>{{ $contract->client->name }}</strong><br>
                        @if($contract->client->company_name)
                            {{ $contract->client->company_name }}<br>
                        @endif
                        Date: _______________
                    </div>
                </td>
            </tr>
        </table>
    </div>



    <!-- Footer -->
    <div class="footer">
        <p>This contract was generated by {{ config('app.name', 'Freeligo') }} on {{ now()->format('d/m/Y H:i') }}</p>
        @if($contract->status === 'signed')
            <p><strong>This contract has been digitally signed and is legally binding.</strong></p>
        @endif
        <p style="font-size: 8px; color: #999;">Run Your Freelance Business Like a Pro</p>
    </div>
</body>
</html>
