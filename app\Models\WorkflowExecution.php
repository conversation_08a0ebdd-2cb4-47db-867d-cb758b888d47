<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class WorkflowExecution extends Model
{
    use HasFactory;

    protected $fillable = [
        'workflow_id',
        'trigger_data',
        'execution_data',
        'status',
        'started_at',
        'completed_at',
        'error_message',
        'actions_executed',
        'actions_failed',
        'execution_time_seconds',
        'retry_count',
        'priority',
        'scheduled_at',
        'performance_data',
        'ai_analysis',
        'resource_usage',
        'output_data',
    ];

    protected function casts(): array
    {
        return [
            'trigger_data' => 'array',
            'execution_data' => 'array',
            'started_at' => 'datetime',
            'completed_at' => 'datetime',
            'actions_executed' => 'integer',
            'actions_failed' => 'integer',
            'execution_time_seconds' => 'decimal:3',
            'retry_count' => 'integer',
            'priority' => 'integer',
            'scheduled_at' => 'datetime',
            'performance_data' => 'array',
            'ai_analysis' => 'array',
            'resource_usage' => 'array',
            'output_data' => 'array',
        ];
    }

    /**
     * Get the workflow that owns the execution.
     */
    public function workflow(): BelongsTo
    {
        return $this->belongsTo(Workflow::class);
    }

    /**
     * Get the workflow logs for this execution.
     */
    public function logs(): HasMany
    {
        return $this->hasMany(WorkflowLog::class);
    }

    /**
     * Mark execution as completed.
     */
    public function markCompleted(array $executionData = [], array $outputData = []): void
    {
        $executionTime = $this->started_at ? now()->diffInSeconds($this->started_at, true) : 0;

        $this->update([
            'status' => 'completed',
            'completed_at' => now(),
            'execution_data' => $executionData,
            'output_data' => $outputData,
            'execution_time_seconds' => $executionTime,
        ]);

        // Record success in workflow
        $this->workflow->recordSuccess();

        // Log completion
        WorkflowLog::info($this->id, 'execution_completed', 'Workflow execution completed successfully', [
            'execution_time' => $executionTime,
            'actions_executed' => $this->actions_executed,
            'output_data_size' => count($outputData),
        ]);
    }

    /**
     * Mark execution as failed.
     */
    public function markFailed(string $errorMessage, array $errorContext = []): void
    {
        $executionTime = $this->started_at ? now()->diffInSeconds($this->started_at, true) : 0;

        $this->update([
            'status' => 'failed',
            'completed_at' => now(),
            'error_message' => $errorMessage,
            'execution_time_seconds' => $executionTime,
        ]);

        // Record failure in workflow
        $this->workflow->recordFailure($errorMessage);

        // Log failure
        WorkflowLog::error($this->id, 'execution_failed', $errorMessage, array_merge($errorContext, [
            'execution_time' => $executionTime,
            'actions_executed' => $this->actions_executed,
            'actions_failed' => $this->actions_failed,
        ]));
    }

    /**
     * Start execution tracking.
     */
    public function start(): void
    {
        $this->update([
            'status' => 'running',
            'started_at' => now(),
        ]);

        WorkflowLog::info($this->id, 'execution_started', 'Workflow execution started', [
            'trigger_data' => $this->trigger_data,
            'priority' => $this->priority,
        ]);
    }

    /**
     * Increment retry count.
     */
    public function incrementRetry(): void
    {
        $this->increment('retry_count');

        WorkflowLog::warning($this->id, 'execution_retry', "Execution retry attempt #{$this->retry_count}", [
            'retry_count' => $this->retry_count,
            'error_message' => $this->error_message,
        ]);
    }

    /**
     * Record action execution.
     */
    public function recordActionExecution(bool $success = true): void
    {
        if ($success) {
            $this->increment('actions_executed');
        } else {
            $this->increment('actions_failed');
        }
    }

    /**
     * Update performance data.
     */
    public function updatePerformanceData(array $data): void
    {
        $currentData = $this->performance_data ?? [];
        $this->update(['performance_data' => array_merge($currentData, $data)]);
    }

    /**
     * Update AI analysis.
     */
    public function updateAIAnalysis(array $analysis): void
    {
        $this->update(['ai_analysis' => $analysis]);
    }

    /**
     * Get execution duration in seconds.
     */
    public function getDuration(): ?float
    {
        if (!$this->started_at) {
            return null;
        }

        $endTime = $this->completed_at ?? now();
        return $this->started_at->diffInSeconds($endTime, true);
    }

    /**
     * Get execution status with details.
     */
    public function getStatusDetails(): array
    {
        return [
            'status' => $this->status,
            'started_at' => $this->started_at?->toISOString(),
            'completed_at' => $this->completed_at?->toISOString(),
            'duration' => $this->getDuration(),
            'actions_executed' => $this->actions_executed,
            'actions_failed' => $this->actions_failed,
            'retry_count' => $this->retry_count,
            'success_rate' => $this->actions_executed > 0 ?
                (($this->actions_executed - $this->actions_failed) / $this->actions_executed) * 100 : 0,
        ];
    }

    /**
     * Check if execution is in progress.
     */
    public function isRunning(): bool
    {
        return $this->status === 'running';
    }

    /**
     * Check if execution is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === 'completed';
    }

    /**
     * Check if execution failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if execution can be retried.
     */
    public function canRetry(): bool
    {
        return $this->isFailed() && $this->retry_count < 3;
    }

    /**
     * Scope for successful executions.
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', 'completed');
    }

    /**
     * Scope for failed executions.
     */
    public function scopeFailed($query)
    {
        return $query->where('status', 'failed');
    }

    /**
     * Scope for running executions.
     */
    public function scopeRunning($query)
    {
        return $query->where('status', 'running');
    }

    /**
     * Scope for pending executions.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope for high priority executions.
     */
    public function scopeHighPriority($query)
    {
        return $query->where('priority', '>=', 8);
    }

    /**
     * Scope for recent executions.
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('started_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for slow executions.
     */
    public function scopeSlow($query, float $thresholdSeconds = 30.0)
    {
        return $query->where('execution_time_seconds', '>', $thresholdSeconds);
    }

    /**
     * Scope for executions with retries.
     */
    public function scopeWithRetries($query)
    {
        return $query->where('retry_count', '>', 0);
    }

    /**
     * Scope for scheduled executions.
     */
    public function scopeScheduled($query)
    {
        return $query->whereNotNull('scheduled_at');
    }

    /**
     * Scope for executions ready to run.
     */
    public function scopeReadyToRun($query)
    {
        return $query->where('status', 'pending')
                    ->where(function ($q) {
                        $q->whereNull('scheduled_at')
                          ->orWhere('scheduled_at', '<=', now());
                    });
    }
}
