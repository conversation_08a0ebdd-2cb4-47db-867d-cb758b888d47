@props([
    'id' => 'filePickerBtn_' . uniqid(),
    'label' => 'Select File',
    'multiple' => false,
    'accept' => null,
    'onSelect' => null,
    'class' => 'bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors duration-200'
])

<button 
    onclick="openFilePicker('{{ $id }}_modal', { onSelect: {{ $onSelect ?? 'null' }} })"
    class="{{ $class }}"
    {{ $attributes }}>
    <i class="fas fa-file mr-2"></i>
    {{ $label }}
</button>

<!-- Include the file picker modal -->
<x-file-picker 
    :id="$id . '_modal'"
    :title="$multiple ? 'Select Files' : 'Select File'"
    :multiple="$multiple"
    :accept="$accept"
    :onSelect="$onSelect" />

@once
@push('scripts')
<script>
// Global file picker helper functions
window.filePickerHelpers = {
    // Helper to set file input value
    setFileInput: function(inputId, file) {
        const input = document.getElementById(inputId);
        if (input) {
            // Create a display element to show selected file
            let display = document.getElementById(inputId + '_display');
            if (!display) {
                display = document.createElement('div');
                display.id = inputId + '_display';
                display.className = 'mt-2 p-2 bg-gray-50 rounded border text-sm';
                input.parentNode.insertBefore(display, input.nextSibling);
            }
            
            display.innerHTML = `
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <i class="${file.icon || 'fas fa-file'} mr-2"></i>
                        <span>${file.name}</span>
                        <span class="text-gray-500 ml-2">(${file.human_size || file.size})</span>
                    </div>
                    <button type="button" onclick="this.parentElement.parentElement.remove(); document.getElementById('${inputId}').value = '';" class="text-red-600 hover:text-red-800">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            `;
            
            // Set the actual input value (you might want to store file ID)
            input.value = file.id;
            input.setAttribute('data-file-name', file.name);
            input.setAttribute('data-file-url', file.url);
        }
    },

    // Helper to set multiple file inputs
    setMultipleFileInputs: function(inputId, files) {
        const input = document.getElementById(inputId);
        if (input && files.length > 0) {
            let display = document.getElementById(inputId + '_display');
            if (!display) {
                display = document.createElement('div');
                display.id = inputId + '_display';
                display.className = 'mt-2 space-y-2';
                input.parentNode.insertBefore(display, input.nextSibling);
            }
            
            display.innerHTML = files.map(file => `
                <div class="p-2 bg-gray-50 rounded border text-sm">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i class="${file.icon || 'fas fa-file'} mr-2"></i>
                            <span>${file.name}</span>
                            <span class="text-gray-500 ml-2">(${file.human_size || file.size})</span>
                        </div>
                        <button type="button" onclick="removeFileFromMultiple('${inputId}', ${file.id})" class="text-red-600 hover:text-red-800">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            `).join('');
            
            // Set the input value as comma-separated IDs
            input.value = files.map(f => f.id).join(',');
        }
    },

    // Helper to remove file from multiple selection
    removeFileFromMultiple: function(inputId, fileId) {
        const input = document.getElementById(inputId);
        if (input) {
            const currentIds = input.value.split(',').filter(id => id != fileId);
            input.value = currentIds.join(',');
            
            // Remove the display element
            event.target.closest('.p-2').remove();
        }
    }
};

// Make helper functions globally available
window.removeFileFromMultiple = window.filePickerHelpers.removeFileFromMultiple;
</script>
@endpush
@endonce
