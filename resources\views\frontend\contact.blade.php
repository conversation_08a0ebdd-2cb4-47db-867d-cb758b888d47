<x-frontend-layout>
    <x-slot name="title">Contact Us - Freeligo</x-slot>
    <x-slot name="description">Get in touch with the Freeligo team. We're here to help you succeed with your freelance business management needs.</x-slot>
<!-- Hero Section -->
<section class="bg-gradient-to-br from-slate-50 via-emerald-50 to-teal-50 py-20">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-5xl font-heading font-bold text-gray-800 mb-6">
            Get in
            <span class="text-transparent bg-clip-text bg-gradient-to-r from-emerald-600 to-teal-600">Touch</span>
        </h1>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
            Have questions about Freeligo? Need help getting started? Our team is here to support your freelance business success.
        </p>
    </div>
</section>

<!-- Contact Section -->
<section class="py-20 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16">
            <!-- Contact Form -->
            <div>
                <h2 class="text-3xl font-heading font-bold text-gray-900 mb-6">Send us a message</h2>
                <p class="text-gray-600 mb-8">
                    Fill out the form below and we'll get back to you within 24 hours. For urgent matters, 
                    please use our live chat or call us directly.
                </p>

                @if(session('success'))
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <div class="flex">
                            <svg class="w-5 h-5 text-green-400 mr-3 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <p class="text-green-800">{{ session('success') }}</p>
                        </div>
                    </div>
                @endif

                <form action="{{ route('contact.submit') }}" method="POST" class="space-y-6"
                      x-data="{
                          name: '{{ old('name') }}',
                          email: '{{ old('email') }}',
                          subject: '{{ old('subject') }}',
                          message: '{{ old('message') }}',
                          nameError: '',
                          emailError: '',
                          subjectError: '',
                          messageError: '',
                          validateName() {
                              if (!this.name.trim()) {
                                  this.nameError = 'Please enter your full name.';
                              } else if (this.name.length < 2) {
                                  this.nameError = 'Name must be at least 2 characters long.';
                              } else if (!/^[a-zA-Z\s\-\.\']+$/.test(this.name)) {
                                  this.nameError = 'Name can only contain letters, spaces, hyphens, dots, and apostrophes.';
                              } else {
                                  this.nameError = '';
                              }
                          },
                          validateEmail() {
                              if (!this.email.trim()) {
                                  this.emailError = 'Please enter your email address.';
                              } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(this.email)) {
                                  this.emailError = 'Please enter a valid email address.';
                              } else {
                                  this.emailError = '';
                              }
                          },
                          validateSubject() {
                              if (!this.subject.trim()) {
                                  this.subjectError = 'Please enter a subject for your message.';
                              } else if (this.subject.length < 5) {
                                  this.subjectError = 'Subject must be at least 5 characters long.';
                              } else {
                                  this.subjectError = '';
                              }
                          },
                          validateMessage() {
                              if (!this.message.trim()) {
                                  this.messageError = 'Please enter your message.';
                              } else if (this.message.length < 10) {
                                  this.messageError = 'Message must be at least 10 characters long.';
                              } else if (this.message.length > 2000) {
                                  this.messageError = 'Message cannot exceed 2000 characters.';
                              } else {
                                  this.messageError = '';
                              }
                          }
                      }">
                    @csrf
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 mb-2">Name *</label>
                            <input type="text" id="name" name="name" x-model="name" @blur="validateName()" required
                                class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-colors"
                                :class="nameError || @json($errors->has('name')) ? 'border-red-500' : 'border-gray-300'">
                            <p x-show="nameError" x-text="nameError" class="text-red-500 text-sm mt-1"></p>
                            @error('name')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                        <div>
                            <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                            <input type="email" id="email" name="email" x-model="email" @blur="validateEmail()" required
                                class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-colors"
                                :class="emailError || @json($errors->has('email')) ? 'border-red-500' : 'border-gray-300'">
                            <p x-show="emailError" x-text="emailError" class="text-red-500 text-sm mt-1"></p>
                            @error('email')
                                <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                    
                    <div>
                        <label for="subject" class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                        <input type="text" id="subject" name="subject" x-model="subject" @blur="validateSubject()" required
                            class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-colors"
                            :class="subjectError || @json($errors->has('subject')) ? 'border-red-500' : 'border-gray-300'">
                        <p x-show="subjectError" x-text="subjectError" class="text-red-500 text-sm mt-1"></p>
                        @error('subject')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>

                    <div>
                        <label for="message" class="block text-sm font-medium text-gray-700 mb-2">Message *</label>
                        <textarea id="message" name="message" rows="6" x-model="message" @blur="validateMessage()" required
                            class="w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-transparent transition-colors resize-none"
                            :class="messageError || @json($errors->has('message')) ? 'border-red-500' : 'border-gray-300'"></textarea>
                        <div class="flex justify-between items-center mt-1">
                            <p x-show="messageError" x-text="messageError" class="text-red-500 text-sm"></p>
                            <p class="text-gray-500 text-sm" x-text="`${message.length}/2000 characters`"></p>
                        </div>
                        @error('message')
                            <p class="text-red-500 text-sm mt-1">{{ $message }}</p>
                        @enderror
                    </div>
                    
                    <button type="submit" class="w-full bg-gradient-to-r from-emerald-600 to-teal-600 text-white py-4 px-6 rounded-xl font-semibold hover:from-emerald-700 hover:to-teal-700 transition-all duration-300 transform hover:scale-105 shadow-lg touch-manipulation text-base">
                        Send Message
                    </button>
                </form>
            </div>

            <!-- Contact Information -->
            <div>
                <h2 class="text-3xl font-heading font-bold text-gray-900 mb-6">Other ways to reach us</h2>
                
                <div class="space-y-8">
                    <!-- Email -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-gradient-to-br from-emerald-100 to-teal-100 rounded-xl flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-emerald-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-1">Email Support</h3>
                            <p class="text-gray-600 mb-2">Get help with your account or technical issues</p>
                            <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-medium"><EMAIL></a>
                        </div>
                    </div>

                    <!-- Sales -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-1">Sales Inquiries</h3>
                            <p class="text-gray-600 mb-2">Questions about pricing or enterprise plans</p>
                            <a href="mailto:<EMAIL>" class="text-blue-600 hover:text-blue-700 font-medium"><EMAIL></a>
                        </div>
                    </div>

                    <!-- Live Chat -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-1">Live Chat</h3>
                            <p class="text-gray-600 mb-2">Available Monday-Friday, 9 AM - 6 PM EST</p>
                            <button class="text-blue-600 hover:text-blue-700 font-medium">Start Chat</button>
                        </div>
                    </div>

                    <!-- Knowledge Base -->
                    <div class="flex items-start">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-1">Knowledge Base</h3>
                            <p class="text-gray-600 mb-2">Find answers to common questions and tutorials</p>
                            <a href="#" class="text-blue-600 hover:text-blue-700 font-medium">Browse Articles</a>
                        </div>
                    </div>
                </div>

                <!-- Response Time -->
                <div class="mt-12 p-6 bg-gray-50 rounded-lg">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Response Times</h3>
                    <ul class="space-y-2 text-gray-600">
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Live Chat: Immediate</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Email: Within 24 hours</span>
                        </li>
                        <li class="flex items-center">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span>Sales: Within 4 hours</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- FAQ Section -->
<section class="py-20 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-heading font-bold text-gray-900 mb-4">
                Frequently Asked Questions
            </h2>
            <p class="text-xl text-gray-600">
                Quick answers to common questions about Freeligo.
            </p>
        </div>

        <div x-data="{ openFaq: null }" class="space-y-4">
            <div class="bg-white rounded-lg border border-gray-200">
                <button @click="openFaq = openFaq === 1 ? null : 1" class="w-full px-6 py-4 text-left flex items-center justify-between">
                    <span class="font-semibold text-gray-900">How do I get started with Freeligo?</span>
                    <svg class="w-5 h-5 text-gray-500 transform transition-transform" :class="openFaq === 1 ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 1" x-transition class="px-6 pb-4">
                    <p class="text-gray-600">Simply sign up for a free account and start your 14-day trial. No credit card required. You can import your existing client data and start creating invoices immediately.</p>
                </div>
            </div>

            <div class="bg-white rounded-lg border border-gray-200">
                <button @click="openFaq = openFaq === 2 ? null : 2" class="w-full px-6 py-4 text-left flex items-center justify-between">
                    <span class="font-semibold text-gray-900">Can I import my existing data?</span>
                    <svg class="w-5 h-5 text-gray-500 transform transition-transform" :class="openFaq === 2 ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 2" x-transition class="px-6 pb-4">
                    <p class="text-gray-600">Yes! Freeligo supports importing client data, invoices, and other business information from CSV files and popular freelance tools.</p>
                </div>
            </div>

            <div class="bg-white rounded-lg border border-gray-200">
                <button @click="openFaq = openFaq === 3 ? null : 3" class="w-full px-6 py-4 text-left flex items-center justify-between">
                    <span class="font-semibold text-gray-900">Is my data secure?</span>
                    <svg class="w-5 h-5 text-gray-500 transform transition-transform" :class="openFaq === 3 ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 3" x-transition class="px-6 pb-4">
                    <p class="text-gray-600">Absolutely. We use enterprise-grade security with SSL encryption, regular backups, and comply with industry security standards to protect your business data.</p>
                </div>
            </div>

            <div class="bg-white rounded-lg border border-gray-200">
                <button @click="openFaq = openFaq === 4 ? null : 4" class="w-full px-6 py-4 text-left flex items-center justify-between">
                    <span class="font-semibold text-gray-900">Do you offer training or onboarding?</span>
                    <svg class="w-5 h-5 text-gray-500 transform transition-transform" :class="openFaq === 4 ? 'rotate-180' : ''" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                </button>
                <div x-show="openFaq === 4" x-transition class="px-6 pb-4">
                    <p class="text-gray-600">Yes! We provide comprehensive onboarding for all new users, including video tutorials, documentation, and personalized setup assistance for Enterprise customers.</p>
                </div>
            </div>
        </div>
    </div>
</section>
</x-frontend-layout>
