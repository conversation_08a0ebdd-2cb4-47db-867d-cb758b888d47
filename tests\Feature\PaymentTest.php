<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Plan;
use App\Models\Payment;
use App\Models\UserSubscription;
use App\Services\PayPalService;
use App\Services\PaymentSecurityService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Mockery;

class PaymentTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $plan;
    protected $payment;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create();
        $this->plan = Plan::factory()->create([
            'name' => 'Pro Plan',
            'price' => 199.00,
            'currency' => 'USD',
            'billing_cycle' => 'monthly',
        ]);
        
        $this->payment = Payment::factory()->create([
            'user_id' => $this->user->id,
            'amount' => $this->plan->price,
            'currency' => $this->plan->currency,
            'status' => 'pending',
        ]);
    }

    public function test_user_can_access_payment_gateway_page()
    {
        $this->actingAs($this->user)
            ->get(route('payment.gateway', $this->payment))
            ->assertStatus(200)
            ->assertViewIs('payment.gateway')
            ->assertViewHas('payment', $this->payment);
    }

    public function test_user_cannot_access_other_users_payment()
    {
        $otherUser = User::factory()->create();
        $otherPayment = Payment::factory()->create(['user_id' => $otherUser->id]);

        $this->actingAs($this->user)
            ->get(route('payment.gateway', $otherPayment))
            ->assertStatus(403);
    }

    public function test_paypal_payment_initiation()
    {
        $mockPayPalService = Mockery::mock(PayPalService::class);
        $mockPayPalService->shouldReceive('createSubscription')
            ->once()
            ->with($this->payment)
            ->andReturn('https://paypal.com/approve/subscription');

        $this->app->instance(PayPalService::class, $mockPayPalService);

        $this->actingAs($this->user)
            ->post(route('payment.paypal', $this->payment))
            ->assertRedirect('https://paypal.com/approve/subscription');

        $this->assertDatabaseHas('payments', [
            'id' => $this->payment->id,
            'gateway' => 'paypal',
        ]);
    }

    public function test_paypal_payment_security_validation()
    {
        // Create a payment that doesn't belong to the user
        $otherUser = User::factory()->create();
        $invalidPayment = Payment::factory()->create(['user_id' => $otherUser->id]);

        $this->actingAs($this->user)
            ->post(route('payment.paypal', $invalidPayment))
            ->assertStatus(403);
    }

    public function test_payment_rate_limiting()
    {
        $mockSecurityService = Mockery::mock(PaymentSecurityService::class);
        $mockSecurityService->shouldReceive('validatePaymentRequest')
            ->andReturn(['Too many payment attempts']);
        $mockSecurityService->shouldReceive('logSecurityEvent');

        $this->app->instance(PaymentSecurityService::class, $mockSecurityService);

        $this->actingAs($this->user)
            ->post(route('payment.paypal', $this->payment))
            ->assertStatus(403);
    }

    public function test_paypal_webhook_signature_verification()
    {
        $payload = json_encode([
            'event_type' => 'BILLING.SUBSCRIPTION.ACTIVATED',
            'resource' => ['id' => 'subscription_123']
        ]);

        $headers = [
            'paypal-auth-algo' => ['SHA256withRSA'],
            'paypal-transmission-id' => ['transmission_123'],
            'paypal-cert-id' => ['cert_123'],
            'paypal-transmission-sig' => ['signature_123'],
            'paypal-transmission-time' => [now()->toISOString()],
        ];

        $mockPayPalService = Mockery::mock(PayPalService::class);
        $mockPayPalService->shouldReceive('handleWebhook')
            ->once()
            ->with($payload, $headers);

        $this->app->instance(PayPalService::class, $mockPayPalService);

        $response = $this->postJson(route('payment.paypal.webhook'), [], $headers)
            ->assertStatus(200)
            ->assertJson(['status' => 'success']);
    }

    public function test_webhook_ip_validation()
    {
        $mockSecurityService = Mockery::mock(PaymentSecurityService::class);
        $mockSecurityService->shouldReceive('validateWebhookSource')
            ->with('paypal', '127.0.0.1')
            ->andReturn(false);

        $this->app->instance(PaymentSecurityService::class, $mockSecurityService);

        $this->postJson(route('payment.paypal.webhook'))
            ->assertStatus(403)
            ->assertJson(['error' => 'Unauthorized']);
    }

    public function test_payment_amount_validation()
    {
        $securityService = new PaymentSecurityService();
        
        // Create subscription with plan
        $subscription = UserSubscription::factory()->create([
            'user_id' => $this->user->id,
            'plan_id' => $this->plan->id,
        ]);
        
        $this->payment->update(['user_subscription_id' => $subscription->id]);
        $this->payment->load('userSubscription.plan');

        $this->assertTrue($securityService->validatePaymentAmount($this->payment));

        // Test with wrong amount
        $this->payment->update(['amount' => 999.99]);
        $this->assertFalse($securityService->validatePaymentAmount($this->payment));
    }

    public function test_suspicious_activity_detection()
    {
        $securityService = new PaymentSecurityService();

        // Create multiple failed payments
        Payment::factory()->count(4)->create([
            'user_id' => $this->user->id,
            'status' => 'failed',
            'created_at' => now()->subMinutes(30),
        ]);

        $this->assertTrue($securityService->detectSuspiciousActivity($this->user));
    }

    public function test_payment_data_sanitization()
    {
        $securityService = new PaymentSecurityService();
        
        $dangerousPayload = json_encode([
            'event_type' => 'test',
            'script' => '<script>alert("xss")</script>',
            'data' => [
                'eval' => 'eval(malicious_code)',
                'safe_field' => 'safe_value'
            ]
        ]);

        $sanitized = $securityService->sanitizeWebhookPayload($dangerousPayload);

        $this->assertArrayNotHasKey('script', $sanitized);
        $this->assertArrayNotHasKey('eval', $sanitized['data']);
        $this->assertEquals('safe_value', $sanitized['data']['safe_field']);
    }

    public function test_sensitive_data_masking()
    {
        $securityService = new PaymentSecurityService();
        
        $sensitiveData = [
            'card_number' => '****************',
            'cvv' => '123',
            'email' => '<EMAIL>',
            'safe_field' => 'safe_value'
        ];

        $masked = $securityService->maskSensitiveData($sensitiveData);

        $this->assertEquals('41***********11', $masked['card_number']);
        $this->assertEquals('***', $masked['cvv']);
        $this->assertEquals('us***@e***.com', $masked['email']);
        $this->assertEquals('safe_value', $masked['safe_field']);
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
}
