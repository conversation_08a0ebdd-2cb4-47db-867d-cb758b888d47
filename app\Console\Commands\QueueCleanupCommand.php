<?php

namespace App\Console\Commands;

use App\Services\QueueMonitoringService;
use Illuminate\Console\Command;

class QueueCleanupCommand extends Command
{
    protected $signature = 'queue:cleanup 
                            {--days=7 : Number of days to keep failed jobs}
                            {--retry-failed : Retry all failed jobs}
                            {--force : Force cleanup without confirmation}';
    
    protected $description = 'Clean up old failed jobs and optionally retry failed jobs';

    protected $queueMonitoringService;

    public function __construct(QueueMonitoringService $queueMonitoringService)
    {
        parent::__construct();
        $this->queueMonitoringService = $queueMonitoringService;
    }

    public function handle(): int
    {
        $days = (int) $this->option('days');
        $retryFailed = $this->option('retry-failed');
        $force = $this->option('force');

        $this->info('Queue Cleanup Process');
        $this->line('===================');

        // Show current status
        $stats = $this->queueMonitoringService->getQueueStatistics();
        $this->line('Current failed jobs: ' . $stats['failed_jobs']);

        if ($retryFailed && $stats['failed_jobs'] > 0) {
            if ($force || $this->confirm('Do you want to retry all failed jobs?')) {
                $this->info('Retrying failed jobs...');
                $results = $this->queueMonitoringService->retryFailedJobs();
                
                $this->info('Retry Results:');
                $this->line('  Retried: ' . $results['retried']);
                $this->line('  Failed to retry: ' . $results['failed']);
                
                if (!empty($results['errors'])) {
                    $this->warn('Retry Errors:');
                    foreach ($results['errors'] as $error) {
                        $this->line('  Job ' . $error['job_id'] . ': ' . $error['error']);
                    }
                }
            }
        }

        // Clean up old failed jobs
        if ($force || $this->confirm("Do you want to delete failed jobs older than {$days} days?")) {
            $this->info('Cleaning up old failed jobs...');
            $deleted = $this->queueMonitoringService->clearOldFailedJobs($days);
            $this->info("Deleted {$deleted} old failed jobs.");
        }

        // Show final status
        $finalStats = $this->queueMonitoringService->getQueueStatistics();
        $this->line('');
        $this->info('Final Status:');
        $this->line('  Failed jobs: ' . $finalStats['failed_jobs']);
        $this->line('  Queue health: ' . $finalStats['queue_health']['status']);

        return 0;
    }
}
