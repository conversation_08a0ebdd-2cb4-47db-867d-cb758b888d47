<?php

namespace Tests\Unit\Models;

use App\Models\Client;
use App\Models\User;
use App\Models\Invoice;
use App\Models\Contract;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ClientTest extends TestCase
{
    use RefreshDatabase;

    public function test_client_has_fillable_attributes()
    {
        $client = new Client();
        $fillable = [
            'user_id', 'name', 'email', 'phone', 'company_name', 'address',
            'gst_number', 'contact_person', 'default_tds_percentage'
        ];

        $this->assertEquals($fillable, $client->getFillable());
    }

    public function test_client_casts_attributes_correctly()
    {
        $client = Client::factory()->create([
            'default_tds_percentage' => 10.50
        ]);

        $this->assertIsFloat($client->default_tds_percentage);
        $this->assertEquals(10.50, $client->default_tds_percentage);
    }

    public function test_client_belongs_to_user()
    {
        $user = User::factory()->create();
        $client = Client::factory()->forUser($user)->create();

        $this->assertInstanceOf(User::class, $client->user);
        $this->assertEquals($user->id, $client->user->id);
    }

    public function test_client_has_many_invoices()
    {
        $client = Client::factory()->create();
        $invoices = Invoice::factory()->count(3)->forClient($client)->create();

        $this->assertCount(3, $client->invoices);
        $this->assertInstanceOf(Invoice::class, $client->invoices->first());
    }

    public function test_client_has_many_contracts()
    {
        $client = Client::factory()->create();
        $contracts = Contract::factory()->count(2)->forClient($client)->create();

        $this->assertCount(2, $client->contracts);
        $this->assertInstanceOf(Contract::class, $client->contracts->first());
    }

    public function test_client_can_have_gst_number()
    {
        $gstNumber = '29ABCDE1234F1Z5';
        $client = Client::factory()->create(['gst_number' => $gstNumber]);

        $this->assertEquals($gstNumber, $client->gst_number);
    }

    public function test_client_can_have_null_gst_number()
    {
        $client = Client::factory()->withoutGst()->create();

        $this->assertNull($client->gst_number);
    }

    public function test_client_can_have_default_tds_percentage()
    {
        $client = Client::factory()->create(['default_tds_percentage' => 2.00]);

        $this->assertEquals(2.00, $client->default_tds_percentage);
    }

    public function test_client_can_have_zero_tds_percentage()
    {
        $client = Client::factory()->withoutTds()->create();

        $this->assertEquals(0, $client->default_tds_percentage);
    }

    public function test_client_total_invoice_amount()
    {
        $client = Client::factory()->create();
        
        Invoice::factory()->forClient($client)->create(['total_amount' => 1000.00, 'status' => 'paid']);
        Invoice::factory()->forClient($client)->create(['total_amount' => 2000.00, 'status' => 'paid']);
        Invoice::factory()->forClient($client)->create(['total_amount' => 500.00, 'status' => 'draft']); // Should not count

        $totalPaid = $client->invoices()->where('status', 'paid')->sum('total_amount');
        $this->assertEquals(3000.00, $totalPaid);
    }

    public function test_client_pending_invoice_amount()
    {
        $client = Client::factory()->create();
        
        Invoice::factory()->forClient($client)->create(['total_amount' => 1000.00, 'status' => 'sent']);
        Invoice::factory()->forClient($client)->create(['total_amount' => 1500.00, 'status' => 'overdue']);
        Invoice::factory()->forClient($client)->create(['total_amount' => 500.00, 'status' => 'paid']); // Should not count

        $totalPending = $client->invoices()->whereIn('status', ['sent', 'overdue'])->sum('total_amount');
        $this->assertEquals(2500.00, $totalPending);
    }

    public function test_client_contract_count()
    {
        $client = Client::factory()->create();
        
        Contract::factory()->count(3)->forClient($client)->create(['status' => 'signed']);
        Contract::factory()->count(2)->forClient($client)->create(['status' => 'draft']);

        $signedContracts = $client->contracts()->where('status', 'signed')->count();
        $this->assertEquals(3, $signedContracts);
        
        $totalContracts = $client->contracts()->count();
        $this->assertEquals(5, $totalContracts);
    }

    public function test_client_belongs_to_specific_user()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        $client1 = Client::factory()->forUser($user1)->create();
        $client2 = Client::factory()->forUser($user2)->create();

        $this->assertEquals($user1->id, $client1->user_id);
        $this->assertEquals($user2->id, $client2->user_id);
        $this->assertNotEquals($client1->user_id, $client2->user_id);
    }

    public function test_client_email_can_be_unique_per_user()
    {
        $user1 = User::factory()->create();
        $user2 = User::factory()->create();
        
        $email = '<EMAIL>';
        
        $client1 = Client::factory()->forUser($user1)->create(['email' => $email]);
        $client2 = Client::factory()->forUser($user2)->create(['email' => $email]);

        $this->assertEquals($email, $client1->email);
        $this->assertEquals($email, $client2->email);
        $this->assertNotEquals($client1->user_id, $client2->user_id);
    }
}
