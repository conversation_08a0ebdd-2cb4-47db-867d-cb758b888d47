<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class RateLimitMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $key = 'global'): Response
    {
        $identifier = $this->getIdentifier($request, $key);
        $maxAttempts = $this->getMaxAttempts($key);
        $decayMinutes = $this->getDecayMinutes($key);

        if (RateLimiter::tooManyAttempts($identifier, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($identifier);
            
            if ($request->expectsJson()) {
                return response()->json([
                    'error' => 'Too many requests. Please try again later.',
                    'retry_after' => $seconds
                ], 429);
            }

            return response()->view('errors.429', [
                'seconds' => $seconds
            ], 429);
        }

        RateLimiter::hit($identifier, $decayMinutes * 60);

        $response = $next($request);

        // Add rate limit headers
        $response->headers->set('X-RateLimit-Limit', $maxAttempts);
        $response->headers->set('X-RateLimit-Remaining', max(0, $maxAttempts - RateLimiter::attempts($identifier)));

        return $response;
    }

    /**
     * Get the rate limit identifier.
     */
    private function getIdentifier(Request $request, string $key): string
    {
        $ip = $request->ip();
        $user = $request->user();
        
        if ($user) {
            return "rate_limit:{$key}:user:{$user->id}";
        }
        
        return "rate_limit:{$key}:ip:{$ip}";
    }

    /**
     * Get max attempts for the given key.
     */
    private function getMaxAttempts(string $key): int
    {
        return match($key) {
            'login' => config('security.rate_limiting.login_attempts', 5),
            'api' => config('security.rate_limiting.api_per_minute', 100),
            'payment' => 10,
            'contact' => 5,
            default => config('security.rate_limiting.per_minute', 60),
        };
    }

    /**
     * Get decay minutes for the given key.
     */
    private function getDecayMinutes(string $key): int
    {
        return match($key) {
            'login' => config('security.rate_limiting.login_decay_minutes', 15),
            'payment' => 10,
            'contact' => 5,
            default => 1,
        };
    }
}
